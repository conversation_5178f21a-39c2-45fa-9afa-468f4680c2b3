/* 响应式布局优化 */

/* 全宽容器样式 */
.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

/* 在大屏幕上增加左右边距 */
@media (min-width: 1400px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1600px) {
    .container-fluid {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

@media (min-width: 1920px) {
    .container-fluid {
        padding-left: 4rem;
        padding-right: 4rem;
    }
}

/* 英雄区域优化 */
.hero-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
}

/* 确保内容在超宽屏幕上不会过度拉伸 */
@media (min-width: 1920px) {
    .hero-section .container-fluid {
        max-width: 1800px;
        margin: 0 auto;
    }
    
    .container-fluid.px-4 {
        max-width: 1800px;
        margin: 0 auto;
    }
}

/* 卡片和组件的最大宽度限制 */
.feature-card, .service-card {
    max-width: 100%;
}

/* 导航栏保持原有宽度限制 */
.navbar .container {
    max-width: 1320px;
}

/* 页脚保持原有宽度限制 */
footer .container {
    max-width: 1320px;
}

/* 响应式图片优化 */
@media (min-width: 1200px) {
    .hero-section img {
        max-width: 100%;
        height: auto;
    }
}

/* 统计数据在大屏幕上的优化 */
@media (min-width: 1200px) {
    .stats-counter {
        font-size: 2.5rem;
    }
}

/* 服务卡片在超宽屏幕上的布局优化 */
@media (min-width: 1400px) {
    .col-xl-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }
}

/* 确保按钮和交互元素在大屏幕上保持合适的大小 */
@media (min-width: 1200px) {
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1.125rem;
    }
}

/* 表格在大屏幕上的优化 */
@media (min-width: 1200px) {
    .table-responsive {
        overflow-x: visible;
    }
}

/* 心愿工坊区域的特殊优化 */
.feature-card.p-5 {
    padding: 3rem !important;
}

@media (min-width: 1400px) {
    .feature-card.p-5 {
        padding: 4rem !important;
    }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .hero-section {
        min-height: 60vh;
    }
    
    .display-3 {
        font-size: 2.5rem;
    }
}
