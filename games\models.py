from django.db import models
from django.conf import settings
from django.utils.text import slugify
from django.urls import reverse

class Game(models.Model):
    """游戏模型"""
    name = models.CharField('游戏名称', max_length=100)
    slug = models.SlugField('URL别名', max_length=100, unique=True)
    description = models.TextField('游戏描述')
    logo = models.ImageField('游戏logo', upload_to='game_logos/')
    is_active = models.BooleanField('是否激活', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '游戏'
        verbose_name_plural = '游戏'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        return reverse('games:game_detail', args=[self.slug])

class GameServer(models.Model):
    """游戏服务器模型"""
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='servers', verbose_name='所属游戏')
    name = models.CharField('服务器名称', max_length=100)
    is_active = models.BooleanField('是否激活', default=True)
    
    class Meta:
        verbose_name = '游戏服务器'
        verbose_name_plural = '游戏服务器'
        unique_together = ('game', 'name')
    
    def __str__(self):
        return f"{self.game.name} - {self.name}"

class GameAccount(models.Model):
    """游戏账号模型"""
    ACCOUNT_STATUS_CHOICES = (
        ('available', '可购买'),
        ('reserved', '已预订'),
        ('sold', '已售出'),
    )
    
    REGION_CHOICES = (
        ('雪国', '雪国'),
        ('沙城', '沙城'),
        ('希望谷', '希望谷'),
        ('新世界', '新世界'),
        ('圣托帕', '圣托帕'),
        ('其他', '其他'),
    )
    
    seller = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='game_accounts', verbose_name='卖家')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='accounts', verbose_name='游戏')
    title = models.CharField(max_length=100, verbose_name='标题')
    server = models.CharField(max_length=50, verbose_name='服务器')
    level = models.IntegerField(default=1, verbose_name='等级')
    character_class = models.CharField(max_length=50, blank=True, verbose_name='职业')
    description = models.TextField(verbose_name='描述')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格')
    image = models.ImageField(upload_to='accounts/%Y/%m/%d/', blank=True, verbose_name='账号截图')
    status = models.CharField(max_length=10, choices=ACCOUNT_STATUS_CHOICES, default='available', verbose_name='状态')
    is_featured = models.BooleanField(default=False, verbose_name='推荐账号')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '游戏账号'
        verbose_name_plural = '游戏账号'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return reverse('games:account_detail', args=[self.id])

class AccountScreenshot(models.Model):
    """账号截图模型"""
    account = models.ForeignKey(GameAccount, on_delete=models.CASCADE, related_name='screenshots', verbose_name='游戏账号')
    image = models.ImageField(upload_to='account_screenshots/%Y/%m/%d/', verbose_name='截图')
    caption = models.CharField(max_length=100, blank=True, verbose_name='说明')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '账号截图'
        verbose_name_plural = '账号截图'
    
    def __str__(self):
        return f"{self.account.title} - {self.caption or '截图'}"

class AccountReview(models.Model):
    """账号审核记录模型"""
    account = models.ForeignKey(GameAccount, on_delete=models.CASCADE, related_name='reviews', verbose_name='游戏账号')
    reviewer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='account_reviews', verbose_name='审核人')
    approved = models.BooleanField(verbose_name='是否批准')
    comments = models.TextField(blank=True, verbose_name='审核意见')
    reviewed_at = models.DateTimeField(auto_now_add=True, verbose_name='审核时间')
    
    class Meta:
        verbose_name = '账号审核记录'
        verbose_name_plural = '账号审核记录'
        ordering = ['-reviewed_at']
    
    def __str__(self):
        status = '通过' if self.approved else '拒绝'
        return f"{self.account.title} - {status} - {self.reviewer.username}" 