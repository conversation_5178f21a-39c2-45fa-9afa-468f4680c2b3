{% extends 'base.html' %}

{% block title %}提供服务 - 心愿工坊{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="bi bi-briefcase"></i> 提供服务</h4>
                </div>
                <div class="card-body">
                    {% if not user.is_authenticated %}
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle"></i> 请先<a href="{% url 'accounts:login' %}?next={{ request.path }}" class="alert-link">登录</a>后再提供服务。
                    </div>
                    {% else %}
                    <form method="post" action="{% url 'services:wishlist_provide' %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="service_type" class="form-label">服务类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="service_type" name="service_type" required>
                                <option value="" selected disabled>请选择您能提供的服务类型...</option>
                                <option value="daigan">代肝服务</option>
                                <option value="map">跑图服务</option>
                                <option value="pvp">PVP代打</option>
                                <option value="gold">金条交易</option>
                                <option value="other">其他服务</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required maxlength="100" placeholder="请简要描述您提供的服务">
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">详细描述 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required placeholder="请详细描述您能提供的服务内容、特色、优势等信息..."></textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label">服务价格</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" placeholder="您的服务价格">
                                </div>
                                <div class="form-text text-muted">留空表示价格面议</div>
                            </div>
                            <div class="col-md-6">
                                <div class="mt-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="price_negotiable" name="price_negotiable" checked>
                                        <label class="form-check-label" for="price_negotiable">
                                            价格可议
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="contact_info" class="form-label">联系方式 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="contact_info" name="contact_info" required placeholder="留下您的微信、QQ或电话等联系方式">
                            <div class="form-text text-muted">请确保联系方式正确，以便需求方与您取得联系</div>
                        </div>
                        <div class="alert alert-primary" role="alert">
                            <i class="bi bi-info-circle"></i> 平台将收取一定比例的服务费，具体金额将根据交易额计算。
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'services:wishlist_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回列表
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-plus-circle"></i> 提供服务
                            </button>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 