{% extends "base.html" %}

{% block title %}系统设置 | 梦羽明日之后{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #6a11cb, #2575fc);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .dashboard-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
    }
    
    .dashboard-header p {
        margin: 10px 0 0;
        opacity: 0.9;
    }
    
    /* 模块卡片容器 */
    .modules-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }
    
    /* 模块卡片 */
    .module-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .module-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .module-header {
        padding: 25px 25px 15px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .module-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 26px;
        color: white;
    }
    
    .icon-users {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }
    
    .icon-security {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
    }
    
    .icon-database {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
    }
    
    .icon-sitemap {
        background: linear-gradient(135deg, #f1c40f, #f39c12);
    }
    
    .icon-interface {
        background: linear-gradient(135deg, #2ecc71, #27ae60);
    }
    
    .icon-tools {
        background: linear-gradient(135deg, #fd746c, #ff9068);
    }
    
    .module-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    .module-desc {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }
    
    .module-content {
        padding: 0 25px;
        flex-grow: 1;
    }
    
    .module-features {
        list-style: none;
        padding: 15px 0;
        margin: 0;
    }
    
    .module-features li {
        padding: 8px 0;
        display: flex;
        align-items: center;
        border-bottom: 1px dashed #f0f0f0;
    }
    
    .module-features li:last-child {
        border-bottom: none;
    }
    
    .feature-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 12px;
        color: white;
    }
    
    .feature-blue {
        background-color: #3498db;
    }
    
    .feature-green {
        background-color: #2ecc71;
    }
    
    .feature-purple {
        background-color: #9b59b6;
    }
    
    .module-footer {
        padding: 20px 25px;
        border-top: 1px solid #f0f0f0;
    }
    
    /* 系统信息卡片 */
    .system-info-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .system-info-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    
    .system-info-title i {
        color: #3498db;
        margin-right: 10px;
    }
    
    .system-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 20px;
    }
    
    .info-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
    }
    
    .info-label {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 5px;
    }
    
    .info-value {
        font-weight: 600;
        font-size: 1rem;
    }
    
    /* 按钮样式 */
    .btn-module {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 6px;
        border: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.2s;
        width: 100%;
        text-align: center;
    }
    
    .btn-blue {
        background-color: #3498db;
        color: white;
    }
    
    .btn-blue:hover {
        background-color: #2980b9;
        color: white;
    }
    
    .btn-red {
        background-color: #e74c3c;
        color: white;
    }
    
    .btn-red:hover {
        background-color: #c0392b;
        color: white;
    }
    
    .btn-purple {
        background-color: #9b59b6;
        color: white;
    }
    
    .btn-purple:hover {
        background-color: #8e44ad;
        color: white;
    }
    
    .btn-yellow {
        background-color: #f39c12;
        color: white;
    }
    
    .btn-yellow:hover {
        background-color: #d35400;
        color: white;
    }
    
    .btn-green {
        background-color: #2ecc71;
        color: white;
    }
    
    .btn-green:hover {
        background-color: #27ae60;
        color: white;
    }
    
    .btn-orange {
        background-color: #fd746c;
        color: white;
    }
    
    .btn-orange:hover {
        background-color: #ff9068;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container py-4">
    <div class="dashboard-header">
        <h1>系统设置</h1>
        <p>管理系统各项功能设置、用户权限及安全设置</p>
    </div>
    
    <!-- 系统信息卡片 -->
    <div class="system-info-card">
        <div class="system-info-title">
            <i class="bi bi-info-circle-fill"></i> 系统信息
        </div>
        <div class="system-info-grid">
            <div class="info-item">
                <div class="info-label">系统版本</div>
                <div class="info-value">v2.5.3</div>
            </div>
            <div class="info-item">
                <div class="info-label">Django版本</div>
                <div class="info-value">4.2.4</div>
            </div>
            <div class="info-item">
                <div class="info-label">Python版本</div>
                <div class="info-value">3.9.7</div>
            </div>
            <div class="info-item">
                <div class="info-label">数据库</div>
                <div class="info-value">SQLite 3</div>
            </div>
            <div class="info-item">
                <div class="info-label">服务器时间</div>
                <div class="info-value">{{ now|date:"Y-m-d H:i" }}</div>
            </div>
        </div>
    </div>
    
    <!-- 模块卡片 -->
    <div class="modules-grid">
        <!-- 用户管理 -->
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon icon-users">
                    <i class="bi bi-people-fill"></i>
                </div>
                <h3 class="module-title">用户管理</h3>
                <p class="module-desc">管理平台用户账号和权限</p>
            </div>
            <div class="module-content">
                <ul class="module-features">
                    <li>
                        <div class="feature-icon feature-blue">
                            <i class="bi bi-person"></i>
                        </div>
                        <span>用户账号管理</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-green">
                            <i class="bi bi-shield"></i>
                        </div>
                        <span>用户权限设置</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-purple">
                            <i class="bi bi-people"></i>
                        </div>
                        <span>用户组管理</span>
                    </li>
                </ul>
            </div>
            <div class="module-footer">
                <a href="{% url 'services:admin_user_management' %}" class="btn-module btn-blue">进入管理</a>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon icon-security">
                    <i class="bi bi-shield-lock-fill"></i>
                </div>
                <h3 class="module-title">安全设置</h3>
                <p class="module-desc">系统安全与隐私配置</p>
            </div>
            <div class="module-content">
                <ul class="module-features">
                    <li>
                        <div class="feature-icon feature-blue">
                            <i class="bi bi-shield"></i>
                        </div>
                        <span>登录安全策略</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-green">
                            <i class="bi bi-eye"></i>
                        </div>
                        <span>隐私设置</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-purple">
                            <i class="bi bi-fingerprint"></i>
                        </div>
                        <span>验证与授权</span>
                    </li>
                </ul>
            </div>
            <div class="module-footer">
                <a href="/admin/auth/user/" class="btn-module btn-red">进入设置</a>
            </div>
        </div>
        
        <!-- 数据维护 -->
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon icon-database">
                    <i class="bi bi-database-fill"></i>
                </div>
                <h3 class="module-title">数据维护</h3>
                <p class="module-desc">数据库和缓存管理</p>
            </div>
            <div class="module-content">
                <ul class="module-features">
                    <li>
                        <div class="feature-icon feature-blue">
                            <i class="bi bi-arrow-repeat"></i>
                        </div>
                        <span>数据备份还原</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-green">
                            <i class="bi bi-lightning"></i>
                        </div>
                        <span>缓存管理</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-purple">
                            <i class="bi bi-trash"></i>
                        </div>
                        <span>数据清理</span>
                    </li>
                </ul>
            </div>
            <div class="module-footer">
                <a href="/admin/" class="btn-module btn-purple">进入管理</a>
            </div>
        </div>
        
        <!-- 内容管理 -->
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon icon-sitemap">
                    <i class="bi bi-diagram-3-fill"></i>
                </div>
                <h3 class="module-title">内容管理</h3>
                <p class="module-desc">攻略、问答与社区内容</p>
            </div>
            <div class="module-content">
                <ul class="module-features">
                    <li>
                        <div class="feature-icon feature-blue">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <span>攻略内容管理</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-green">
                            <i class="bi bi-chat-square-text"></i>
                        </div>
                        <span>问答审核</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-purple">
                            <i class="bi bi-people"></i>
                        </div>
                        <span>社区内容管理</span>
                    </li>
                </ul>
            </div>
            <div class="module-footer">
                <a href="{% url 'services:admin_questions' %}" class="btn-module btn-yellow">进入管理</a>
            </div>
        </div>
        
        <!-- 界面设置 -->
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon icon-interface">
                    <i class="bi bi-palette-fill"></i>
                </div>
                <h3 class="module-title">界面设置</h3>
                <p class="module-desc">网站界面和主题设置</p>
            </div>
            <div class="module-content">
                <ul class="module-features">
                    <li>
                        <div class="feature-icon feature-blue">
                            <i class="bi bi-palette"></i>
                        </div>
                        <span>主题设置</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-green">
                            <i class="bi bi-grid"></i>
                        </div>
                        <span>布局管理</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-purple">
                            <i class="bi bi-phone"></i>
                        </div>
                        <span>移动端优化</span>
                    </li>
                </ul>
            </div>
            <div class="module-footer">
                <a href="/admin/sites/site/" class="btn-module btn-green">进入设置</a>
            </div>
        </div>
        
        <!-- 工具箱 -->
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon icon-tools">
                    <i class="bi bi-tools"></i>
                </div>
                <h3 class="module-title">系统工具</h3>
                <p class="module-desc">日志、通知和辅助工具</p>
            </div>
            <div class="module-content">
                <ul class="module-features">
                    <li>
                        <div class="feature-icon feature-blue">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <span>系统日志</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-green">
                            <i class="bi bi-bell"></i>
                        </div>
                        <span>消息通知</span>
                    </li>
                    <li>
                        <div class="feature-icon feature-purple">
                            <i class="bi bi-gear"></i>
                        </div>
                        <span>系统诊断</span>
                    </li>
                </ul>
            </div>
            <div class="module-footer">
                <a href="/admin/admin/logentry/" class="btn-module btn-orange">进入工具箱</a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 