from django import forms
from django.contrib.auth.forms import UserCreationForm
from .models import User

class UserRegistrationForm(UserCreationForm):
    """用户注册表单"""
    email = forms.EmailField(required=True, label='电子邮箱')
    phone = forms.CharField(max_length=15, required=True, label='手机号码')
    
    class Meta:
        model = User
        fields = ('username', 'email', 'phone', 'password1', 'password2')
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError('该邮箱已被注册')
        return email
    
    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if User.objects.filter(phone=phone).exists():
            raise forms.ValidationError('该手机号已被注册')
        return phone

class UserProfileForm(forms.ModelForm):
    """用户资料表单"""
    class Meta:
        model = User
        fields = ('nickname', 'first_name', 'last_name', 'email', 'phone', 'game_id', 'avatar', 'bio')
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4}),
        }

class UserVerificationForm(forms.Form):
    """实名认证表单"""
    real_name = forms.CharField(max_length=50, label='真实姓名')
    id_number = forms.CharField(max_length=18, label='身份证号码')
    id_front = forms.ImageField(label='身份证正面照片')
    id_back = forms.ImageField(label='身份证背面照片')
    
    def clean_id_number(self):
        id_number = self.cleaned_data.get('id_number')
        # 这里可以添加身份证号码验证逻辑
        if len(id_number) != 18:
            raise forms.ValidationError('身份证号码必须为18位')
        return id_number 