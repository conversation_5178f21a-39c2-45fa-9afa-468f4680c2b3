{% extends 'base.html' %}

{% block title %}确认删除用户 | 梦羽明日之后{% endblock %}

{% block css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 50px auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    
    .warning-icon {
        color: #dc3545;
        font-size: 3rem;
        margin-bottom: 20px;
    }
    
    .delete-title {
        color: #dc3545;
        font-weight: bold;
        margin-bottom: 20px;
    }
    
    .user-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .user-info p {
        margin-bottom: 5px;
    }
    
    .user-info strong {
        color: #343a40;
    }
    
    .warning-text {
        color: #dc3545;
        font-weight: bold;
    }
    
    .btn-group {
        margin-top: 30px;
    }
    
    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    
    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
    
    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }
    
    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }
    
    .data-warning {
        background-color: #fff3cd;
        color: #856404;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border-left: 5px solid #ffc107;
    }
    
    .active-warning {
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border-left: 5px solid #dc3545;
    }
    
    .form-check {
        margin: 15px 0;
    }
    
    .active-orders-table {
        width: 100%;
        margin: 15px 0;
        border-collapse: collapse;
    }
    
    .active-orders-table th, 
    .active-orders-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .active-orders-table th {
        background-color: #f2f2f2;
        font-weight: bold;
    }
    
    .active-orders-table tr:hover {
        background-color: #f5f5f5;
    }
    
    .badge-buyer {
        background-color: #17a2b8;
    }
    
    .badge-seller {
        background-color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <div class="text-center">
        <i class="bi bi-exclamation-triangle-fill warning-icon"></i>
        <h2 class="delete-title">确认删除用户</h2>
    </div>
    
    <div class="user-info">
        <p><strong>用户名:</strong> {{ user_to_delete.username }}</p>
        <p><strong>电子邮箱:</strong> {{ user_to_delete.email }}</p>
        <p><strong>注册时间:</strong> {{ user_to_delete.date_joined|date:"Y-m-d H:i" }}</p>
        <p><strong>上次登录:</strong> {{ user_to_delete.last_login|date:"Y-m-d H:i"|default:"从未登录" }}</p>
        <p><strong>用户状态:</strong> 
            {% if user_to_delete.is_superuser %}
            <span class="badge bg-danger">超级管理员</span>
            {% elif user_to_delete.is_staff %}
            <span class="badge bg-warning">管理员</span>
            {% else %}
            <span class="badge bg-secondary">普通用户</span>
            {% endif %}
        </p>
    </div>
    
    {% if has_active_orders %}
    <div class="active-warning">
        <h5><i class="bi bi-exclamation-triangle-fill me-2"></i>活跃交易警告</h5>
        <p>该用户有 <strong>{{ active_orders_count }}</strong> 个正在进行中的交易。删除此用户将会强制终止所有交易！</p>
        
        <div class="table-responsive">
            <table class="active-orders-table">
                <thead>
                    <tr>
                        <th>订单ID</th>
                        <th>账号</th>
                        <th>状态</th>
                        <th>价格</th>
                        <th>用户角色</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in active_orders_details %}
                    <tr>
                        <td>{{ order.id }}</td>
                        <td>{{ order.account }}</td>
                        <td>{{ order.status }}</td>
                        <td>￥{{ order.price }}</td>
                        <td>
                            {% if order.is_buyer %}
                            <span class="badge badge-buyer">买家</span>
                            {% endif %}
                            {% if order.is_seller %}
                            <span class="badge badge-seller">卖家</span>
                            {% endif %}
                        </td>
                        <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <p class="mt-3 mb-0 text-danger fw-bold">强烈建议在删除用户前，先处理这些交易！</p>
    </div>
    {% endif %}
    
    {% if has_accounts or has_orders %}
    <div class="data-warning">
        <h5><i class="bi bi-exclamation-circle-fill me-2"></i>用户数据关联警告</h5>
        <p>该用户存在以下关联数据：</p>
        <ul>
            {% if has_accounts %}
            <li>用户发布的游戏账号</li>
            {% endif %}
            {% if has_orders %}
            <li>用户相关的交易订单</li>
            {% endif %}
        </ul>
        <p>删除用户将需要一并处理这些数据。</p>
    </div>
    {% endif %}
    
    <div class="alert alert-danger">
        <p class="warning-text">警告!</p>
        <p>删除用户是不可逆操作，将永久删除该用户账号及其相关数据。</p>
        <p>该操作无法撤销，请确认后再继续。</p>
    </div>
    
    <form method="post" action="{% url 'services:delete_user' user_to_delete.id %}">
        {% csrf_token %}
        
        {% if has_active_orders %}
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="force_delete_active" id="force_delete_active">
            <label class="form-check-label fw-bold text-danger" for="force_delete_active">
                我已确认并强制删除活跃交易
            </label>
        </div>
        <small class="text-muted d-block mb-3">请谨慎操作！此操作将中断所有进行中的交易，可能影响用户体验和平台信誉。</small>
        {% endif %}
        
        {% if has_accounts or has_orders %}
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="confirm_delete_data" id="confirm_delete_data">
            <label class="form-check-label fw-bold" for="confirm_delete_data">
                同时删除所有关联数据（游戏账号、订单等）
            </label>
        </div>
        <small class="text-muted">如不勾选，将无法继续删除操作。如有疑问，请先取消并检查用户关联数据。</small>
        {% endif %}
        
        <div class="d-flex justify-content-between btn-group">
            <a href="{% url 'services:admin_user_management' %}" class="btn btn-secondary">取消</a>
            <button type="submit" class="btn btn-danger">确认删除</button>
        </div>
    </form>
</div>
{% endblock %} 