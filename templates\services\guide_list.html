{% extends 'base.html' %}
{% load static %}

{% block title %}攻略专区 - 明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="text-center mb-4">游戏攻略专区</h1>
    
    <!-- 搜索和分类筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="搜索攻略..." value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">搜索</button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="category" class="form-select" onchange="this.form.submit()">
                        <option value="">所有分类</option>
                        {% for cat_code, cat_name in categories %}
                        <option value="{{ cat_code }}" {% if category == cat_code %}selected{% endif %}>{{ cat_name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 管理员操作 -->
    {% if user.is_staff %}
    <div class="d-flex justify-content-end mb-4">
        <a href="{% url 'services:create_guide' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 发布新攻略
        </a>
    </div>
    {% endif %}
    
    <!-- 精选攻略轮播 -->
    {% if featured_guides %}
    <div class="mb-5">
        <h2 class="h4 mb-3">精选攻略</h2>
        <div id="featuredGuidesCarousel" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-indicators">
                {% for guide in featured_guides %}
                <button type="button" data-bs-target="#featuredGuidesCarousel" data-bs-slide-to="{{ forloop.counter0 }}" {% if forloop.first %}class="active"{% endif %} aria-label="Slide {{ forloop.counter }}"></button>
                {% endfor %}
            </div>
            <div class="carousel-inner">
                {% for guide in featured_guides %}
                <div class="carousel-item {% if forloop.first %}active{% endif %}">
                    <div class="d-block w-100 bg-dark text-white" style="height: 300px; position: relative;">
                        {% if guide.images.first %}
                        <img src="{{ guide.images.first.image.url }}" class="img-fluid" style="width: 100%; height: 300px; object-fit: cover;" alt="{{ guide.title }}">
                        {% endif %}
                        <div class="carousel-caption" style="background-color: rgba(0,0,0,0.5); bottom: 0; left: 0; right: 0;">
                            <h5>{{ guide.title }}</h5>
                            <p>{{ guide.content|truncatechars:100 }}</p>
                            <a href="{% url 'services:guide_detail' guide_id=guide.id %}" class="btn btn-sm btn-light">查看详情</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#featuredGuidesCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">上一页</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#featuredGuidesCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">下一页</span>
            </button>
        </div>
    </div>
    {% endif %}
    
    <!-- 攻略列表 -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="h4 mb-0">攻略列表</h2>
                </div>
                <div class="col-auto">
                    {% if category %}
                        <span class="badge bg-primary">{{ category_display }}</span>
                    {% endif %}
                    {% if search_query %}
                        <span class="badge bg-secondary">搜索: {{ search_query }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if guides %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" style="width: 60%">标题</th>
                                <th scope="col">分类</th>
                                <th scope="col">发布时间</th>
                                <th scope="col">浏览量</th>
                                <th scope="col"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for guide in guides %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if guide.is_featured %}
                                        <span class="badge bg-warning text-dark me-2">精选</span>
                                        {% endif %}
                                        <a href="{% url 'services:guide_detail' guide_id=guide.id %}" class="fw-semibold text-decoration-none text-dark">
                                            {{ guide.title }}
                                        </a>
                                    </div>
                                </td>
                                <td><span class="badge bg-secondary">{{ guide.get_category_display }}</span></td>
                                <td>{{ guide.created_at|date:"Y-m-d" }}</td>
                                <td>{{ guide.view_count }}</td>
                                <td>
                                    <a href="{% url 'services:guide_detail' guide_id=guide.id %}" class="btn btn-sm btn-outline-primary">查看</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if guides.has_other_pages %}
                <nav aria-label="攻略分页" class="p-3 border-top">
                    <ul class="pagination justify-content-center mb-0">
                        {% if guides.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ guides.previous_page_number }}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="上一页">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&laquo;</span>
                        </li>
                        {% endif %}
                        
                        {% for i in guides.paginator.page_range %}
                            {% if i == guides.number %}
                            <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                            {% elif i > guides.number|add:'-3' and i < guides.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if guides.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ guides.next_page_number }}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="下一页">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&raquo;</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info m-3 mb-0">
                    暂无符合条件的攻略。{% if user.is_staff %}<a href="{% url 'services:create_guide' %}" class="alert-link">发布新攻略</a>{% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 