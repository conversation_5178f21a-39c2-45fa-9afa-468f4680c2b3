{% extends 'base.html' %}
{% load static %}

{% block title %}明日之后 - 梦羽明日之后{% endblock %}

{% block content %}
<div class="container">
    <!-- 游戏信息 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <img src="{% static 'img/lifeafter_logo.jpg' %}" alt="明日之后" class="img-fluid rounded" onerror="this.src='https://via.placeholder.com/400x300?text=明日之后'">
        </div>
        <div class="col-md-8">
            <h1 class="mb-3">明日之后</h1>
            <p class="lead">{{ game.description }}</p>
            <div class="d-flex gap-2 mb-4">
                <a href="{% url 'services:account_list' %}" class="btn btn-primary">
                    <i class="bi bi-search"></i> 浏览账号
                </a>
                {% if user.is_authenticated %}
                <a href="{% url 'services:sell_account' %}" class="btn btn-outline-primary">
                    <i class="bi bi-plus-circle"></i> 出售账号
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 区服信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">热门区服</h2>
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>排名</th>
                                    <th>区服名称</th>
                                    <th>账号数量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="popularServersTableBody">
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="text-end">
                            <small class="text-muted" id="lastUpdateTime"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐账号 -->
    {% if featured_accounts %}
    <div class="row">
        <div class="col-12">
            <h2 class="mb-3">推荐账号</h2>
        </div>
        {% for account in featured_accounts %}
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                {% if account.screenshots.exists %}
                <img src="{{ account.screenshots.first.image.url }}" class="card-img-top" alt="{{ account.title }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <img src="https://via.placeholder.com/300x200?text=无截图" class="card-img-top" alt="无截图">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ account.title }}</h5>
                    <p class="card-text text-muted">
                        <small>
                            <i class="bi bi-person-circle"></i> {{ account.character_class }} |
                            <i class="bi bi-star-fill"></i> 等级 {{ account.character_level }} |
                            <i class="bi bi-geo-alt"></i> {{ account.server_region }}
                        </small>
                    </p>
                    <p class="card-text">{{ account.description|truncatewords:20 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-primary fw-bold">¥{{ account.price }}</span>
                        <a href="{% url 'games:account_detail' account.id %}" class="btn btn-outline-primary btn-sm">查看详情</a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载热门区服数据
    loadPopularServers();
    
    // 每60秒自动刷新一次
    setInterval(loadPopularServers, 60000);
});

function loadPopularServers() {
    fetch('{% url "games:popular_servers" %}')
    .then(response => response.json())
    .then(data => {
        const tableBody = document.getElementById('popularServersTableBody');
        const lastUpdateTime = document.getElementById('lastUpdateTime');
        
        // 清空现有内容
        tableBody.innerHTML = '';
        
        if (data.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">暂无数据</td>
                </tr>
            `;
            return;
        }
        
        // 添加新数据
        data.forEach((server, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${server.server}</td>
                <td>${server.account_count}</td>
                <td>
                    <a href="{% url 'services:account_list' %}?server=${encodeURIComponent(server.server)}" 
                       class="btn btn-outline-primary btn-sm">查看账号</a>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
        // 更新最后更新时间
        const now = new Date();
        lastUpdateTime.textContent = `最后更新: ${now.toLocaleTimeString()}`;
    })
    .catch(error => {
        console.error('加载热门区服数据失败:', error);
        const tableBody = document.getElementById('popularServersTableBody');
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle"></i> 加载失败，请稍后重试
                </td>
            </tr>
        `;
    });
}
</script>
{% endblock %} 