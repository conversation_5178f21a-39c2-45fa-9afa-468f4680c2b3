{% extends "admin/base_site.html" %}

{% block title %}审核账号: {{ account.title }} | 梦羽明日之后{% endblock %}

{% block extrahead %}
<style>
    .review-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    .account-image {
        max-width: 100%;
        max-height: 400px;
        object-fit: contain;
    }
    .action-buttons {
        margin-top: 20px;
    }
    .status-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        display: inline-block;
    }
    .status-pending {
        background-color: #ffc107;
        color: #212529;
    }
</style>
{% endblock %}

{% block content %}
<div class="review-container">
    <div id="content-main">
        <h1>审核账号: {{ account.title }}</h1>
        
        <div class="module">
            <h2>账号基本信息</h2>
            <div class="form-row">
                <div>
                    <table>
                        <tr>
                            <th>ID:</th>
                            <td>{{ account.id }}</td>
                        </tr>
                        <tr>
                            <th>卖家:</th>
                            <td>{{ account.seller.get_display_name }} ({{ account.seller.email }})</td>
                        </tr>
                        <tr>
                            <th>标题:</th>
                            <td>{{ account.title }}</td>
                        </tr>
                        <tr>
                            <th>游戏:</th>
                            <td>{{ account.game.name }}</td>
                        </tr>
                        <tr>
                            <th>区服:</th>
                            <td>{{ account.server }}</td>
                        </tr>
                        <tr>
                            <th>角色等级:</th>
                            <td>{{ account.level }}</td>
                        </tr>
                        <tr>
                            <th>职业:</th>
                            <td>{{ account.character_class }}</td>
                        </tr>
                        <tr>
                            <th>价格:</th>
                            <td>¥{{ account.price }}</td>
                        </tr>
                        <tr>
                            <th>状态:</th>
                            <td><span class="status-badge status-pending">待审核</span></td>
                        </tr>
                        <tr>
                            <th>提交时间:</th>
                            <td>{{ account.created_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="module">
            <h2>账号详细描述</h2>
            <div class="form-row">
                <div style="padding: 15px; background: #f9f9f9; border-radius: 4px;">
                    {{ account.description|linebreaks }}
                </div>
            </div>
        </div>
        
        <div class="module">
            <h2>账号截图</h2>
            <div class="form-row">
                {% if account.image %}
                <div style="text-align: center; margin-bottom: 20px;">
                    <img src="{{ account.image.url }}" alt="账号截图" class="account-image">
                </div>
                {% else %}
                <p>该账号没有上传截图</p>
                {% endif %}
            </div>
        </div>
        
        <div class="action-buttons">
            <div class="submit-row">
                <a href="{% url 'admin:approve_account' account.id %}" class="button default" style="background: #28a745;">
                    批准上架
                </a>
                <a href="{% url 'admin:reject_account' account.id %}" class="button" style="background: #dc3545;">
                    拒绝
                </a>
                <a href="{% url 'admin:review_accounts' %}" class="button">
                    返回列表
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 