# Generated by Django 4.2 on 2025-03-26 08:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CommunityPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='置顶')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='查看次数')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_posts', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
            ],
            options={
                'verbose_name': '社区帖子',
                'verbose_name_plural': '社区帖子',
                'ordering': ['-is_pinned', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GameGuide',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('category', models.CharField(choices=[('beginner', '新手指南'), ('advanced', '进阶技巧'), ('boss', 'BOSS攻略'), ('map', '地图攻略'), ('weapon', '武器装备'), ('quest', '任务攻略'), ('event', '活动攻略'), ('other', '其他攻略')], default='other', max_length=20, verbose_name='攻略分类')),
                ('is_featured', models.BooleanField(default=False, verbose_name='精选攻略')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='查看次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_guides', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
            ],
            options={
                'verbose_name': '游戏攻略',
                'verbose_name_plural': '游戏攻略',
                'ordering': ['-is_featured', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GameQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='问题标题')),
                ('content', models.TextField(verbose_name='问题内容')),
                ('category', models.CharField(choices=[('gameplay', '游戏玩法'), ('technical', '技术问题'), ('account', '账号问题'), ('payment', '支付问题'), ('other', '其他问题')], default='gameplay', max_length=20, verbose_name='问题类别')),
                ('status', models.CharField(choices=[('pending', '待回复'), ('answered', '已回复'), ('closed', '已关闭')], default='pending', max_length=20, verbose_name='状态')),
                ('is_public', models.BooleanField(default=True, help_text='公开的问题会显示在常见问题列表中', verbose_name='公开问题')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='提问时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='查看次数')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_questions', to=settings.AUTH_USER_MODEL, verbose_name='提问用户')),
            ],
            options={
                'verbose_name': '游戏问题',
                'verbose_name_plural': '游戏问题',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WishlistService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_type', models.CharField(choices=[('daigan', '代肝服务'), ('map', '跑图服务'), ('pvp', 'PVP代打'), ('gold', '金条交易'), ('other', '其他服务')], max_length=20, verbose_name='服务类型')),
                ('title', models.CharField(max_length=100, verbose_name='标题')),
                ('description', models.TextField(verbose_name='描述')),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='价格')),
                ('price_negotiable', models.BooleanField(default=True, verbose_name='价格可议')),
                ('is_offering', models.BooleanField(default=True, verbose_name='提供服务')),
                ('contact_info', models.CharField(max_length=100, verbose_name='联系方式')),
                ('is_active', models.BooleanField(default=True, verbose_name='有效状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlist_services', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '心愿工坊服务',
                'verbose_name_plural': '心愿工坊服务',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TradeApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(blank=True, verbose_name='申请信息')),
                ('contact_preference', models.CharField(choices=[('wechat', '微信'), ('qq', 'QQ'), ('phone', '电话')], max_length=20, verbose_name='联系方式偏好')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('rejected', '已拒绝'), ('canceled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('admin_notes', models.TextField(blank=True, verbose_name='管理员备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='申请时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='处理时间')),
                ('applicant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trade_applications', to=settings.AUTH_USER_MODEL, verbose_name='申请人')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_applications', to=settings.AUTH_USER_MODEL, verbose_name='处理人')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='services.wishlistservice', verbose_name='服务')),
            ],
            options={
                'verbose_name': '交易申请',
                'verbose_name_plural': '交易申请',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ServiceGameAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('game_type', models.CharField(choices=[('lifeafter', '明日之后'), ('peace_elite', '和平精英'), ('honor_kings', '王者荣耀'), ('game_for_peace', '和平精英'), ('pubg_mobile', '绝地求生：刺激战场'), ('lol_mobile', '英雄联盟手游'), ('others', '其他游戏')], max_length=50, verbose_name='游戏类型')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('description', models.TextField(verbose_name='账号描述')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格')),
                ('account_level', models.CharField(choices=[('low', '初级账号'), ('medium', '中级账号'), ('high', '高级账号'), ('premium', '稀有账号')], max_length=20, verbose_name='账号等级')),
                ('device_type', models.CharField(blank=True, help_text='如Android, iOS等', max_length=50, verbose_name='设备类型')),
                ('game_roles', models.JSONField(blank=True, default=dict, null=True, verbose_name='游戏角色')),
                ('game_items', models.JSONField(blank=True, default=dict, null=True, verbose_name='游戏物品')),
                ('status', models.CharField(choices=[('pending', '待审核'), ('published', '已上架'), ('sold', '已售出'), ('rejected', '未通过审核'), ('expired', '已下架')], default='pending', max_length=20, verbose_name='状态')),
                ('is_featured', models.BooleanField(default=False, verbose_name='推荐账号')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='浏览量')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('publish_at', models.DateTimeField(blank=True, null=True, verbose_name='发布时间')),
                ('expire_at', models.DateTimeField(blank=True, null=True, verbose_name='下架时间')),
                ('review_note', models.TextField(blank=True, verbose_name='审核备注')),
                ('review_at', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('contact_type', models.CharField(default='phone', max_length=50, verbose_name='联系方式类型')),
                ('contact_value', models.CharField(max_length=100, verbose_name='联系方式')),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_accounts', to=settings.AUTH_USER_MODEL, verbose_name='审核人员')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='selling_accounts', to=settings.AUTH_USER_MODEL, verbose_name='卖家')),
            ],
            options={
                'verbose_name': '游戏账号',
                'verbose_name_plural': '游戏账号',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QuestionAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='回复内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='回复时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_staff_answer', models.BooleanField(default=False, verbose_name='官方回复')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='services.gamequestion', verbose_name='问题')),
                ('responder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_answers', to=settings.AUTH_USER_MODEL, verbose_name='回复者')),
            ],
            options={
                'verbose_name': '问题回复',
                'verbose_name_plural': '问题回复',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='PostComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='post_comments', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='services.communitypost', verbose_name='帖子')),
            ],
            options={
                'verbose_name': '帖子评论',
                'verbose_name_plural': '帖子评论',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='GuideImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='guide_images/', verbose_name='图片')),
                ('caption', models.CharField(blank=True, max_length=200, verbose_name='图片说明')),
                ('order', models.PositiveSmallIntegerField(default=0, verbose_name='排序')),
                ('guide', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='services.gameguide', verbose_name='攻略')),
            ],
            options={
                'verbose_name': '攻略图片',
                'verbose_name_plural': '攻略图片',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='GuideComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guide_comments', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
                ('guide', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='services.gameguide', verbose_name='攻略')),
            ],
            options={
                'verbose_name': '攻略评论',
                'verbose_name_plural': '攻略评论',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='AccountOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='订单号')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='交易价格')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('completed', '已完成'), ('cancelled', '已取消'), ('refunded', '已退款')], default='pending', max_length=20, verbose_name='状态')),
                ('contact_type', models.CharField(max_length=50, verbose_name='联系方式类型')),
                ('contact_value', models.CharField(max_length=100, verbose_name='联系方式')),
                ('message', models.TextField(blank=True, verbose_name='留言')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('cancelled_at', models.DateTimeField(blank=True, null=True, verbose_name='取消时间')),
                ('admin_note', models.TextField(blank=True, verbose_name='管理员备注')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='orders', to='services.servicegameaccount', verbose_name='账号')),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='buying_orders', to=settings.AUTH_USER_MODEL, verbose_name='买家')),
                ('handled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='handled_orders', to=settings.AUTH_USER_MODEL, verbose_name='处理人员')),
            ],
            options={
                'verbose_name': '账号订单',
                'verbose_name_plural': '账号订单',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AccountImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='account_images/', verbose_name='图片')),
                ('caption', models.CharField(blank=True, max_length=200, verbose_name='图片说明')),
                ('is_main', models.BooleanField(default=False, verbose_name='主图')),
                ('order', models.PositiveSmallIntegerField(default=0, verbose_name='排序')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='services.servicegameaccount', verbose_name='账号')),
            ],
            options={
                'verbose_name': '账号图片',
                'verbose_name_plural': '账号图片',
                'ordering': ['order', 'uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='AccountTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='标签名')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tags', to='services.servicegameaccount', verbose_name='账号')),
            ],
            options={
                'verbose_name': '账号标签',
                'verbose_name_plural': '账号标签',
                'unique_together': {('account', 'name')},
            },
        ),
    ]
