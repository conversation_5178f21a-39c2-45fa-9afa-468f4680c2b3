{% extends "admin/base_site.html" %}

{% block title %}拒绝账号: {{ account.title }} | 梦羽明日之后{% endblock %}

{% block content %}
<div style="max-width: 800px; margin: 0 auto;">
    <h1>拒绝账号上架</h1>
    
    <div class="module">
        <h2>账号信息</h2>
        <table>
            <tr>
                <th style="width: 150px;">ID:</th>
                <td>{{ account.id }}</td>
            </tr>
            <tr>
                <th>卖家:</th>
                <td>{{ account.seller.username }}</td>
            </tr>
            <tr>
                <th>标题:</th>
                <td>{{ account.title }}</td>
            </tr>
            <tr>
                <th>区服:</th>
                <td>{{ account.server }}</td>
            </tr>
            <tr>
                <th>价格:</th>
                <td>¥{{ account.price }}</td>
            </tr>
        </table>
    </div>
    
    <form method="post">
        {% csrf_token %}
        <div class="module">
            <h2>拒绝原因</h2>
            <div class="form-row">
                <div>
                    <label for="reason">拒绝原因 (必填):</label>
                    <textarea name="reason" id="reason" rows="4" cols="80" style="width: 100%;" placeholder="请输入拒绝原因，这将显示给卖家" required></textarea>
                    <p class="help">
                        请详细说明拒绝原因，以便卖家进行修改。例如：
                        <ul>
                            <li>信息不完整，请补充详细的账号描述</li>
                            <li>截图不清晰，无法辨认账号信息</li>
                            <li>价格明显高于市场水平，建议调整</li>
                            <li>账号描述中包含违规内容</li>
                        </ul>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="确认拒绝" class="default" style="background-color: #dc3545;">
            <a href="{% url 'admin:review_account_detail' account.id %}" class="closelink">返回</a>
        </div>
    </form>
</div>
{% endblock %} 