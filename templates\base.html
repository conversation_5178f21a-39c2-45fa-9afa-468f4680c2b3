<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}梦羽明日之后{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/responsive.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="bi bi-controller"></i> 梦羽明日之后
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'services:account_list' %}">账号交易</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'services:guide_list' %}">游戏攻略</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            特色服务
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="servicesDropdown">
                            <li><a class="dropdown-item" href="{% url 'services:account_adjustment' %}">
                                <i class="bi bi-gear-fill me-2"></i>账号调整
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'services:game_qa' %}">
                                <i class="bi bi-question-circle-fill me-2"></i>游戏答疑
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'services:live_stream' %}">
                                <i class="bi bi-broadcast me-2"></i>在线直播
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'services:wishlist_list' %}">心愿工坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'services:community' %}">社区讨论</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'about' %}">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'games:test_transaction_page' %}">测试交易</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            {% if user.avatar %}
                            <img src="{{ user.avatar.url }}" alt="{{ user.get_display_name }}" width="25" height="25" class="rounded-circle me-1">
                            {% else %}
                            <i class="bi bi-person-circle me-1"></i>
                            {% endif %}
                            {{ user.get_display_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}"><i class="bi bi-speedometer2 me-1"></i> 控制面板</a></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}"><i class="bi bi-person me-1"></i> 个人资料</a></li>
                            <li><a class="dropdown-item" href="{% url 'trades:transaction_list' %}"><i class="bi bi-bag me-1"></i> 我的交易</a></li>
                            {% if user.is_staff %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'services:admin_dashboard' %}"><i class="bi bi-speedometer me-1"></i> 管理员仪表盘</a></li>
                            <li><a class="dropdown-item" href="{% url 'services:admin_account_review' %}"><i class="bi bi-shield-check me-1"></i> 账号审核</a></li>
                            <li><a class="dropdown-item" href="{% url 'services:admin_account_orders' %}"><i class="bi bi-cart-check me-1"></i> 订单管理</a></li>
                            <li><a class="dropdown-item" href="{% url 'services:admin_system_settings' %}"><i class="bi bi-gear me-1"></i> 系统设置</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="bi bi-box-arrow-right me-1"></i> 退出登录</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:register' %}">注册</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 消息提示 -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- 主内容 -->
    <main class="container py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="py-4 bg-dark text-white mt-5">
        <div class="container text-center">
            <p class="mb-2">© 2025 梦羽明日之后 - 专业的游戏服务平台</p>
            <p class="mb-0 small">
                <a href="http://beian.miit.gov.cn/" target="_blank" class="text-light text-decoration-none" style="color: #adb5bd !important;">
                    <i class="bi bi-shield-check me-1"></i>赣ICP备2025067849号
                </a>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 