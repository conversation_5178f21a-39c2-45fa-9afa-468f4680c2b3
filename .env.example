# Django配置
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,your-server-ip

# 数据库配置（PostgreSQL推荐）
DB_ENGINE=django.db.backends.postgresql
DB_NAME=game_trade_db
DB_USER=game_trade_user
DB_PASSWORD=your-database-password
DB_HOST=localhost
DB_PORT=5432

# 如果使用SQLite（不推荐生产环境）
# DB_ENGINE=django.db.backends.sqlite3
# DB_NAME=/path/to/your/db.sqlite3

# CSRF配置
CSRF_TRUSTED_ORIGINS=https://your-domain.com,http://your-server-ip

# 邮件配置（可选）
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
