{% extends "base.html" %}

{% block title %}控制面板 - 梦羽明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- 侧边栏导航 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">个人中心</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-speedometer2 me-2"></i>控制面板
                    </a>
                    <a href="{% url 'accounts:profile' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-person me-2"></i>个人资料
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-shop me-2"></i>我的账号
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-cart me-2"></i>购买记录
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-wallet2 me-2"></i>我的钱包
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-bell me-2"></i>消息通知
                    </a>
                    {% if user.is_staff %}
                    <div class="list-group-item bg-light fw-bold text-muted">管理功能</div>
                    <a href="{% url 'admin:index' %}" class="list-group-item list-group-item-action text-danger">
                        <i class="bi bi-gear me-2"></i>管理后台
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-lg-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">我的账号</h5>
                    <a href="{% url 'services:sell_account' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-plus-circle me-1"></i>添加账号
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>账号标题</th>
                                    <th>区服</th>
                                    <th>价格</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr>
                                    <td>{{ account.id }}</td>
                                    <td>{{ account.title }}</td>
                                    <td>{{ account.server }}</td>
                                    <td>¥{{ account.price }}</td>
                                    <td>
                                        {% if account.status == 'pending' %}
                                        <span class="badge bg-warning text-dark">待审核</span>
                                        {% elif account.status == 'available' %}
                                        <span class="badge bg-success">已上架</span>
                                        {% elif account.status == 'rejected' %}
                                        <span class="badge bg-danger">审核拒绝</span>
                                        {% elif account.status == 'sold' %}
                                        <span class="badge bg-info">已售出</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ account.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ account.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-outline-primary">查看</a>
                                            {% if account.status == 'pending' or account.status == 'rejected' %}
                                            <a href="#" class="btn btn-outline-secondary">编辑</a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-inbox-fill display-6 d-block mb-2"></i>
                                            <p>您还没有发布任何账号</p>
                                            <a href="{% url 'services:sell_account' %}" class="btn btn-primary btn-sm">
                                                <i class="bi bi-plus-circle me-1"></i>立即发布
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 测试数据 - 在实际应用中移除 -->
                    <div class="small text-muted mt-3">
                        <strong>提示：</strong> 这是测试界面，实际数据将从数据库中加载。
                    </div>
                </div>
            </div>
            
            <!-- 审核流程说明 -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">审核流程说明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                <i class="bi bi-upload fs-4 text-primary"></i>
                            </div>
                            <h6>提交账号</h6>
                            <p class="small text-muted">填写完整账号信息</p>
                        </div>
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                <i class="bi bi-search fs-4 text-primary"></i>
                            </div>
                            <h6>管理员审核</h6>
                            <p class="small text-muted">1-2个工作日完成</p>
                        </div>
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                <i class="bi bi-shop fs-4 text-primary"></i>
                            </div>
                            <h6>账号上架</h6>
                            <p class="small text-muted">审核通过即可上架</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                <i class="bi bi-cash-coin fs-4 text-primary"></i>
                            </div>
                            <h6>完成交易</h6>
                            <p class="small text-muted">买家购买后完成交易</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 