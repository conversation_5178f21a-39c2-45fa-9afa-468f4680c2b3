{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}编辑个人资料 - 梦羽明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.avatar %}
                        <img src="{{ user.avatar.url }}" alt="{{ user.get_display_name }}" class="rounded-circle img-thumbnail mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <div class="bg-secondary rounded-circle d-inline-flex justify-content-center align-items-center mb-3" style="width: 120px; height: 120px;">
                            <span class="text-white fs-1">{{ user.get_display_name.0|upper }}</span>
                        </div>
                    {% endif %}
                    <h5>{{ user.get_display_name }}</h5>
                    <p class="text-muted small">加入时间：{{ user.created_at|date:"Y年m月d日" }}</p>
                </div>
            </div>
            
            <div class="list-group shadow-sm mb-4">
                <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                    <i class="bi bi-speedometer2 me-2"></i> 仪表盘
                </a>
                <a href="{% url 'accounts:profile' %}" class="list-group-item list-group-item-action">
                    <i class="bi bi-person me-2"></i> 个人资料
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                    <i class="bi bi-wallet2 me-2"></i> 我的钱包
                    <span class="badge bg-primary rounded-pill float-end">{{ user.balance }}</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                    <i class="bi bi-chat-left-text me-2"></i> 我的消息
                </a>
                <a href="{% url 'accounts:password_change' %}" class="list-group-item list-group-item-action">
                    <i class="bi bi-shield-lock me-2"></i> 修改密码
                </a>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">编辑个人资料</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.first_name|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.last_name|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.nickname|as_crispy_field }}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.email|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.phone|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.game_id|as_crispy_field }}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.avatar|as_crispy_field }}
                            {% if user.avatar %}
                                <div class="mt-2">
                                    <img src="{{ user.avatar.url }}" alt="当前头像" class="img-thumbnail" style="max-height: 100px;">
                                    <p class="form-text">当前头像</p>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.bio|as_crispy_field }}
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 