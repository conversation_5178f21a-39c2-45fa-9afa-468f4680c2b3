from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import RegexValidator

class User(AbstractUser):
    """自定义用户模型"""
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="手机号码必须为9-15位数字，可以以'+'开头"
    )
    phone = models.CharField('手机号码', validators=[phone_regex], max_length=15, unique=True)
    nickname = models.CharField('昵称', max_length=50, blank=True)
    avatar = models.ImageField('头像', upload_to='avatars/', null=True, blank=True)
    game_id = models.CharField('游戏ID', max_length=50, blank=True)
    bio = models.TextField('个人简介', blank=True)
    is_verified = models.BooleanField('已实名认证', default=False)
    balance = models.DecimalField('账户余额', max_digits=10, decimal_places=2, default=0)
    credit_score = models.PositiveIntegerField('信用评分', default=100)
    created_at = models.DateTimeField('注册时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        
    def __str__(self):
        return self.nickname if self.nickname else self.username
        
    def get_full_name(self):
        return f"{self.last_name}{self.first_name}" if self.first_name or self.last_name else self.username
        
    def get_display_name(self):
        """获取显示名称，优先显示昵称"""
        return self.nickname if self.nickname else self.username

class Notification(models.Model):
    """用户通知模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField('标题', max_length=100)
    content = models.TextField('内容')
    link = models.CharField('链接', max_length=200, blank=True)
    is_read = models.BooleanField('是否已读', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '通知'
        verbose_name_plural = '通知'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username}的通知: {self.title}" 