from django.urls import path
from . import views

app_name = 'services'

urlpatterns = [
    path('account-adjustment/', views.account_adjustment, name='account_adjustment'),
    path('game-qa/', views.game_qa, name='game_qa'),
    path('live-stream/', views.live_stream, name='live_stream'),
    
    path('community/', views.community, name='community'),
    path('community/post/create/', views.create_post, name='create_post'),
    path('community/post/<int:post_id>/', views.post_detail, name='post_detail'),
    
    path('wishlist/', views.wishlist_list, name='wishlist_list'),
    path('wishlist/service/', views.wishlist_service, name='wishlist_service'),
    path('wishlist/provide/', views.wishlist_provide, name='wishlist_provide'),
    path('wishlist/<int:service_id>/', views.wishlist_detail, name='wishlist_detail'),
    path('wishlist/my-services/', views.my_services, name='my_services'),
    
    path('wishlist/apply/<int:service_id>/', views.apply_trade, name='apply_trade'),
    path('applications/', views.my_applications, name='my_applications'),
    path('applications/cancel/<int:application_id>/', views.cancel_application, name='cancel_application'),
    
    path('questions/my/', views.my_questions, name='my_questions'),
    path('questions/ask/', views.ask_question, name='ask_question'),
    path('questions/<int:question_id>/', views.question_detail, name='question_detail'),
    path('admin/questions/', views.admin_questions, name='admin_questions'),
    
    # 攻略专区路由
    path('guides/', views.guide_list, name='guide_list'),
    path('guides/<int:guide_id>/', views.guide_detail, name='guide_detail'),
    path('guides/create/', views.create_guide, name='create_guide'),
    path('guides/edit/<int:guide_id>/', views.edit_guide, name='edit_guide'),
    path('guides/delete/<int:guide_id>/', views.delete_guide, name='delete_guide'),
    
    # 账号交易
    path('accounts/', views.account_list, name='account_list'),
    path('accounts/<int:account_id>/', views.account_detail, name='account_detail'),
    path('accounts/apply/<int:account_id>/', views.apply_for_account, name='apply_for_account'),
    path('accounts/orders/', views.my_account_orders, name='my_account_orders'),
    path('accounts/orders/cancel/<int:order_id>/', views.cancel_account_order, name='cancel_account_order'),
    path('accounts/sell/', views.sell_account, name='sell_account'),
    path('accounts/my-selling/', views.my_selling_accounts, name='my_selling_accounts'),
    path('accounts/edit/<int:account_id>/', views.edit_account, name='edit_account'),
    path('accounts/seller/orders/', views.account_orders_seller, name='account_orders_seller'),
    
    # 账号状态管理
    path('accounts/activate/<int:account_id>/', views.activate_account, name='activate_account'),
    path('accounts/deactivate/<int:account_id>/', views.deactivate_account, name='deactivate_account'),
    path('accounts/delete/<int:account_id>/', views.delete_account, name='delete_account'),
    
    # 管理员审核功能
    path('admin/accounts/review/', views.admin_account_review, name='admin_account_review'),
    path('admin/accounts/review/<int:account_id>/', views.review_account, name='review_account'),
    path('admin/order/<int:order_id>/', views.admin_view_order, name='admin_view_order'),
    path('admin/orders/', views.admin_account_orders, name='admin_account_orders'),
    path('admin/order/process/<int:order_id>/', views.admin_process_order, name='admin_process_order'),

    # 管理员功能
    path('admin/dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/orders/', views.admin_account_orders, name='admin_account_orders'),
    path('admin/order/<int:order_id>/', views.admin_view_order, name='admin_view_order'),
    path('admin/order/process/<int:order_id>/', views.admin_process_order, name='admin_process_order'),
    path('admin/account/review/', views.admin_account_review, name='admin_account_review'),
    path('admin/account/review/<int:account_id>/', views.review_account, name='admin_account_review_detail'),
    path('admin/users/', views.admin_user_management, name='admin_user_management'),
    path('admin/users/<int:user_id>/permissions/', views.edit_user_permissions, name='edit_user_permissions'),
    path('admin/users/<int:user_id>/delete/', views.delete_user, name='delete_user'),
    path('admin/settings/', views.admin_system_settings, name='admin_system_settings'),

    # API接口
    path('api/popular-servers/', views.popular_servers_api, name='popular_servers_api'),
]