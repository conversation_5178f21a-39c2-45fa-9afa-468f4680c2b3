from django import forms
from .models import Review, Dispute

class ReviewForm(forms.ModelForm):
    """评价表单"""
    class Meta:
        model = Review
        fields = ('rating', 'comment')
        widgets = {
            'comment': forms.Textarea(attrs={'rows': 4, 'placeholder': '请分享您的交易体验...'})
        }

class DisputeForm(forms.ModelForm):
    """纠纷表单"""
    class Meta:
        model = Dispute
        fields = ('reason', 'evidence')
        widgets = {
            'reason': forms.Textarea(attrs={'rows': 4, 'placeholder': '请详细描述纠纷原因...'})
        }
    
    def clean_reason(self):
        reason = self.cleaned_data.get('reason')
        if len(reason) < 10:
            raise forms.ValidationError('纠纷原因描述过短，请详细说明问题')
        return reason 