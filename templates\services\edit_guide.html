{% extends 'base.html' %}
{% load static %}

{% block title %}编辑攻略 - 攻略专区{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:guide_list' %}">攻略专区</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:guide_detail' guide_id=guide.id %}">{{ guide.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">编辑攻略</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title h4 mb-0">编辑攻略</h2>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">攻略标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required maxlength="200" value="{{ guide.title }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="form-label">攻略分类 <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">请选择攻略分类</option>
                                {% for cat_code, cat_name in categories %}
                                <option value="{{ cat_code }}" {% if guide.category == cat_code %}selected{% endif %}>{{ cat_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">攻略内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="15" required>{{ guide.content }}</textarea>
                        </div>
                        
                        <!-- 现有图片 -->
                        {% if images %}
                        <div class="mb-3">
                            <label class="form-label">现有图片</label>
                            <div class="row">
                                {% for image in images %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <span>图片 {{ image.order }}</span>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="delete_image_{{ image.id }}" name="delete_image_{{ image.id }}">
                                                <label class="form-check-label text-danger" for="delete_image_{{ image.id }}">
                                                    删除
                                                </label>
                                            </div>
                                        </div>
                                        <img src="{{ image.image.url }}" class="card-img-top" style="height: 200px; object-fit: contain;" alt="{{ image.caption }}">
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="caption_{{ image.id }}" class="form-label">图片说明</label>
                                                <input type="text" class="form-control" id="caption_{{ image.id }}" name="caption_{{ image.id }}" value="{{ image.caption }}">
                                            </div>
                                            <div class="mb-3">
                                                <label for="order_{{ image.id }}" class="form-label">排序顺序</label>
                                                <input type="number" class="form-control" id="order_{{ image.id }}" name="order_{{ image.id }}" value="{{ image.order }}" min="1">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- 添加新图片 -->
                        <div class="mb-3">
                            <label class="form-label">添加新图片</label>
                            <div class="image-upload-container">
                                <div class="mb-3 border p-3 bg-light" id="image-upload-1">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="new-image-1" class="form-label">新图片 1</label>
                                            <input type="file" class="form-control" id="new-image-1" name="new_images" accept="image/*">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new-caption-1" class="form-label">图片说明</label>
                                            <input type="text" class="form-control" id="new-caption-1" name="new_captions" placeholder="图片说明（可选）">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="add-more-images">
                                <i class="bi bi-plus-circle"></i> 添加更多图片
                            </button>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" {% if guide.is_featured %}checked{% endif %}>
                                <label class="form-check-label" for="is_featured">
                                    设为精选攻略
                                </label>
                                <div class="form-text">精选攻略将在攻略专区首页轮播展示</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'services:guide_detail' guide_id=guide.id %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let imageCount = 1;
        const maxImages = 10;
        
        document.getElementById('add-more-images').addEventListener('click', function() {
            if (imageCount >= maxImages) {
                alert('最多只能上传' + maxImages + '张新图片');
                return;
            }
            
            imageCount++;
            
            const imageUploadContainer = document.querySelector('.image-upload-container');
            const newUploadDiv = document.createElement('div');
            newUploadDiv.className = 'mb-3 border p-3 bg-light';
            newUploadDiv.id = 'image-upload-' + imageCount;
            
            newUploadDiv.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="new-image-${imageCount}" class="form-label">新图片 ${imageCount}</label>
                        <input type="file" class="form-control" id="new-image-${imageCount}" name="new_images" accept="image/*">
                    </div>
                    <div class="col-md-6">
                        <label for="new-caption-${imageCount}" class="form-label">图片说明</label>
                        <input type="text" class="form-control" id="new-caption-${imageCount}" name="new_captions" placeholder="图片说明（可选）">
                    </div>
                </div>
                <button type="button" class="btn btn-outline-danger btn-sm mt-2 remove-image">
                    <i class="bi bi-trash"></i> 移除此图片
                </button>
            `;
            
            imageUploadContainer.appendChild(newUploadDiv);
            
            // 添加移除图片的事件监听
            newUploadDiv.querySelector('.remove-image').addEventListener('click', function() {
                newUploadDiv.remove();
            });
        });
        
        // 为已有的remove-image按钮添加事件
        document.querySelectorAll('.remove-image').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.mb-3').remove();
            });
        });
    });
</script>
{% endblock %}
{% endblock %} 