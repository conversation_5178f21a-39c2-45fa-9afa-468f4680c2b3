# Generated by Django 4.2.20 on 2025-03-25 08:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('games', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='交易ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='交易金额')),
                ('platform_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='平台费用')),
                ('status', models.CharField(choices=[('pending', '待付款'), ('paid', '已付款'), ('delivering', '交付中'), ('completed', '已完成'), ('canceled', '已取消'), ('refunding', '退款中'), ('refunded', '已退款')], default='pending', max_length=20, verbose_name='交易状态')),
                ('buyer_message', models.TextField(blank=True, verbose_name='买家留言')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to=settings.AUTH_USER_MODEL, verbose_name='买家')),
                ('game_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='games.gameaccount', verbose_name='游戏账号')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to=settings.AUTH_USER_MODEL, verbose_name='卖家')),
            ],
            options={
                'verbose_name': '交易',
                'verbose_name_plural': '交易',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveSmallIntegerField(choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')], verbose_name='评分')),
                ('comment', models.TextField(verbose_name='评价内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='评价时间')),
                ('reviewee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews_received', to=settings.AUTH_USER_MODEL, verbose_name='被评价人')),
                ('reviewer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews_given', to=settings.AUTH_USER_MODEL, verbose_name='评价人')),
                ('transaction', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='review', to='trades.transaction', verbose_name='交易')),
            ],
            options={
                'verbose_name': '评价',
                'verbose_name_plural': '评价',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='支付ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='支付金额')),
                ('payment_method', models.CharField(choices=[('alipay', '支付宝'), ('wechat', '微信支付'), ('bank', '银行转账'), ('balance', '余额支付')], max_length=20, verbose_name='支付方式')),
                ('payment_status', models.BooleanField(default=False, verbose_name='支付状态')),
                ('payment_time', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('external_reference', models.CharField(blank=True, max_length=100, verbose_name='外部参考号')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='trades.transaction', verbose_name='关联交易')),
            ],
            options={
                'verbose_name': '支付',
                'verbose_name_plural': '支付',
            },
        ),
        migrations.CreateModel(
            name='Dispute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(verbose_name='纠纷原因')),
                ('evidence', models.FileField(blank=True, null=True, upload_to='dispute_evidence/', verbose_name='证据文件')),
                ('status', models.CharField(choices=[('open', '处理中'), ('closed', '已解决')], default='open', max_length=10, verbose_name='状态')),
                ('admin_notes', models.TextField(blank=True, verbose_name='管理员备注')),
                ('resolution', models.TextField(blank=True, verbose_name='解决方案')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('initiator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disputes_initiated', to=settings.AUTH_USER_MODEL, verbose_name='发起人')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disputes', to='trades.transaction', verbose_name='交易')),
            ],
            options={
                'verbose_name': '纠纷',
                'verbose_name_plural': '纠纷',
            },
        ),
    ]
