{% extends 'base.html' %}

{% block title %}我的服务/需求 - 心愿工坊{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>我的服务/需求</h1>
        <div class="btn-group">
            <a href="{% url 'services:wishlist_service' %}" class="btn btn-outline-danger">
                <i class="bi bi-plus-circle"></i> 发布需求
            </a>
            <a href="{% url 'services:wishlist_provide' %}" class="btn btn-outline-success">
                <i class="bi bi-briefcase"></i> 提供服务
            </a>
        </div>
    </div>

    <!-- 服务列表 -->
    {% if services %}
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <ul class="nav nav-tabs card-header-tabs" id="serviceTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                        全部 ({{ services.count }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="providing-tab" data-bs-toggle="tab" data-bs-target="#providing" type="button" role="tab" aria-controls="providing" aria-selected="false">
                        提供的服务
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="requesting-tab" data-bs-toggle="tab" data-bs-target="#requesting" type="button" role="tab" aria-controls="requesting" aria-selected="false">
                        发布的需求
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="serviceTabContent">
                <!-- 全部 -->
                <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>类型</th>
                                    <th>标题</th>
                                    <th>服务类型</th>
                                    <th>价格</th>
                                    <th>发布时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in services %}
                                <tr>
                                    <td>
                                        <span class="badge {% if service.is_offering %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if service.is_offering %}提供{% else %}需求{% endif %}
                                        </span>
                                    </td>
                                    <td>{{ service.title }}</td>
                                    <td>{{ service.get_service_type_display }}</td>
                                    <td>
                                        {% if service.price %}
                                        ¥{{ service.price }}
                                        {% if service.price_negotiable %}<small class="text-muted">（可议）</small>{% endif %}
                                        {% else %}
                                        <span class="text-muted">面议</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ service.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if service.is_active %}
                                        <span class="badge bg-primary">有效</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已下架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'services:wishlist_detail' service_id=service.id %}" class="btn btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-warning">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 提供的服务 -->
                <div class="tab-pane fade" id="providing" role="tabpanel" aria-labelledby="providing-tab">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>标题</th>
                                    <th>服务类型</th>
                                    <th>价格</th>
                                    <th>发布时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in services %}
                                {% if service.is_offering %}
                                <tr>
                                    <td>{{ service.title }}</td>
                                    <td>{{ service.get_service_type_display }}</td>
                                    <td>
                                        {% if service.price %}
                                        ¥{{ service.price }}
                                        {% if service.price_negotiable %}<small class="text-muted">（可议）</small>{% endif %}
                                        {% else %}
                                        <span class="text-muted">面议</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ service.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if service.is_active %}
                                        <span class="badge bg-primary">有效</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已下架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'services:wishlist_detail' service_id=service.id %}" class="btn btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-warning">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 发布的需求 -->
                <div class="tab-pane fade" id="requesting" role="tabpanel" aria-labelledby="requesting-tab">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>标题</th>
                                    <th>服务类型</th>
                                    <th>预算</th>
                                    <th>发布时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in services %}
                                {% if not service.is_offering %}
                                <tr>
                                    <td>{{ service.title }}</td>
                                    <td>{{ service.get_service_type_display }}</td>
                                    <td>
                                        {% if service.price %}
                                        ¥{{ service.price }}
                                        {% if service.price_negotiable %}<small class="text-muted">（可议）</small>{% endif %}
                                        {% else %}
                                        <span class="text-muted">面议</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ service.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        {% if service.is_active %}
                                        <span class="badge bg-primary">有效</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已下架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'services:wishlist_detail' service_id=service.id %}" class="btn btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-warning">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center py-5">
        <div class="display-1 text-muted mb-4">
            <i class="bi bi-clipboard-check"></i>
        </div>
        <h3 class="text-muted mb-3">您还没有发布过服务或需求</h3>
        <p>开始发布您的第一个服务或需求吧！</p>
        <div class="mt-3">
            <a href="{% url 'services:wishlist_provide' %}" class="btn btn-success me-2">提供服务</a>
            <a href="{% url 'services:wishlist_service' %}" class="btn btn-danger">发布需求</a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 