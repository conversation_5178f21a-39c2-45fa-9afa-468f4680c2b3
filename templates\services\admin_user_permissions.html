{% extends "base.html" %}

{% block title %}管理用户权限 - {{ user.username }} | 明日之后游戏账号交易平台{% endblock %}

{% block extra_css %}
<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .header-card {
        background: linear-gradient(135deg, #6a11cb, #2575fc);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .header-card h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
    }
    
    .header-card p {
        margin: 10px 0 0;
        opacity: 0.9;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-top: 15px;
        background: rgba(255, 255, 255, 0.2);
        padding: 15px;
        border-radius: 8px;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid white;
    }
    
    .user-details h2 {
        margin: 0;
        font-weight: 600;
        font-size: 1.5rem;
    }
    
    .user-details p {
        margin: 5px 0 0;
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .permissions-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .permission-group {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        overflow: hidden;
    }
    
    .group-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .group-header h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }
    
    .toggle-all {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 26px;
    }
    
    .toggle-all input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    
    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    
    input:checked + .toggle-slider {
        background-color: #2ecc71;
    }
    
    input:focus + .toggle-slider {
        box-shadow: 0 0 1px #2ecc71;
    }
    
    input:checked + .toggle-slider:before {
        transform: translateX(24px);
    }
    
    .permission-list {
        padding: 5px 0;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .permission-item {
        padding: 10px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .permission-item:last-child {
        border-bottom: none;
    }
    
    .permission-name {
        font-size: 0.9rem;
    }
    
    .permission-desc {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 3px;
    }
    
    .permission-switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
    }
    
    .permission-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .switch-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    
    .switch-slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    
    input:checked + .switch-slider {
        background-color: #2ecc71;
    }
    
    input:focus + .switch-slider {
        box-shadow: 0 0 1px #2ecc71;
    }
    
    input:checked + .switch-slider:before {
        transform: translateX(20px);
    }
    
    .submit-row {
        text-align: center;
        margin-top: 20px;
        margin-bottom: 40px;
    }
    
    .submit-button {
        background: linear-gradient(135deg, #6a11cb, #2575fc);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 50px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .submit-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .submit-button:active {
        transform: translateY(-1px);
    }
    
    /* 滚动条美化 */
    .permission-list::-webkit-scrollbar {
        width: 8px;
    }
    
    .permission-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    .permission-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }
    
    .permission-list::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
    }
    
    /* 保存成功提示 */
    .alert-success {
        padding: 15px 20px;
        background: linear-gradient(135deg, #43c6ac, #28a745);
        color: white;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-success i {
        font-size: 1.5rem;
    }

    /* 权限指南样式 */
    .guide-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        overflow: hidden;
    }

    .guide-header {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .guide-header h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }

    .guide-body {
        padding: 20px;
    }

    /* 权限类型彩色标记 */
    .permission-name {
        display: flex;
        align-items: center;
    }

    .permission-type {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .type-view {
        background-color: #3498db;
    }

    .type-add {
        background-color: #2ecc71;
    }

    .type-change {
        background-color: #f39c12;
    }

    .type-delete {
        background-color: #e74c3c;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:admin_dashboard' %}">管理仪表盘</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:admin_user_management' %}">用户管理</a></li>
            <li class="breadcrumb-item active" aria-current="page">编辑用户权限</li>
        </ol>
    </nav>

    {% if messages %}
    {% for message in messages %}
    <div class="alert-success">
        <i class="bi bi-check-circle-fill"></i>
        {{ message }}
    </div>
    {% endfor %}
    {% endif %}

    <div class="header-card">
        <h1>编辑用户权限</h1>
        <p>管理用户权限，分配访问和操作权限</p>
        
        <div class="user-info">
            {% if user.avatar %}
            <img src="{{ user.avatar.url }}" alt="{{ user.username }}" class="user-avatar">
            {% else %}
            <div class="user-avatar-placeholder">{{ user.username|slice:":1" }}</div>
            {% endif %}
            <div class="user-details">
                <h2>{{ user.nickname|default:user.username }}</h2>
                <p>{{ user.email }} - 注册于 {{ user.date_joined|date:"Y-m-d" }}</p>
            </div>
        </div>
    </div>

    <!-- 权限指南 -->
    <div class="guide-card mb-4">
        <div class="guide-header">
            <h3><i class="bi bi-info-circle-fill me-2"></i>权限指南</h3>
            <button class="btn btn-sm btn-outline-primary" id="toggleGuide">显示/隐藏</button>
        </div>
        <div class="guide-body" id="guideContent" style="display:none;">
            <p class="text-muted mb-2">权限系统说明：</p>
            <ul>
                <li><strong>管理员状态</strong>：只有开启管理员状态的用户才能访问后台管理功能。</li>
                <li><strong>权限分组</strong>：权限按照功能模块分组（账号管理、服务管理等）。</li>
                <li><strong>权限类型</strong>：每个功能通常有四种权限类型：
                    <ul>
                        <li><span class="badge bg-info">查看</span> - 允许用户查看相关数据</li>
                        <li><span class="badge bg-success">新增</span> - 允许用户创建新的数据记录</li>
                        <li><span class="badge bg-warning">修改</span> - 允许用户编辑已有数据</li>
                        <li><span class="badge bg-danger">删除</span> - 允许用户删除数据记录</li>
                    </ul>
                </li>
                <li><strong>整组切换</strong>：每个分组标题旁的开关可一键开启/关闭该组所有权限。</li>
                <li><strong>权限级别</strong>：请谨慎分配权限，仅授予用户工作所需的最小权限集。</li>
            </ul>
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>重要提示：</strong>某些敏感操作（如账号删除、交易管理）需要特别慎重分配权限。
            </div>
        </div>
    </div>

    <form method="post">
        {% csrf_token %}
        
        <!-- 管理员状态开关 -->
        <div class="admin-status-card mb-4 p-4 rounded shadow-sm bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="h5 mb-0">管理员状态</h3>
                    <p class="text-muted mb-0">授予用户访问管理后台的权限</p>
                </div>
                <label class="toggle-all">
                    <input type="checkbox" name="is_staff" {% if user.is_staff %}checked{% endif %}>
                    <span class="toggle-slider"></span>
                </label>
            </div>
        </div>
        
        <!-- 权限组 -->
        <div class="permissions-container">
            {% for app, model_perms in permissions_by_app.items %}
            <div class="permission-group">
                <div class="group-header">
                    <h3>{{ app|title }}</h3>
                    <label class="toggle-all">
                        <input type="checkbox" class="group-toggle" data-group="{{ app }}">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="permission-list">
                    {% for perm in model_perms %}
                    <div class="permission-item">
                        <div>
                            <div class="permission-name">{{ perm.name }}</div>
                            <div class="permission-desc">{{ perm.description }}</div>
                        </div>
                        <label class="permission-switch">
                            <input type="checkbox" name="permissions" value="{{ perm.id }}" class="perm-checkbox" data-group="{{ app }}" data-codename="{{ perm.codename }}" {% if perm.id in user_permissions %}checked{% endif %}>
                            <span class="switch-slider"></span>
                        </label>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="submit-row">
            <button type="submit" class="submit-button">保存权限设置</button>
        </div>
    </form>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 绑定权限组全选切换
    document.querySelectorAll('.group-toggle').forEach(function(toggle) {
        toggle.addEventListener('change', function() {
            const group = this.dataset.group;
            const isChecked = this.checked;
            
            document.querySelectorAll(`.perm-checkbox[data-group="${group}"]`).forEach(function(checkbox) {
                checkbox.checked = isChecked;
            });
        });
    });
    
    // 权限项变化时更新组状态
    document.querySelectorAll('.perm-checkbox').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            updateGroupToggle(this.dataset.group);
        });
    });
    
    // 初始化各组状态
    document.querySelectorAll('.group-toggle').forEach(function(toggle) {
        updateGroupToggle(toggle.dataset.group);
    });
    
    // 更新组选择状态
    function updateGroupToggle(group) {
        const checkboxes = document.querySelectorAll(`.perm-checkbox[data-group="${group}"]`);
        const toggle = document.querySelector(`.group-toggle[data-group="${group}"]`);
        
        if (!toggle) return;
        
        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        toggle.checked = checkedCount === checkboxes.length && checkboxes.length > 0;
        toggle.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
    }
    
    // 权限指南显示/隐藏功能
    document.getElementById('toggleGuide').addEventListener('click', function() {
        const guideContent = document.getElementById('guideContent');
        if (guideContent.style.display === 'none') {
            guideContent.style.display = 'block';
            this.textContent = '隐藏指南';
        } else {
            guideContent.style.display = 'none';
            this.textContent = '显示指南';
        }
    });
    
    // 添加权限类型的彩色指示器
    document.querySelectorAll('.permission-item').forEach(function(item) {
        const permNameEl = item.querySelector('.permission-name');
        if (!permNameEl) return;
        
        const codename = item.querySelector('.perm-checkbox').dataset.codename;
        if (!codename) return;
        
        const action = codename.split('_')[0];
        let typeClass = '';
        
        switch(action) {
            case 'view': typeClass = 'type-view'; break;
            case 'add': typeClass = 'type-add'; break;
            case 'change': typeClass = 'type-change'; break;
            case 'delete': typeClass = 'type-delete'; break;
        }
        
        if (typeClass) {
            const typeIndicator = document.createElement('span');
            typeIndicator.className = `permission-type ${typeClass}`;
            permNameEl.prepend(typeIndicator);
        }
    });
});
</script>
{% endblock %} 