{% extends 'base.html' %}
{% load static %}

{% block title %}问题管理 - 管理面板{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:game_qa' %}">游戏答疑</a></li>
            <li class="breadcrumb-item active" aria-current="page">问题管理</li>
        </ol>
    </nav>
    
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2">问题管理面板</h1>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'services:game_qa' %}" class="btn btn-outline-secondary">返回答疑页面</a>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">待回复问题</h5>
                    <p class="card-text display-6">{{ pending_count }}</p>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a href="?pending_only=true" class="text-white text-decoration-none">查看详情</a>
                    <i class="bi bi-arrow-right-circle text-white"></i>
                </div>
            </div>
        </div>
        <div class="col-md-9 mb-3">
            <div class="card h-100">
                <div class="card-header">筛选和搜索</div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态</label>
                            <select name="status" id="status" class="form-select" onchange="this.form.submit()">
                                <option value="">所有状态</option>
                                {% for status_code, status_name in statuses %}
                                <option value="{{ status_code }}" {% if status == status_code %}selected{% endif %}>{{ status_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">分类</label>
                            <select name="category" id="category" class="form-select" onchange="this.form.submit()">
                                <option value="">所有分类</option>
                                {% for cat_code, cat_name in categories %}
                                <option value="{{ cat_code }}" {% if category == cat_code %}selected{% endif %}>{{ cat_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">搜索</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" placeholder="搜索问题/用户..." value="{{ search_query }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="pending_only" name="pending_only" value="true" {% if pending_only %}checked{% endif %} onchange="this.form.submit()">
                                <label class="form-check-label" for="pending_only">仅显示待回复</label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 问题列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">问题列表</h5>
        </div>
        <div class="table-responsive">
            <table class="table table-hover table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>标题</th>
                        <th>用户</th>
                        <th>分类</th>
                        <th>提问时间</th>
                        <th>状态</th>
                        <th>回复数</th>
                        <th>公开</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for question in questions %}
                    <tr>
                        <td>{{ question.id }}</td>
                        <td>
                            <a href="{% url 'services:question_detail' question_id=question.id %}" class="text-decoration-none">
                                {{ question.title|truncatechars:40 }}
                            </a>
                        </td>
                        <td>{{ question.user.username }}</td>
                        <td>{{ question.get_category_display }}</td>
                        <td>{{ question.created_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            <span class="badge {% if question.status == 'pending' %}bg-warning{% elif question.status == 'answered' %}bg-success{% elif question.status == 'closed' %}bg-secondary{% endif %}">
                                {{ question.get_status_display }}
                            </span>
                        </td>
                        <td>{{ question.answer_count }}</td>
                        <td>
                            {% if question.is_public %}
                            <i class="bi bi-check-circle-fill text-success"></i>
                            {% else %}
                            <i class="bi bi-x-circle-fill text-danger"></i>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'services:question_detail' question_id=question.id %}" class="btn btn-sm btn-primary">
                                <i class="bi bi-reply-fill"></i> 回复
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center py-4">暂无符合条件的问题</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if questions.has_other_pages %}
        <div class="card-footer">
            <nav aria-label="问题列表分页">
                <ul class="pagination justify-content-center mb-0">
                    {% if questions.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ questions.previous_page_number }}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if pending_only %}&pending_only={{ pending_only }}{% endif %}" aria-label="上一页">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&laquo;</span>
                    </li>
                    {% endif %}
                    
                    {% for i in questions.paginator.page_range %}
                        {% if i == questions.number %}
                        <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                        {% elif i > questions.number|add:'-3' and i < questions.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if pending_only %}&pending_only={{ pending_only }}{% endif %}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if questions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ questions.next_page_number }}{% if status %}&status={{ status }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if pending_only %}&pending_only={{ pending_only }}{% endif %}" aria-label="下一页">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %} 