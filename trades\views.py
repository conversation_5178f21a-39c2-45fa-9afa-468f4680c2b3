from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from .models import Transaction, Payment, Review, Dispute
from games.models import GameAccount
from .forms import ReviewForm, DisputeForm
from django.core.paginator import Paginator

@login_required
def transaction_list(request):
    """用户交易列表视图"""
    # 获取用户相关的所有交易（买家或卖家）
    transactions = Transaction.objects.filter(
        Q(buyer=request.user) | Q(seller=request.user)
    ).order_by('-created_at')
    
    # 状态筛选
    status = request.GET.get('status')
    if status and status != 'all':
        transactions = transactions.filter(status=status)
    
    # 时间范围筛选
    date_range = request.GET.get('date_range')
    if date_range:
        now = timezone.now()
        if date_range == 'today':
            transactions = transactions.filter(created_at__date=now.date())
        elif date_range == 'week':
            transactions = transactions.filter(created_at__gte=now - timezone.timedelta(days=7))
        elif date_range == 'month':
            transactions = transactions.filter(created_at__gte=now - timezone.timedelta(days=30))
        elif date_range == 'year':
            transactions = transactions.filter(created_at__gte=now - timezone.timedelta(days=365))
    
    # 搜索功能
    search_query = request.GET.get('search')
    if search_query:
        transactions = transactions.filter(
            Q(transaction_id__icontains=search_query) |
            Q(game_account__title__icontains=search_query) |
            Q(seller__username__icontains=search_query) |
            Q(buyer__username__icontains=search_query)
        )
    
    # 排序
    sort = request.GET.get('sort', 'newest')
    if sort == 'newest':
        transactions = transactions.order_by('-created_at')
    elif sort == 'oldest':
        transactions = transactions.order_by('created_at')
    elif sort == 'price_high':
        transactions = transactions.order_by('-amount')
    elif sort == 'price_low':
        transactions = transactions.order_by('amount')
    
    # 分页
    paginator = Paginator(transactions, 10)  # 每页10条记录
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    # 交易状态统计
    stats = {
        'total': transactions.count(),
        'pending': transactions.filter(status='pending').count(),
        'processing': transactions.filter(status__in=['paid', 'delivering']).count(),
        'completed': transactions.filter(status='completed').count(),
        'cancelled': transactions.filter(status__in=['canceled', 'refunded']).count(),
    }
    
    context = {
        'page_obj': page_obj,
        'transactions': page_obj.object_list,
        'status': status,
        'search_query': search_query,
        'date_range': date_range,
        'sort': sort,
        'stats': stats,
    }
    
    return render(request, 'trades/transaction_list.html', context)

@login_required
def transaction_detail(request, transaction_id):
    """交易详情视图"""
    # 确保只有买卖双方能查看交易
    queryset = Transaction.objects.filter(
        Q(buyer=request.user) | Q(seller=request.user),
        transaction_id=transaction_id
    )
    transaction = get_object_or_404(queryset)
    
    context = {
        'transaction': transaction,
        'is_buyer': transaction.buyer == request.user,
    }
    return render(request, 'trades/transaction_detail.html', context)

@login_required
def create_transaction(request, account_id):
    """创建交易视图"""
    account = get_object_or_404(GameAccount, pk=account_id, status='selling')
    
    # 防止自己购买自己的账号
    if account.seller == request.user:
        messages.error(request, '您不能购买自己的游戏账号。')
        return redirect('account_detail', account_id=account_id)
    
    if request.method == 'POST':
        # 计算平台费用（示例：交易额的5%）
        platform_fee = account.price * 0.05
        
        # 创建交易记录
        transaction = Transaction.objects.create(
            buyer=request.user,
            seller=account.seller,
            game_account=account,
            amount=account.price,
            platform_fee=platform_fee,
            buyer_message=request.POST.get('message', ''),
            status='pending'
        )
        
        # 更新账号状态
        account.status = 'reserved'
        account.save()
        
        messages.success(request, '订单已创建，请完成支付。')
        return redirect('payment', transaction_id=transaction.transaction_id)
    
    return render(request, 'trades/create_transaction.html', {'account': account})

@login_required
def payment(request, transaction_id):
    """支付视图"""
    transaction = get_object_or_404(
        Transaction,
        transaction_id=transaction_id,
        buyer=request.user,
        status='pending'
    )
    
    # 防止重复支付
    if Payment.objects.filter(transaction=transaction, payment_status=True).exists():
        messages.warning(request, '该订单已支付。')
        return redirect('transaction_detail', transaction_id=transaction.transaction_id)
    
    if request.method == 'POST':
        payment_method = request.POST.get('payment_method')
        
        # 实际应用中这里应该调用支付网关API
        # 这里简化处理，直接创建支付记录
        payment = Payment.objects.create(
            transaction=transaction,
            amount=transaction.amount + transaction.platform_fee,
            payment_method=payment_method,
            payment_status=True,
            payment_time=timezone.now()
        )
        
        # 更新交易状态
        transaction.status = 'paid'
        transaction.save()
        
        messages.success(request, '支付成功！卖家将尽快处理您的订单。')
        return redirect('payment_success', transaction_id=transaction.transaction_id)
    
    context = {
        'transaction': transaction,
        'total_amount': transaction.amount + transaction.platform_fee,
    }
    return render(request, 'trades/payment.html', context)

@login_required
def payment_success(request, transaction_id):
    """支付成功视图"""
    transaction = get_object_or_404(
        Transaction,
        transaction_id=transaction_id,
        buyer=request.user
    )
    return render(request, 'trades/payment_success.html', {'transaction': transaction})

@login_required
def payment_cancel(request, transaction_id):
    """支付取消视图"""
    transaction = get_object_or_404(
        Transaction,
        transaction_id=transaction_id,
        buyer=request.user,
        status='pending'
    )
    return render(request, 'trades/payment_cancel.html', {'transaction': transaction})

@login_required
def confirm_transaction(request, transaction_id):
    """卖家确认交易视图"""
    transaction = get_object_or_404(
        Transaction,
        transaction_id=transaction_id,
        seller=request.user,
        status='paid'
    )
    
    if request.method == 'POST':
        # 更新交易状态
        transaction.status = 'delivering'
        transaction.save()
        
        messages.success(request, '您已确认交易，请尽快联系买家完成账号交接。')
        return redirect('transaction_detail', transaction_id=transaction.transaction_id)
    
    return render(request, 'trades/confirm_transaction.html', {'transaction': transaction})

@login_required
def complete_transaction(request, transaction_id):
    """买家完成交易视图"""
    transaction = get_object_or_404(
        Transaction,
        transaction_id=transaction_id,
        buyer=request.user,
        status='delivering'
    )
    
    if request.method == 'POST':
        with transaction.atomic():
            # 更新交易状态
            transaction.status = 'completed'
            transaction.completed_at = timezone.now()
            transaction.save()
            
            # 更新游戏账号状态
            game_account = transaction.game_account
            game_account.status = 'sold'
            game_account.save()
            
            # 更新卖家余额（实际应用中可能需要更复杂的结算逻辑）
            seller = transaction.seller
            seller.balance += transaction.amount
            seller.save()
        
        messages.success(request, '交易已完成！感谢您的购买。')
        return redirect('create_review', transaction_id=transaction.transaction_id)
    
    return render(request, 'trades/complete_transaction.html', {'transaction': transaction})

@login_required
def cancel_transaction(request, transaction_id):
    """取消交易视图"""
    # 允许买卖双方都可以取消未完成的交易
    queryset = Transaction.objects.filter(
        Q(buyer=request.user) | Q(seller=request.user),
        transaction_id=transaction_id,
        status__in=['pending', 'paid', 'delivering']
    )
    transaction = get_object_or_404(queryset)
    
    if request.method == 'POST':
        reason = request.POST.get('reason', '')
        
        # 如果已支付，需要退款逻辑
        if transaction.status in ['paid', 'delivering']:
            transaction.status = 'refunding'
            # 实际应用中这里应该调用退款API
            # 简化处理：
            transaction.status = 'refunded'
            
            # 如果是卖家取消，可能需要一些惩罚机制
            if request.user == transaction.seller:
                seller = transaction.seller
                seller.credit_score -= 5  # 降低信用分
                seller.save()
        else:
            transaction.status = 'canceled'
        
        transaction.save()
        
        # 恢复账号状态
        game_account = transaction.game_account
        game_account.status = 'selling'
        game_account.save()
        
        messages.success(request, '交易已取消。')
        return redirect('transaction_list')
    
    return render(request, 'trades/cancel_transaction.html', {'transaction': transaction})

@login_required
def create_review(request, transaction_id):
    """创建评价视图"""
    queryset = Transaction.objects.filter(
        Q(buyer=request.user) | Q(seller=request.user),
        transaction_id=transaction_id,
        status='completed'
    )
    transaction = get_object_or_404(queryset)
    
    # 防止重复评价
    if hasattr(transaction, 'review'):
        messages.warning(request, '您已经评价过此交易。')
        return redirect('transaction_detail', transaction_id=transaction.transaction_id)
    
    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.transaction = transaction
            review.reviewer = request.user
            review.reviewee = transaction.seller if request.user == transaction.buyer else transaction.buyer
            review.save()
            
            messages.success(request, '评价已提交，感谢您的反馈！')
            return redirect('transaction_detail', transaction_id=transaction.transaction_id)
    else:
        form = ReviewForm()
    
    context = {
        'form': form,
        'transaction': transaction,
    }
    return render(request, 'trades/create_review.html', context)

@login_required
def review_detail(request, review_id):
    """评价详情视图"""
    review = get_object_or_404(Review, pk=review_id)
    
    # 确保只有交易相关方可以查看评价
    transaction = review.transaction
    if request.user != transaction.buyer and request.user != transaction.seller:
        messages.error(request, '您无权查看此评价。')
        return redirect('home')
    
    return render(request, 'trades/review_detail.html', {'review': review})

@login_required
def create_dispute(request, transaction_id):
    """创建纠纷视图"""
    queryset = Transaction.objects.filter(
        Q(buyer=request.user) | Q(seller=request.user),
        transaction_id=transaction_id,
        status__in=['paid', 'delivering', 'completed', 'refunding']
    )
    transaction = get_object_or_404(queryset)
    
    # 防止重复提交纠纷
    if transaction.disputes.filter(status='open').exists():
        messages.warning(request, '此交易已存在未解决的纠纷。')
        return redirect('transaction_detail', transaction_id=transaction.transaction_id)
    
    if request.method == 'POST':
        form = DisputeForm(request.POST, request.FILES)
        if form.is_valid():
            dispute = form.save(commit=False)
            dispute.transaction = transaction
            dispute.initiator = request.user
            dispute.save()
            
            messages.success(request, '纠纷已提交，客服将尽快处理。')
            return redirect('dispute_detail', dispute_id=dispute.id)
    else:
        form = DisputeForm()
    
    context = {
        'form': form,
        'transaction': transaction,
    }
    return render(request, 'trades/create_dispute.html', context)

@login_required
def dispute_detail(request, dispute_id):
    """纠纷详情视图"""
    dispute = get_object_or_404(Dispute, pk=dispute_id)
    transaction = dispute.transaction
    
    # 确保只有交易相关方可以查看纠纷
    if request.user != transaction.buyer and request.user != transaction.seller:
        if not request.user.is_staff:  # 管理员可以查看
            messages.error(request, '您无权查看此纠纷。')
            return redirect('home')
    
    return render(request, 'trades/dispute_detail.html', {'dispute': dispute}) 