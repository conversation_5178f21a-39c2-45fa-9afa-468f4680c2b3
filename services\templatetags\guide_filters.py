from django import template
from django.utils.safestring import mark_safe

register = template.Library()

@register.filter
def split(value, arg):
    """将字符串按指定分隔符分割为列表"""
    return value.split(arg)

@register.filter
def startswith(value, arg):
    """检查字符串是否以指定前缀开始"""
    return value.startswith(arg)

@register.filter
def endswith(value, arg):
    """检查字符串是否以指定后缀结束"""
    return value.endswith(arg)

@register.filter
def slice(value, arg):
    """取字符串的指定部分"""
    try:
        bits = []
        for x in arg.split(':'):
            if x:
                bits.append(int(x))
            else:
                bits.append(None)
        return value[slice(*bits)]
    except (ValueError, TypeError):
        return value

@register.filter(is_safe=True)
def process_guide_content(content, images):
    """处理攻略内容，将[IMG:序号]标记替换为实际图片"""
    if not content:
        return ""
    
    result = []
    lines = content.splitlines()
    
    for line in lines:
        # 检查是否是图片标记行
        if line.strip().startswith('[IMG:') and line.strip().endswith(']'):
            # 提取图片序号
            try:
                img_id = int(line.strip()[5:-1])
                # 查找匹配的图片
                image_found = False
                for image in images:
                    if image.order == img_id:
                        # 生成图片HTML
                        img_html = f'<div class="text-center my-3">'
                        img_html += f'<img src="{image.image.url}" class="img-fluid" style="max-width: 100%;" alt="{image.caption}">'
                        if image.caption:
                            img_html += f'<div class="figure-caption text-center mt-2">{image.caption}</div>'
                        img_html += '</div>'
                        
                        result.append(img_html)
                        image_found = True
                        break
                
                # 如果没找到匹配的图片，保留原标记
                if not image_found:
                    result.append(f'<p>{line}</p>')
            except (ValueError, IndexError):
                # 如果解析失败，保留原标记
                result.append(f'<p>{line}</p>')
        else:
            # 非图片行，作为段落添加
            if line.strip():  # 只有非空行才添加段落标签
                result.append(f'<p>{line}</p>')
            else:
                # 空行添加空段落
                result.append('<p>&nbsp;</p>')
    
    return mark_safe('\n'.join(result)) 