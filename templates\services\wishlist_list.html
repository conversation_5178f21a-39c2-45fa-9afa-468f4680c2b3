{% extends 'base.html' %}

{% block title %}心愿工坊 - 梦羽明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>心愿工坊</h1>
        <div class="btn-group">
            <a href="{% url 'services:wishlist_service' %}" class="btn btn-outline-danger">
                <i class="bi bi-plus-circle"></i> 发布需求
            </a>
            <a href="{% url 'services:wishlist_provide' %}" class="btn btn-outline-danger">
                <i class="bi bi-briefcase"></i> 提供服务
            </a>
            {% if user.is_authenticated %}
            <a href="{% url 'services:my_services' %}" class="btn btn-outline-secondary">
                <i class="bi bi-person"></i> 我的服务/需求
            </a>
            {% endif %}
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="type" class="form-label">服务类型</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">全部类型</option>
                        <option value="daigan" {% if service_type == 'daigan' %}selected{% endif %}>代肝服务</option>
                        <option value="map" {% if service_type == 'map' %}selected{% endif %}>跑图服务</option>
                        <option value="pvp" {% if service_type == 'pvp' %}selected{% endif %}>PVP代打</option>
                        <option value="gold" {% if service_type == 'gold' %}selected{% endif %}>金条交易</option>
                        <option value="other" {% if service_type == 'other' %}selected{% endif %}>其他服务</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="offering" class="form-label">服务/需求</label>
                    <select name="offering" id="offering" class="form-select">
                        <option value="">全部</option>
                        <option value="true" {% if is_offering == 'true' %}selected{% endif %}>提供服务</option>
                        <option value="false" {% if is_offering == 'false' %}selected{% endif %}>寻求服务</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">关键词搜索</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="搜索标题或描述..." value="{{ search_query }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label d-block">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 服务列表 -->
    {% if services %}
    <div class="row row-cols-1 row-cols-md-3 g-4 mb-4">
        {% for service in services %}
        <div class="col">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="badge {% if service.is_offering %}bg-success{% else %}bg-danger{% endif %}">
                            {% if service.is_offering %}提供服务{% else %}寻求服务{% endif %}
                        </span>
                        <span class="badge bg-primary">{{ service.get_service_type_display }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ service.title }}</h5>
                    <p class="card-text text-muted small">
                        <i class="bi bi-person"></i> {{ service.user.username }} · 
                        <i class="bi bi-clock"></i> {{ service.created_at|date:"Y-m-d" }}
                    </p>
                    <p class="card-text">{{ service.description|truncatechars:120 }}</p>
                </div>
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if service.price %}
                            <span class="text-danger fw-bold">¥{{ service.price }}</span>
                            {% if service.price_negotiable %}<small class="text-muted">（可议价）</small>{% endif %}
                            {% else %}
                            <span class="text-muted">价格面议</span>
                            {% endif %}
                        </div>
                        <a href="{% url 'services:wishlist_detail' service_id=service.id %}" class="btn btn-sm btn-outline-primary">
                            查看详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if services.has_other_pages %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if services.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ services.previous_page_number }}{% if service_type %}&type={{ service_type }}{% endif %}{% if is_offering %}&offering={{ is_offering }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&laquo;</a>
            </li>
            {% endif %}

            {% for i in services.paginator.page_range %}
                {% if services.number == i %}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ i }}</span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if service_type %}&type={{ service_type }}{% endif %}{% if is_offering %}&offering={{ is_offering }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                </li>
                {% endif %}
            {% endfor %}

            {% if services.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ services.next_page_number }}{% if service_type %}&type={{ service_type }}{% endif %}{% if is_offering %}&offering={{ is_offering }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&raquo;</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    {% else %}
    <div class="text-center py-5">
        <div class="display-1 text-muted mb-4">
            <i class="bi bi-search"></i>
        </div>
        <h3 class="text-muted mb-3">没有找到服务</h3>
        {% if service_type or is_offering or search_query %}
            <p>没有符合当前筛选条件的服务或需求。</p>
            <a href="{% url 'services:wishlist_list' %}" class="btn btn-outline-primary">清除筛选条件</a>
        {% else %}
            <p>心愿工坊目前还没有任何服务或需求发布。</p>
            <div class="mt-3">
                <a href="{% url 'services:wishlist_provide' %}" class="btn btn-danger me-2">发布一项服务</a>
                <a href="{% url 'services:wishlist_service' %}" class="btn btn-outline-danger">发布一项需求</a>
            </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %} 