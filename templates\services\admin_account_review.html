{% extends 'base.html' %}
{% load static %}

{% block title %}账号审核管理{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item active" aria-current="page">账号审核管理</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">管理员功能</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="{% url 'services:admin_account_review' %}" class="list-group-item list-group-item-action active">
                            <i class="bi bi-shield-check me-2"></i>账号审核管理
                        </a>
                        <a href="{% url 'admin:services_servicegameaccount_changelist' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-list-ul me-2"></i>所有账号管理
                        </a>
                        <a href="{% url 'admin:services_accountorder_changelist' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-cart-check me-2"></i>订单管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- 审核统计 -->
            <div class="card mt-3">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">审核统计</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h3 class="mb-0">{{ accounts.paginator.count }}</h3>
                            <small class="text-muted">待审核账号</small>
                        </div>
                        <div class="col-6">
                            <h3 class="mb-0">{{ accounts_today }}</h3>
                            <small class="text-muted">今日新增</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">待审核账号列表</h5>
                </div>
                <div class="card-body">
                    {% if accounts %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">账号信息</th>
                                    <th scope="col">卖家</th>
                                    <th scope="col">价格</th>
                                    <th scope="col">提交时间</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr>
                                    <td style="min-width: 200px;">
                                        <div class="d-flex align-items-center">
                                            {% if account.main_image %}
                                            <img src="{{ account.main_image.url }}" alt="{{ account.title }}" class="me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                            <div class="me-3 bg-light text-center d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-image text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <strong>{{ account.title|truncatechars:30 }}</strong>
                                                <div class="small text-muted">{{ account.get_game_type_display }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ account.seller.get_display_name }}</td>
                                    <td>
                                        <span class="text-danger fw-bold">¥{{ account.price }}</span>
                                    </td>
                                    <td>{{ account.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <a href="{% url 'services:review_account' account.id %}" class="btn btn-primary btn-sm">
                                            <i class="bi bi-eye me-1"></i>审核
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if accounts.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if accounts.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ accounts.previous_page_number }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in accounts.paginator.page_range %}
                                {% if accounts.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > accounts.number|add:'-3' and i < accounts.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if accounts.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ accounts.next_page_number }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="bi bi-check-circle-fill fs-1 text-success"></i>
                        </div>
                        <h5>当前没有待审核的账号</h5>
                        <p class="text-muted">所有账号都已审核完毕</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 审核指南 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">审核指南</h5>
                </div>
                <div class="card-body">
                    <div class="small">
                        <p class="fw-bold">审核时请检查以下内容：</p>
                        <ul>
                            <li>账号信息是否完整、清晰，描述是否详细</li>
                            <li>是否有合适的截图展示账号情况</li>
                            <li>价格是否合理，不存在明显虚高或异常情况</li>
                            <li>账号内容是否符合平台规定，不含有违规内容</li>
                            <li>卖家联系方式是否有效</li>
                        </ul>
                        <p class="fw-bold">拒绝审核时，请提供明确的拒绝原因，以便卖家修改后重新提交</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 