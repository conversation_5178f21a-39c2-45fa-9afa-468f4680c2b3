# 服务器部署指南

## 1. 服务器环境准备

### 更新系统
```bash
sudo apt update && sudo apt upgrade -y
```

### 安装必要软件
```bash
# Python和相关工具
sudo apt install python3 python3-pip python3-venv python3-dev -y

# 数据库（PostgreSQL推荐）
sudo apt install postgresql postgresql-contrib -y

# Nginx
sudo apt install nginx -y

# 其他工具
sudo apt install git curl wget unzip -y
```

### 配置PostgreSQL（推荐）
```bash
# 切换到postgres用户
sudo -u postgres psql

# 在PostgreSQL中执行：
CREATE DATABASE game_trade_db;
CREATE USER game_trade_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE game_trade_db TO game_trade_user;
\q
```

## 2. 部署步骤

### 上传代码后执行：

1. **进入项目目录**
```bash
cd /root/web
```

2. **设置权限**
```bash
chmod +x deploy.sh
```

3. **创建日志目录**
```bash
mkdir -p logs
```

4. **配置环境变量**
```bash
cp .env.example .env
nano .env  # 编辑配置
```

5. **运行部署脚本**
```bash
./deploy.sh
```

6. **配置Nginx**
```bash
# 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/game_trade

# 启用站点
sudo ln -s /etc/nginx/sites-available/game_trade /etc/nginx/sites-enabled/

# 删除默认站点（可选）
sudo rm /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

7. **配置Gunicorn服务**
```bash
# 复制服务文件
sudo cp game_trade.service /etc/systemd/system/

# 重载systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable game_trade
sudo systemctl start game_trade

# 检查状态
sudo systemctl status game_trade
```

8. **配置防火墙**
```bash
# 允许HTTP和HTTPS
sudo ufw allow 'Nginx Full'
sudo ufw allow ssh
sudo ufw enable
```

## 3. 常用命令

### 查看服务状态
```bash
sudo systemctl status game_trade
sudo systemctl status nginx
```

### 查看日志
```bash
# Gunicorn日志
tail -f /root/web/logs/gunicorn_error.log
tail -f /root/web/logs/gunicorn_access.log

# Nginx日志
sudo tail -f /var/log/nginx/game_trade_error.log
sudo tail -f /var/log/nginx/game_trade_access.log

# Django日志
tail -f /root/web/django.log
```

### 重启服务
```bash
sudo systemctl restart game_trade
sudo systemctl restart nginx
```

### 更新代码
```bash
cd /root/web
source venv/bin/activate
git pull  # 如果使用Git
python manage.py collectstatic --noinput
python manage.py migrate
sudo systemctl restart game_trade
```

## 4. 故障排除

### 常见问题
1. **静态文件不显示**: 检查Nginx配置和文件权限
2. **500错误**: 查看Gunicorn错误日志
3. **数据库连接失败**: 检查.env文件中的数据库配置
4. **权限问题**: 确保文件所有者正确

### 检查清单
- [ ] .env文件配置正确
- [ ] 数据库连接正常
- [ ] 静态文件收集完成
- [ ] Nginx配置正确
- [ ] Gunicorn服务运行正常
- [ ] 防火墙规则配置
- [ ] 域名解析（如果使用域名）
