from django.db import models
from django.conf import settings
from games.models import GameAccount
import uuid

class Transaction(models.Model):
    """交易模型"""
    STATUS_CHOICES = (
        ('pending', '待付款'),
        ('paid', '已付款'),
        ('delivering', '交付中'),
        ('completed', '已完成'),
        ('canceled', '已取消'),
        ('refunding', '退款中'),
        ('refunded', '已退款'),
    )
    
    transaction_id = models.UUIDField('交易ID', default=uuid.uuid4, editable=False, unique=True)
    buyer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='purchases', verbose_name='买家')
    seller = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sales', verbose_name='卖家')
    game_account = models.ForeignKey(GameAccount, on_delete=models.CASCADE, related_name='transactions', verbose_name='游戏账号')
    amount = models.DecimalField('交易金额', max_digits=10, decimal_places=2)
    platform_fee = models.DecimalField('平台费用', max_digits=10, decimal_places=2)
    status = models.CharField('交易状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    buyer_message = models.TextField('买家留言', blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    class Meta:
        verbose_name = '交易'
        verbose_name_plural = '交易'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"交易 {self.transaction_id}"
    
    def get_total_amount(self):
        return self.amount + self.platform_fee

class Payment(models.Model):
    """支付记录"""
    PAYMENT_METHOD_CHOICES = (
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('bank', '银行转账'),
        ('balance', '余额支付'),
    )
    
    payment_id = models.UUIDField('支付ID', default=uuid.uuid4, editable=False, unique=True)
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='payments', verbose_name='关联交易')
    amount = models.DecimalField('支付金额', max_digits=10, decimal_places=2)
    payment_method = models.CharField('支付方式', max_length=20, choices=PAYMENT_METHOD_CHOICES)
    payment_status = models.BooleanField('支付状态', default=False)
    payment_time = models.DateTimeField('支付时间', null=True, blank=True)
    external_reference = models.CharField('外部参考号', max_length=100, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '支付'
        verbose_name_plural = '支付'
    
    def __str__(self):
        return f"支付 {self.payment_id}"

class Review(models.Model):
    """评价模型"""
    RATING_CHOICES = (
        (1, '1星'),
        (2, '2星'),
        (3, '3星'),
        (4, '4星'),
        (5, '5星'),
    )
    
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='review', verbose_name='交易')
    reviewer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reviews_given', verbose_name='评价人')
    reviewee = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reviews_received', verbose_name='被评价人')
    rating = models.PositiveSmallIntegerField('评分', choices=RATING_CHOICES)
    comment = models.TextField('评价内容')
    created_at = models.DateTimeField('评价时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '评价'
        verbose_name_plural = '评价'
    
    def __str__(self):
        return f"{self.reviewer.username} 对 {self.reviewee.username} 的评价"

class Dispute(models.Model):
    """纠纷模型"""
    STATUS_CHOICES = (
        ('open', '处理中'),
        ('closed', '已解决'),
    )
    
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='disputes', verbose_name='交易')
    initiator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='disputes_initiated', verbose_name='发起人')
    reason = models.TextField('纠纷原因')
    evidence = models.FileField('证据文件', upload_to='dispute_evidence/', blank=True, null=True)
    status = models.CharField('状态', max_length=10, choices=STATUS_CHOICES, default='open')
    admin_notes = models.TextField('管理员备注', blank=True)
    resolution = models.TextField('解决方案', blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    resolved_at = models.DateTimeField('解决时间', null=True, blank=True)
    
    class Meta:
        verbose_name = '纠纷'
        verbose_name_plural = '纠纷'
    
    def __str__(self):
        return f"纠纷 #{self.id} - {self.transaction}" 