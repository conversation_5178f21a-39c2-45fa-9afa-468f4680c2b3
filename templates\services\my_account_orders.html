{% extends 'base.html' %}
{% load static %}

{% block title %}我的账号购买申请{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item active" aria-current="page">我的购买申请</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">账号交易</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="{% url 'services:account_list' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-search me-2"></i>浏览账号
                        </a>
                        <a href="{% url 'services:sell_account' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle me-2"></i>出售我的账号
                        </a>
                        <a href="{% url 'services:my_selling_accounts' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-list-ul me-2"></i>我出售的账号
                        </a>
                        <a href="{% url 'services:my_account_orders' %}" class="list-group-item list-group-item-action active">
                            <i class="bi bi-bag me-2"></i>我的购买申请
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">我的购买申请</h5>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>账号</th>
                                    <th>价格</th>
                                    <th>状态</th>
                                    <th>申请时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>{{ order.order_number }}</td>
                                    <td>
                                        <a href="{% url 'services:account_detail' account_id=order.account.id %}" class="text-decoration-none">
                                            {{ order.account.title|truncatechars:20 }}
                                        </a>
                                    </td>
                                    <td class="text-danger">¥ {{ order.price }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                        <span class="badge bg-warning text-dark">待处理</span>
                                        {% elif order.status == 'processing' %}
                                        <span class="badge bg-info">处理中</span>
                                        {% elif order.status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-secondary">已取消</span>
                                        {% elif order.status == 'refunded' %}
                                        <span class="badge bg-danger">已退款</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'services:account_detail' account_id=order.account.id %}" class="btn btn-sm btn-outline-primary">查看</a>
                                            {% if order.status == 'pending' or order.status == 'processing' %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelOrderModal" data-order-id="{{ order.id }}">取消</button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if orders.has_other_pages %}
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if orders.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ orders.previous_page_number }}">上一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">上一页</span>
                            </li>
                            {% endif %}
                            
                            {% for num in orders.paginator.page_range %}
                                {% if orders.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > orders.number|add:'-3' and num < orders.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if orders.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ orders.next_page_number }}">下一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">下一页</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>您还没有提交过账号购买申请
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'services:account_list' %}" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>浏览可购买的账号
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 交易流程说明 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">交易流程说明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="bi bi-file-earmark-text fs-1"></i>
                            </div>
                            <h6 class="mt-2">提交申请</h6>
                            <small class="text-muted">您提交购买申请</small>
                        </div>
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="bi bi-check2-circle fs-1"></i>
                            </div>
                            <h6 class="mt-2">平台审核</h6>
                            <small class="text-muted">平台审核您的申请</small>
                        </div>
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="bi bi-credit-card fs-1"></i>
                            </div>
                            <h6 class="mt-2">支付款项</h6>
                            <small class="text-muted">确认后支付款项</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="bi bi-box-seam fs-1"></i>
                            </div>
                            <h6 class="mt-2">交易完成</h6>
                            <small class="text-muted">获得账号所有权</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 取消订单确认框 -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1" aria-labelledby="cancelOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelOrderModalLabel">确认取消</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要取消这个购买申请吗？</p>
                <p class="text-danger"><small>注意：取消后需要重新提交申请才能购买此账号</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a href="#" id="confirmCancelBtn" class="btn btn-danger">确认取消</a>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置取消订单的确认按钮链接
    var cancelOrderModal = document.getElementById('cancelOrderModal');
    if (cancelOrderModal) {
        cancelOrderModal.addEventListener('show.bs.modal', function(event) {
            var button = event.relatedTarget;
            var orderId = button.getAttribute('data-order-id');
            var confirmCancelBtn = document.getElementById('confirmCancelBtn');
            confirmCancelBtn.href = "{% url 'services:cancel_account_order' order_id=0 %}".replace('0', orderId);
        });
    }
});
</script>
{% endblock %}
{% endblock %} 