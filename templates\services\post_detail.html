{% extends 'base.html' %}

{% block title %}{{ post.title }} - 玩家社区{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-12">
            <!-- 帖子内容 -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ post.title }}</h3>
                            <small class="text-muted">
                                <i class="bi bi-person"></i> {{ post.author.username }} · 
                                <i class="bi bi-clock"></i> {{ post.created_at|date:"Y-m-d H:i" }} · 
                                <i class="bi bi-eye"></i> {{ post.view_count }} 浏览 · 
                                <i class="bi bi-chat"></i> {{ comments|length }} 评论
                            </small>
                        </div>
                        <a href="{% url 'services:community' %}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="post-content mb-4">
                        {{ post.content|linebreaks }}
                    </div>
                    
                    <div class="border-top pt-3 mt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">
                                {% if post.updated_at > post.created_at %}
                                最后编辑于: {{ post.updated_at|date:"Y-m-d H:i" }}
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 评论区 -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-chat-dots"></i> 评论 ({{ comments|length }})
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 发表评论 -->
                    {% if user.is_authenticated %}
                    <form method="post" action="{% url 'services:post_detail' post_id=post.id %}" class="mb-4">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="comment" class="form-label">发表评论</label>
                            <textarea class="form-control" id="comment" name="comment" rows="3" required placeholder="请输入您的评论..."></textarea>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send"></i> 提交评论
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-info mb-4" role="alert">
                        <i class="bi bi-info-circle"></i> 请<a href="{% url 'accounts:login' %}?next={{ request.path }}" class="alert-link">登录</a>后发表评论。
                    </div>
                    {% endif %}
                    
                    <!-- 评论列表 -->
                    {% if comments %}
                    <div class="comments-list">
                        {% for comment in comments %}
                        <div class="d-flex mb-4{% if not forloop.last %} border-bottom pb-3{% endif %}">
                            <div class="flex-shrink-0">
                                {% if comment.author.avatar %}
                                <img src="{{ comment.author.avatar.url }}" class="rounded-circle" width="50" height="50" alt="{{ comment.author.username }}">
                                {% else %}
                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    {{ comment.author.username|first|upper }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex align-items-center mb-1">
                                    <h6 class="fw-bold mb-0">{{ comment.author.username }}</h6>
                                    <small class="text-muted ms-2">{{ comment.created_at|date:"Y-m-d H:i" }}</small>
                                </div>
                                <div>{{ comment.content|linebreaks }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="display-1 text-muted mb-4">
                            <i class="bi bi-chat"></i>
                        </div>
                        <p class="lead">暂无评论，来发表第一条评论吧！</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 