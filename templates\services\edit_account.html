{% extends 'base.html' %}
{% load static %}

{% block title %}编辑账号 - {{ account.title }}{% endblock %}

{% block extra_css %}
<style>
.image-preview {
    max-width: 100%;
    max-height: 150px;
    margin-top: 10px;
}
.preview-container {
    margin-bottom: 15px;
    text-align: center;
}
.tag-input-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}
.tag-item {
    background-color: #e9ecef;
    border-radius: 3px;
    padding: 2px 8px;
    display: flex;
    align-items: center;
}
.tag-item .remove-tag {
    margin-left: 5px;
    cursor: pointer;
    color: #dc3545;
}
.screenshot-section {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}
.screenshot-section h6 {
    margin-bottom: 15px;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 10px;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:my_selling_accounts' %}">我出售的账号</a></li>
            <li class="breadcrumb-item active" aria-current="page">编辑账号</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">账号交易</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="{% url 'services:account_list' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-search me-2"></i>浏览账号
                        </a>
                        <a href="{% url 'services:sell_account' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle me-2"></i>出售我的账号
                        </a>
                        <a href="{% url 'services:my_selling_accounts' %}" class="list-group-item list-group-item-action active">
                            <i class="bi bi-list-ul me-2"></i>我出售的账号
                        </a>
                        <a href="{% url 'services:my_account_orders' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-bag me-2"></i>我的购买申请
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 账号状态信息 -->
            <div class="card mt-3">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">账号状态</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">状态:</span>
                        <span class="badge {% if account.status == 'active' %}bg-success{% elif account.status == 'pending' %}bg-warning{% elif account.status == 'inactive' %}bg-secondary{% elif account.status == 'rejected' %}bg-danger{% else %}bg-info{% endif %}">
                            {{ account.get_status_display }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">创建时间:</span>
                        <span>{{ account.created_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    {% if account.status == 'rejected' %}
                    <div class="alert alert-danger mt-3 mb-0">
                        <p class="fw-bold mb-1">拒绝原因:</p>
                        <p class="mb-0">{{ account.rejection_reason }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 表单内容 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">编辑账号 - {{ account.title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="editAccountForm">
                        {% csrf_token %}
                        
                        <!-- 基本信息 -->
                        <h5 class="mb-3">基本信息</h5>
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="title" class="form-label">账号标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required maxlength="200" value="{{ account.title }}">
                            </div>
                            <div class="col-md-4">
                                <label for="price" class="form-label">价格(¥) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" required min="1" step="0.01" value="{{ account.price }}">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="account_level" class="form-label">庄园等级 <span class="text-danger">*</span></label>
                                <select class="form-select" id="account_level" name="account_level" required>
                                    <option value="">选择庄园等级</option>
                                    {% for code, name in account_level_choices %}
                                    <option value="{{ code }}" {% if account.account_level == code %}selected{% endif %}>{{ name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="profession" class="form-label">职业 <span class="text-danger">*</span></label>
                                <select class="form-select" id="profession" name="profession" required>
                                    <option value="">选择职业</option>
                                    {% for code, name in profession_choices %}
                                    <option value="{{ code }}" {% if account.profession == code %}selected{% endif %}>{{ name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="server" class="form-label">区服 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="server" name="server" placeholder="例如：秋日森林" value="{{ account.server }}" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="device_type" class="form-label">设备类型 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="device_type" name="device_type" placeholder="例如：Android, iOS" value="{{ account.device_type }}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">账号描述 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="6" required>{{ account.description }}</textarea>
                        </div>
                        
                        <!-- 标签 -->
                        <div class="mb-3">
                            <label for="tags-input" class="form-label">标签</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="tags-input" placeholder="输入标签后按回车添加">
                                <button class="btn btn-outline-secondary" type="button" id="add-tag-btn">添加</button>
                            </div>
                            <div class="form-text">添加标签可以提高账号的曝光度，最多添加5个标签</div>
                            
                            <div class="tag-input-container" id="tags-container"></div>
                            <input type="hidden" name="tags" id="tags-hidden" value="{{ tags }}">
                        </div>
                        
                        <!-- 现有图片管理 -->
                        <h5 class="mt-4 mb-3">现有图片</h5>
                        <div id="existing-images" class="row g-3">
                            {% for image in images %}
                            <div class="col-md-4 existing-image-item" data-image-id="{{ image.id }}">
                                <div class="card">
                                    <div class="preview-container">
                                        <img src="{{ image.image.url }}" class="image-preview card-img-top">
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input existing-main-image" type="radio" name="main_existing" id="existing_main_{{ image.id }}" value="{{ image.id }}" {% if image.is_main %}checked{% endif %}>
                                            <label class="form-check-label" for="existing_main_{{ image.id }}">
                                                设为主图
                                            </label>
                                        </div>
                                        <div class="mb-2">
                                            <select class="form-select form-select-sm" name="section_{{ image.id }}">
                                                <option value="" {% if not image.section %}selected{% endif %}>选择分类</option>
                                                <option value="profile" {% if image.section == 'profile' %}selected{% endif %}>人物主页</option>
                                                <option value="stats" {% if image.section == 'stats' %}selected{% endif %}>属性面板</option>
                                                <option value="accessory" {% if image.section == 'accessory' %}selected{% endif %}>配件</option>
                                                <option value="core" {% if image.section == 'core' %}selected{% endif %}>核心</option>
                                                <option value="recipe" {% if image.section == 'recipe' %}selected{% endif %}>配方</option>
                                                <option value="potential" {% if image.section == 'potential' %}selected{% endif %}>潜能</option>
                                                <option value="drone" {% if image.section == 'drone' %}selected{% endif %}>无人机</option>
                                                <option value="college" {% if image.section == 'college' %}selected{% endif %}>高校段位</option>
                                                <option value="fashion" {% if image.section == 'fashion' %}selected{% endif %}>时装载具</option>
                                                <option value="other" {% if image.section == 'other' %}selected{% endif %}>其他</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <input type="text" class="form-control form-control-sm" name="caption_{{ image.id }}" placeholder="图片说明" value="{{ image.caption }}">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input delete-image-checkbox" type="checkbox" id="delete_{{ image.id }}" name="deleted_images" value="{{ image.id }}">
                                            <label class="form-check-label text-danger" for="delete_{{ image.id }}">
                                                删除此图片
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="col-12">
                                <div class="alert alert-info">
                                    您还没有上传任何图片，请在下方添加账号截图。
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- 上传新图片 -->
                        <h5 class="mt-4 mb-3">添加新截图</h5>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            请上传清晰的游戏账号截图，完整的截图能提高账号的售出几率。每个分类可上传多张图片，所有类别都不是必填项。
                        </div>
                        
                        <!-- 人物主页截图 -->
                        <div class="screenshot-section" id="profile-section">
                            <h6>人物主页（设置）</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="profile-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="profile">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="profile-preview"></div>
                        </div>
                        
                        <!-- 属性面板截图 -->
                        <div class="screenshot-section" id="stats-section">
                            <h6>属性面板</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="stats-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="stats">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="stats-preview"></div>
                        </div>
                        
                        <!-- 配件截图 -->
                        <div class="screenshot-section" id="accessory-section">
                            <h6>配件</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="accessory-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="accessory">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="accessory-preview"></div>
                        </div>
                        
                        <!-- 核心截图 -->
                        <div class="screenshot-section" id="core-section">
                            <h6>核心</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="core-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="core">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="core-preview"></div>
                        </div>
                        
                        <!-- 配方截图 -->
                        <div class="screenshot-section" id="recipe-section">
                            <h6>配方</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="recipe-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="recipe">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="recipe-preview"></div>
                        </div>
                        
                        <!-- 潜能截图 -->
                        <div class="screenshot-section" id="potential-section">
                            <h6>潜能</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="potential-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="potential">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="potential-preview"></div>
                        </div>
                        
                        <!-- 无人机截图 -->
                        <div class="screenshot-section" id="drone-section">
                            <h6>无人机</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="drone-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="drone">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="drone-preview"></div>
                        </div>
                        
                        <!-- 高校段位截图 -->
                        <div class="screenshot-section" id="college-section">
                            <h6>高校段位</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="college-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="college">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="college-preview"></div>
                        </div>
                        
                        <!-- 时装载具截图 -->
                        <div class="screenshot-section" id="fashion-section">
                            <h6>时装载具</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="fashion-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="fashion">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="fashion-preview"></div>
                        </div>
                        
                        <!-- 其他截图 -->
                        <div class="screenshot-section" id="other-section">
                            <h6>其他</h6>
                            <div class="mb-2">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="other-upload" accept="image/*" multiple>
                                    <button class="btn btn-outline-secondary" type="button" data-section="other">上传截图</button>
                                </div>
                            </div>
                            <div class="row g-3 preview-container" id="other-preview"></div>
                        </div>
                        
                        <!-- 联系方式 -->
                        <h5 class="mt-4 mb-3">联系方式 (仅管理员可见)</h5>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="contact_type" class="form-label">联系方式类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="contact_type" name="contact_type" required>
                                    <option value="">选择联系方式</option>
                                    <option value="phone" {% if account.contact_type == 'phone' %}selected{% endif %}>手机号码</option>
                                    <option value="wechat" {% if account.contact_type == 'wechat' %}selected{% endif %}>微信</option>
                                    <option value="qq" {% if account.contact_type == 'qq' %}selected{% endif %}>QQ</option>
                                </select>
                            </div>
                            <div class="col-md-8">
                                <label for="contact_value" class="form-label">联系方式 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_value" name="contact_value" required value="{{ account.contact_value }}">
                                <div class="form-text">联系方式仅管理员可见，用于交易过程中联系您</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'services:my_selling_accounts' %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签管理
    const tagsInput = document.getElementById('tags-input');
    const addTagBtn = document.getElementById('add-tag-btn');
    const tagsContainer = document.getElementById('tags-container');
    const tagsHidden = document.getElementById('tags-hidden');
    let tags = [];
    
    // 初始化标签
    if (tagsHidden.value) {
        tags = tagsHidden.value.split(',').map(tag => tag.trim());
        updateTags();
    }
    
    function updateTags() {
        // 更新hidden input
        tagsHidden.value = tags.join(',');
        
        // 更新UI
        tagsContainer.innerHTML = '';
        tags.forEach(tag => {
            const tagItem = document.createElement('div');
            tagItem.className = 'tag-item';
            tagItem.innerHTML = `
                ${tag} <span class="remove-tag" data-tag="${tag}"><i class="bi bi-x"></i></span>
            `;
            tagsContainer.appendChild(tagItem);
            
            // 添加删除事件
            tagItem.querySelector('.remove-tag').addEventListener('click', function() {
                const tagToRemove = this.getAttribute('data-tag');
                const index = tags.indexOf(tagToRemove);
                if (index !== -1) {
                    tags.splice(index, 1);
                    updateTags();
                }
            });
        });
    }
    
    function addTag() {
        const tag = tagsInput.value.trim();
        if (tag && tags.length < 5) {
            if (!tags.includes(tag)) {
                tags.push(tag);
                updateTags();
                tagsInput.value = '';
            } else {
                alert('该标签已存在');
            }
        } else if (tags.length >= 5) {
            alert('最多只能添加5个标签');
        }
    }
    
    addTagBtn.addEventListener('click', addTag);
    tagsInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            addTag();
        }
    });
    
    // 新截图上传管理
    const MAX_IMAGES_PER_CATEGORY = 5;
    let allImageCount = 0;
    const MAX_TOTAL_IMAGES = 30;
    
    // 获取所有上传按钮
    const uploadButtons = document.querySelectorAll('button[data-section]');
    
    // 为每个上传按钮添加点击事件
    uploadButtons.forEach(button => {
        const section = button.getAttribute('data-section');
        const fileInput = document.getElementById(`${section}-upload`);
        const previewContainer = document.getElementById(`${section}-preview`);
        
        button.addEventListener('click', function() {
            const files = fileInput.files;
            
            if (files.length === 0) {
                alert('请先选择图片');
                return;
            }
            
            // 检查当前分类的图片数量
            const currentCategoryCount = previewContainer.querySelectorAll('.col-md-4').length;
            
            if (currentCategoryCount + files.length > MAX_IMAGES_PER_CATEGORY) {
                alert(`每个分类最多上传 ${MAX_IMAGES_PER_CATEGORY} 张图片`);
                return;
            }
            
            if (allImageCount + files.length > MAX_TOTAL_IMAGES) {
                alert(`所有分类总共最多上传 ${MAX_TOTAL_IMAGES} 张图片`);
                return;
            }
            
            // 处理每个文件
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    allImageCount++;
                    const imageIndex = `${section}_${Date.now()}_${i}`;
                    
                    const previewItem = document.createElement('div');
                    previewItem.className = 'col-md-4';
                    previewItem.innerHTML = `
                        <div class="card">
                            <div class="preview-container">
                                <img src="${e.target.result}" class="image-preview card-img-top">
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-2">
                                    <input class="form-check-input main-image-radio" type="radio" name="main_image" id="main_${imageIndex}" value="${imageIndex}">
                                    <label class="form-check-label" for="main_${imageIndex}">
                                        设为主图
                                    </label>
                                </div>
                                <div class="mb-2">
                                    <input type="text" class="form-control form-control-sm" placeholder="图片说明" name="captions_${i}" value="">
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-image w-100" data-index="${imageIndex}">删除</button>
                            </div>
                        </div>
                    `;
                    previewContainer.appendChild(previewItem);
                    
                    // 创建隐藏的文件输入
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.name = 'images';
                    fileInput.style.display = 'none';
                    fileInput.dataset.imageIndex = imageIndex;
                    fileInput.dataset.section = section;
                    
                    // 将File对象分配给fileInput
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    fileInput.files = dataTransfer.files;
                    
                    document.getElementById('editAccountForm').appendChild(fileInput);
                    
                    // 添加删除事件
                    previewItem.querySelector('.remove-image').addEventListener('click', function() {
                        previewItem.remove();
                        allImageCount--;
                        
                        // 移除对应的文件输入
                        const inputToRemove = document.querySelector(`input[type="file"][data-image-index="${imageIndex}"]`);
                        if (inputToRemove) {
                            inputToRemove.remove();
                        }
                    });
                };
                
                reader.readAsDataURL(file);
            }
            
            // 清空文件选择
            fileInput.value = '';
        });
    });
    
    // 添加表单提交前验证
    document.getElementById('editAccountForm').addEventListener('submit', function(e) {
        // 验证价格
        const price = parseFloat(document.getElementById('price').value);
        if (isNaN(price) || price <= 0) {
            alert('请输入有效的价格');
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}
{% endblock %} 