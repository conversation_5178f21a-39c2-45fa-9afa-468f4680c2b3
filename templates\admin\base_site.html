{% extends "base.html" %}

{% block title %}梦羽明日之后 - 管理后台{% endblock %}

{% block extrahead %}
<!-- 添加Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
<style>
    .admin-header {
        background-color: #343a40;
        color: white;
        padding: 1rem 0;
    }
    .admin-nav {
        background-color: #f8f9fa;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .admin-nav .nav-link {
        color: #212529;
        padding: 0.8rem 1rem;
        font-weight: 500;
    }
    .admin-nav .nav-link:hover {
        background-color: #e9ecef;
    }
    .admin-nav .nav-link.active {
        color: #007bff;
        background-color: #e9ecef;
        border-bottom: 2px solid #007bff;
    }
    .admin-nav .container {
        display: flex;
        justify-content: space-between;
    }
    .admin-nav-left, .admin-nav-right {
        display: flex;
    }
    .admin-container {
        padding: 2rem 0;
    }
    .admin-footer {
        margin-top: 3rem;
        padding: 1rem 0;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        color: #6c757d;
    }
</style>
{% block extra_admin_css %}{% endblock %}
{% endblock %}

{% block content %}
<header class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1>梦羽明日之后</h1>
                <p class="mb-0">管理员后台</p>
            </div>
            <div class="col-md-6 text-end">
                <span>欢迎，{{ request.user.get_display_name }}</span>
                <a href="{% url 'accounts:logout' %}" class="btn btn-light ms-3">退出登录</a>
            </div>
        </div>
    </div>
</header>

<nav class="admin-nav">
    <div class="container">
        <div class="admin-nav-left">
            <a href="{% url 'services:admin_dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'admin_dashboard' %}active{% endif %}">
                <i class="bi bi-speedometer2 me-1"></i> 控制面板
            </a>
            <a href="{% url 'services:admin_account_review' %}" class="nav-link {% if request.resolver_match.url_name == 'admin_account_review' %}active{% endif %}">
                <i class="bi bi-clipboard-check me-1"></i> 账号审核
            </a>
            <a href="{% url 'services:admin_account_orders' %}" class="nav-link {% if request.resolver_match.url_name == 'admin_account_orders' %}active{% endif %}">
                <i class="bi bi-cart me-1"></i> 订单管理
            </a>
        </div>
        <div class="admin-nav-right">
            <a href="/" class="nav-link">
                <i class="bi bi-house me-1"></i> 前台首页
            </a>
            <a href="/admin/" class="nav-link">
                <i class="bi bi-gear me-1"></i> Django管理
            </a>
        </div>
    </div>
</nav>

<div class="admin-container">
    <div class="container">
        {% block admin_content %}{% endblock %}
    </div>
</div>

<footer class="admin-footer">
    <div class="container text-center">
        <p class="mb-0">&copy; 2025 梦羽明日之后 - 管理员后台</p>
    </div>
</footer>
{% endblock %} 