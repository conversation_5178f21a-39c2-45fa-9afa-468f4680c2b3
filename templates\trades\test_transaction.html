{% extends 'base.html' %}

{% block title %}创建测试交易 | 梦羽明日之后{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">创建测试交易</h3>
        </div>
        <div class="card-body">
            <p class="lead">点击下方按钮创建一个测试交易，用于演示和测试交易功能。</p>
            
            <div class="alert alert-info">
                <strong>提示：</strong> 测试交易将会创建一个虚拟游戏账号并自动生成一个待支付的订单。
            </div>
            
            <form method="post" action="{% url 'games:create_test_transaction' %}">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary btn-lg">创建测试交易</button>
                <a href="{% url 'trades:transaction_list' %}" class="btn btn-outline-secondary btn-lg ms-2">查看我的交易</a>
            </form>
            
            <hr class="my-4">
            
            <div class="alert alert-warning">
                <h5>您的交易列表为空？</h5>
                <p>可能原因:</p>
                <ul>
                    <li>您尚未创建任何交易</li>
                    <li>交易创建失败或数据未正确保存</li>
                    <li>数据库连接问题</li>
                </ul>
                <p>建议操作:</p>
                <ol>
                    <li>点击上方"创建测试交易"按钮</li>
                    <li>检查数据库是否正常</li>
                    <li>检查控制台是否有错误日志</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %} 