import webbrowser
import socket
from threading import Timer
from django.core.management import execute_from_command_line
import os
import sys

def find_free_port():
    """找到一个可用的端口"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def open_browser(port):
    """在默认浏览器中打开URL"""
    webbrowser.open(f'http://127.0.0.1:{port}')

if __name__ == '__main__':
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'game_trade.settings')
    
    # 找到一个可用端口
    port = find_free_port()
    
    # 设置定时器在服务器启动后打开浏览器
    Timer(1.5, open_browser, [port]).start()
    
    # 启动Django开发服务器
    sys.argv = ['manage.py', 'runserver', f'127.0.0.1:{port}']
    execute_from_command_line(sys.argv) 