{% extends "base.html" %}

{% block title %}个人资料 - 梦羽明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.avatar %}
                        <img src="{{ user.avatar.url }}" alt="{{ user.get_display_name }}" class="rounded-circle img-thumbnail mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <div class="bg-secondary rounded-circle d-inline-flex justify-content-center align-items-center mb-3" style="width: 120px; height: 120px;">
                            <span class="text-white fs-1">{{ user.get_display_name.0|upper }}</span>
                        </div>
                    {% endif %}
                    <h5>{{ user.get_display_name }}</h5>
                    <p class="text-muted small">加入时间：{{ user.created_at|date:"Y年m月d日" }}</p>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:edit_profile' %}" class="btn btn-outline-primary btn-sm">编辑资料</a>
                        {% if not user.is_verified %}
                            <a href="{% url 'accounts:verification' %}" class="btn btn-outline-warning btn-sm">实名认证</a>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="list-group shadow-sm mb-4">
                <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                    <i class="bi bi-speedometer2 me-2"></i> 仪表盘
                </a>
                <a href="{% url 'accounts:profile' %}" class="list-group-item list-group-item-action active">
                    <i class="bi bi-person me-2"></i> 个人资料
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                    <i class="bi bi-wallet2 me-2"></i> 我的钱包
                    <span class="badge bg-primary rounded-pill float-end">{{ user.balance }}</span>
                </a>
                <a href="#" class="list-group-item list-group-item-action">
                    <i class="bi bi-chat-left-text me-2"></i> 我的消息
                </a>
                <a href="{% url 'accounts:password_change' %}" class="list-group-item list-group-item-action">
                    <i class="bi bi-shield-lock me-2"></i> 修改密码
                </a>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">个人资料详情</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">账号信息</h6>
                            <hr>
                            <dl class="row">
                                <dt class="col-sm-4">用户名</dt>
                                <dd class="col-sm-8">{{ user.username }}</dd>
                                
                                <dt class="col-sm-4">昵称</dt>
                                <dd class="col-sm-8">{{ user.nickname|default:"未设置" }}</dd>
                                
                                <dt class="col-sm-4">电子邮箱</dt>
                                <dd class="col-sm-8">{{ user.email }}</dd>
                                
                                <dt class="col-sm-4">手机号码</dt>
                                <dd class="col-sm-8">{{ user.phone }}</dd>
                                
                                <dt class="col-sm-4">注册时间</dt>
                                <dd class="col-sm-8">{{ user.created_at|date:"Y年m月d日 H:i" }}</dd>
                                
                                <dt class="col-sm-4">实名认证</dt>
                                <dd class="col-sm-8">
                                    {% if user.is_verified %}
                                        <span class="badge bg-success">已认证</span>
                                    {% else %}
                                        <span class="badge bg-warning">未认证</span>
                                    {% endif %}
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">个人信息</h6>
                            <hr>
                            <dl class="row">
                                <dt class="col-sm-4">姓名</dt>
                                <dd class="col-sm-8">{{ user.get_full_name|default:"未设置" }}</dd>
                                
                                <dt class="col-sm-4">游戏ID</dt>
                                <dd class="col-sm-8">{{ user.game_id|default:"未设置" }}</dd>
                                
                                <dt class="col-sm-4">信用评分</dt>
                                <dd class="col-sm-8">
                                    <span class="badge {% if user.credit_score >= 80 %}bg-success{% elif user.credit_score >= 60 %}bg-warning{% else %}bg-danger{% endif %}">
                                        {{ user.credit_score }}分
                                    </span>
                                </dd>
                                
                                <dt class="col-sm-4">账户余额</dt>
                                <dd class="col-sm-8">¥{{ user.balance }}</dd>
                            </dl>
                        </div>
                    </div>
                    
                    <h6 class="text-muted">个人简介</h6>
                    <hr>
                    <p>{{ user.bio|default:"这个人很懒，还没有填写个人简介..." }}</p>
                    
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{% url 'accounts:edit_profile' %}" class="btn btn-primary">
                            <i class="bi bi-pencil-square me-1"></i> 编辑资料
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 