{% extends 'base.html' %}
{% load static %}

{% block title %}游戏答疑 - 明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="text-center mb-5">游戏答疑</h1>
    
    <div class="row">
        <!-- 左侧答疑栏 -->
        <div class="col-lg-8 mb-4">
            <!-- 搜索栏 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" placeholder="搜索问题..." value="{{ search_query }}">
                                <button class="btn btn-outline-secondary" type="submit">搜索</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select name="category" class="form-select" onchange="this.form.submit()">
                                <option value="">所有分类</option>
                                {% for cat_code, cat_name in categories %}
                                <option value="{{ cat_code }}" {% if category == cat_code %}selected{% endif %}>{{ cat_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 问题列表 -->
            <h3 class="mb-3">热门问答</h3>
            {% if public_questions %}
                {% for question in public_questions %}
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <h5 class="card-title">
                                <a href="{% url 'services:question_detail' question_id=question.id %}" class="text-decoration-none">
                                    {{ question.title }}
                                </a>
                            </h5>
                            <span class="badge {% if question.status == 'pending' %}bg-warning{% elif question.status == 'answered' %}bg-success{% elif question.status == 'closed' %}bg-secondary{% endif %}">
                                {{ question.get_status_display }}
                            </span>
                        </div>
                        <p class="card-text text-truncate">{{ question.content }}</p>
                        <div class="d-flex justify-content-between">
                            <div>
                                <span class="badge bg-secondary">{{ question.get_category_display }}</span>
                                <small class="text-muted ms-2">{{ question.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                            <div>
                                <small class="text-muted">{{ question.view_count }} 浏览</small>
                                <small class="text-muted ms-2">{{ question.answer_count }} 回答</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                
                <!-- 分页 -->
                {% if public_questions.has_other_pages %}
                <nav aria-label="问题列表分页">
                    <ul class="pagination justify-content-center">
                        {% if public_questions.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ public_questions.previous_page_number }}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="上一页">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&laquo;</span>
                        </li>
                        {% endif %}
                        
                        {% for i in public_questions.paginator.page_range %}
                            {% if i == public_questions.number %}
                            <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                            {% elif i > public_questions.number|add:'-3' and i < public_questions.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if public_questions.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ public_questions.next_page_number }}{% if category %}&category={{ category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="下一页">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&raquo;</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">暂无公开问题或未找到匹配的问题。</div>
            {% endif %}
        </div>
        
        <!-- 右侧信息 -->
        <div class="col-lg-4">
            <!-- 用户操作卡片 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">便捷操作</h5>
                </div>
                <div class="card-body">
                    {% if user.is_authenticated %}
                        <a href="{% url 'services:ask_question' %}" class="btn btn-primary w-100 mb-3">提交新问题</a>
                        <a href="{% url 'services:my_questions' %}" class="btn btn-outline-secondary w-100 mb-2">我的问题记录</a>
                        
                        {% if user.is_staff %}
                        <a href="{% url 'services:admin_questions' %}" class="btn btn-outline-danger w-100 mt-3">管理员问题面板</a>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-warning">
                            请 <a href="{% url 'login' %}?next={{ request.path }}" class="alert-link">登录</a> 后提交问题
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 我的最近问题 -->
            {% if user.is_authenticated and user_questions %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">我的最近问题</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        {% for q in user_questions %}
                        <li class="list-group-item">
                            <a href="{% url 'services:question_detail' question_id=q.id %}">{{ q.title }}</a>
                            <span class="badge {% if q.status == 'pending' %}bg-warning{% elif q.status == 'answered' %}bg-success{% elif q.status == 'closed' %}bg-secondary{% endif %} float-end">
                                {{ q.get_status_display }}
                            </span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="card-footer text-end">
                    <a href="{% url 'services:my_questions' %}" class="btn btn-sm btn-outline-primary">查看全部</a>
                </div>
            </div>
            {% endif %}
            
            <!-- 答疑说明 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">答疑说明</h5>
                </div>
                <div class="card-body">
                    <p>明日之后官方答疑频道，由专业客服团队为您解答游戏相关问题。</p>
                    <ul>
                        <li>问题提交后将在24小时内获得回复</li>
                        <li>请详细描述您的问题，便于我们准确解答</li>
                        <li>您可以选择公开或私密提问</li>
                        <li>公开的问题将显示在此页面，帮助其他玩家</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 