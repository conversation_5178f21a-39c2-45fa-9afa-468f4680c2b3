from django.contrib import admin
from .models import CommunityPost, PostComment, WishlistService, TradeApplication, GameQuestion, QuestionAnswer, GameGuide, GuideImage, GuideComment, ServiceGameAccount, AccountImage, AccountTag, AccountOrder
from django.utils import timezone
from django.utils.html import format_html

@admin.register(CommunityPost)
class CommunityPostAdmin(admin.ModelAdmin):
    list_display = ('title', 'author', 'is_pinned', 'view_count', 'created_at')
    list_filter = ('is_pinned', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    date_hierarchy = 'created_at'
    list_editable = ('is_pinned',)

@admin.register(PostComment)
class PostCommentAdmin(admin.ModelAdmin):
    list_display = ('post', 'author', 'content_preview', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('content', 'author__username', 'post__title')
    date_hierarchy = 'created_at'
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '评论内容'

@admin.register(WishlistService)
class WishlistServiceAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'service_type', 'is_offering', 'price', 'is_active', 'created_at')
    list_filter = ('service_type', 'is_offering', 'is_active', 'created_at')
    search_fields = ('title', 'description', 'user__username')
    date_hierarchy = 'created_at'
    list_editable = ('is_active',)

@admin.register(TradeApplication)
class TradeApplicationAdmin(admin.ModelAdmin):
    list_display = ('service', 'applicant', 'contact_preference', 'status', 'created_at', 'processed_by')
    list_filter = ('status', 'contact_preference', 'created_at')
    search_fields = ('service__title', 'applicant__username', 'message')
    date_hierarchy = 'created_at'
    list_editable = ('status',)
    readonly_fields = ('service', 'applicant', 'message', 'contact_preference', 'created_at')
    
    fieldsets = (
        ('申请信息', {
            'fields': ('service', 'applicant', 'message', 'contact_preference', 'created_at')
        }),
        ('处理信息', {
            'fields': ('status', 'admin_notes', 'processed_by', 'processed_at')
        }),
    )
    
    def save_model(self, request, obj, form, change):
        # 如果状态从pending/processing变为其他状态，则自动设置处理人和处理时间
        if 'status' in form.changed_data and obj.status not in ['pending', 'processing']:
            if not obj.processed_by:
                obj.processed_by = request.user
            if not obj.processed_at:
                obj.processed_at = timezone.now()
        super().save_model(request, obj, form, change)

class QuestionAnswerInline(admin.TabularInline):
    model = QuestionAnswer
    extra = 1
    readonly_fields = ('created_at',)
    fields = ('responder', 'content', 'is_staff_answer', 'created_at')
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.order_by('-created_at')
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == 'responder' and not kwargs.get('initial'):
            kwargs['initial'] = request.user.id
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

@admin.register(GameQuestion)
class GameQuestionAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'category', 'status', 'has_answer', 'is_public', 'created_at')
    list_filter = ('status', 'category', 'is_public', 'created_at')
    search_fields = ('title', 'content', 'user__username')
    date_hierarchy = 'created_at'
    list_editable = ('status', 'is_public')
    readonly_fields = ('user', 'created_at', 'updated_at', 'view_count')
    inlines = [QuestionAnswerInline]
    
    fieldsets = (
        ('问题信息', {
            'fields': ('user', 'title', 'content', 'category', 'created_at')
        }),
        ('状态设置', {
            'fields': ('status', 'is_public', 'view_count', 'updated_at')
        }),
    )
    
    def has_answer(self, obj):
        return obj.answers.exists()
    has_answer.boolean = True
    has_answer.short_description = '已回复'
    
    def save_model(self, request, obj, form, change):
        if 'status' in form.changed_data:
            obj.updated_at = timezone.now()
        super().save_model(request, obj, form, change)

@admin.register(QuestionAnswer)
class QuestionAnswerAdmin(admin.ModelAdmin):
    list_display = ('question', 'responder', 'is_staff_answer', 'created_at')
    list_filter = ('is_staff_answer', 'created_at')
    search_fields = ('content', 'responder__username', 'question__title')
    date_hierarchy = 'created_at'
    list_editable = ('is_staff_answer',)
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == 'responder' and not kwargs.get('initial'):
            kwargs['initial'] = request.user.id
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

# 注册游戏攻略相关模型
class GuideImageInline(admin.TabularInline):
    model = GuideImage
    extra = 1
    fields = ('image', 'caption', 'order')

class GuideCommentInline(admin.TabularInline):
    model = GuideComment
    extra = 0
    readonly_fields = ('author', 'content', 'created_at')
    fields = ('author', 'content', 'created_at')
    can_delete = True
    
    def has_add_permission(self, request, obj=None):
        return False

@admin.register(GameGuide)
class GameGuideAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'author', 'is_featured', 'view_count', 'created_at')
    list_filter = ('category', 'is_featured', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    date_hierarchy = 'created_at'
    list_editable = ('is_featured', 'category')
    readonly_fields = ('created_at', 'updated_at', 'view_count')
    inlines = [GuideImageInline, GuideCommentInline]
    
    fieldsets = (
        ('攻略信息', {
            'fields': ('title', 'author', 'category', 'content')
        }),
        ('显示设置', {
            'fields': ('is_featured', 'view_count', 'created_at', 'updated_at')
        }),
    )
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == 'author' and not kwargs.get('initial'):
            kwargs['initial'] = request.user.id
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新创建的攻略，设置作者为当前用户
            obj.author = request.user
        super().save_model(request, obj, form, change)

@admin.register(GuideImage)
class GuideImageAdmin(admin.ModelAdmin):
    list_display = ('guide', 'caption', 'order', 'image_preview')
    list_filter = ('guide',)
    search_fields = ('guide__title', 'caption')
    list_editable = ('order', 'caption')
    
    def image_preview(self, obj):
        if obj.image:
            return f'<img src="{obj.image.url}" width="100" />'
        return '无图片'
    image_preview.allow_tags = True
    image_preview.short_description = '图片预览'

@admin.register(GuideComment)
class GuideCommentAdmin(admin.ModelAdmin):
    list_display = ('guide', 'author', 'content_preview', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('content', 'author__username', 'guide__title')
    date_hierarchy = 'created_at'
    readonly_fields = ('guide', 'author', 'created_at')
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '评论内容'

# 注册账号交易相关模型
class AccountImageInline(admin.TabularInline):
    model = AccountImage
    extra = 1
    fields = ('image', 'caption', 'is_main', 'section')

class AccountTagInline(admin.TabularInline):
    model = AccountTag
    extra = 1
    fields = ('name',)

@admin.register(ServiceGameAccount)
class ServiceGameAccountAdmin(admin.ModelAdmin):
    list_display = ('title', 'game_type', 'seller', 'price', 'account_level', 'status', 'is_featured', 'created_at')
    list_filter = ('game_type', 'status', 'account_level', 'is_featured', 'created_at')
    search_fields = ('title', 'description', 'seller__username')
    date_hierarchy = 'created_at'
    list_editable = ('status', 'is_featured')
    readonly_fields = ('created_at', 'updated_at', 'view_count', 'publish_at', 'expire_at', 'review_at')
    inlines = [AccountImageInline, AccountTagInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('seller', 'title', 'game_type', 'account_level', 'device_type', 'price')
        }),
        ('详细描述', {
            'fields': ('description', 'game_roles', 'game_items')
        }),
        ('状态管理', {
            'fields': ('status', 'is_featured', 'view_count', 'created_at', 'updated_at', 'publish_at', 'expire_at')
        }),
        ('审核信息', {
            'fields': ('reviewed_by', 'review_note', 'review_at')
        }),
        ('联系方式', {
            'fields': ('contact_type', 'contact_value'),
            'description': '联系方式仅管理员可见，不会直接显示给用户'
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        if obj and obj.status in ['sold', 'completed']:
            # 已售出的账号不允许编辑核心信息
            return self.readonly_fields + ('seller', 'game_type', 'title', 'description', 'price', 'account_level')
        return self.readonly_fields
    
    def save_model(self, request, obj, form, change):
        # 标记审核人员
        if change and 'status' in form.changed_data:
            if obj.status in ['published', 'rejected'] and not obj.reviewed_by:
                obj.reviewed_by = request.user
                obj.review_at = timezone.now()
        super().save_model(request, obj, form, change)

@admin.register(AccountImage)
class AccountImageAdmin(admin.ModelAdmin):
    list_display = ('account', 'caption', 'is_main', 'section', 'image_preview')
    list_filter = ('account__game_type', 'is_main', 'section')
    search_fields = ('account__title', 'caption', 'section')
    list_editable = ('is_main', 'caption')
    
    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="100" />', obj.image.url)
        return '无图片'
    image_preview.short_description = '图片预览'

@admin.register(AccountOrder)
class AccountOrderAdmin(admin.ModelAdmin):
    list_display = ('order_number', 'account', 'buyer', 'price', 'status', 'created_at')
    list_filter = ('status', 'created_at', 'account__game_type')
    search_fields = ('order_number', 'account__title', 'buyer__username')
    date_hierarchy = 'created_at'
    readonly_fields = ('order_number', 'created_at', 'paid_at', 'completed_at', 'cancelled_at')
    
    fieldsets = (
        ('订单信息', {
            'fields': ('order_number', 'account', 'buyer', 'price', 'status')
        }),
        ('联系信息', {
            'fields': ('contact_type', 'contact_value', 'message')
        }),
        ('订单时间', {
            'fields': ('created_at', 'paid_at', 'completed_at', 'cancelled_at')
        }),
        ('管理员处理', {
            'fields': ('handled_by', 'admin_note')
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if change and 'status' in form.changed_data:
            # 更新处理人员
            if obj.status in ['completed', 'cancelled', 'refunded'] and not obj.handled_by:
                obj.handled_by = request.user
                
            # 更新相应的时间
            if obj.status == 'processing' and not obj.paid_at:
                obj.paid_at = timezone.now()
            elif obj.status == 'completed' and not obj.completed_at:
                obj.completed_at = timezone.now()
                # 标记账号为已售出
                if obj.account.status == 'published':
                    obj.account.status = 'sold'
                    obj.account.save()
            elif obj.status == 'cancelled' and not obj.cancelled_at:
                obj.cancelled_at = timezone.now()
        
        super().save_model(request, obj, form, change)
