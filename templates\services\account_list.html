{% extends 'base.html' %}
{% load static %}

{% block title %}明日之后账号交易 - 账号列表{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item active" aria-current="page">明日之后账号交易</li>
        </ol>
    </nav>
    
    <!-- 推荐账号轮播 -->
    {% if featured_accounts %}
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">精选账号</h5>
        </div>
        <div class="card-body p-0">
            <div id="featuredAccountsCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-indicators">
                    {% for account in featured_accounts %}
                    <button type="button" data-bs-target="#featuredAccountsCarousel" data-bs-slide-to="{{ forloop.counter0 }}" {% if forloop.first %}class="active"{% endif %} aria-label="Slide {{ forloop.counter }}"></button>
                    {% endfor %}
                </div>
                <div class="carousel-inner">
                    {% for account in featured_accounts %}
                    <div class="carousel-item {% if forloop.first %}active{% endif %}">
                        <a href="{% url 'services:account_detail' account_id=account.id %}">
                            <div class="row g-0 align-items-center">
                                <div class="col-md-4">
                                    {% if account.main_image %}
                                    <img src="{{ account.main_image.image.url }}" class="img-fluid" alt="{{ account.title }}" style="height: 250px; object-fit: cover;">
                                    {% else %}
                                    <div class="bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                        <span class="text-muted">无图片</span>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-8">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ account.title }}</h5>
                                        <p class="card-text text-danger fw-bold">¥ {{ account.price }}</p>
                                        <p class="card-text">
                                            <span class="badge bg-secondary">明日之后</span>
                                            <span class="badge bg-info">{{ account.get_account_level_display }}</span>
                                            {% if account.profession %}
                                            <span class="badge bg-success">{{ account.get_profession_display }}</span>
                                            {% endif %}
                                            {% if account.server %}
                                            <span class="badge bg-warning text-dark">{{ account.server }}</span>
                                            {% endif %}
                                        </p>
                                        <p class="card-text">{{ account.description|truncatechars:120 }}</p>
                                        <button class="btn btn-primary">查看详情</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    {% endfor %}
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#featuredAccountsCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">上一个</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#featuredAccountsCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">下一个</span>
                </button>
            </div>
        </div>
    </div>
    {% endif %}
    
    <div class="row">
        <!-- 筛选栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">筛选条件</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'services:account_list' %}">
                        <div class="mb-3">
                            <label for="account_level" class="form-label">庄园等级</label>
                            <select class="form-select" id="account_level" name="account_level">
                                <option value="">全部等级</option>
                                {% for code, name in account_level_choices %}
                                <option value="{{ code }}" {% if account_level == code %}selected{% endif %}>{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="profession" class="form-label">职业选择</label>
                            <select class="form-select" id="profession" name="profession">
                                <option value="">全部职业</option>
                                {% for code, name in profession_choices %}
                                <option value="{{ code }}" {% if profession == code %}selected{% endif %}>{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="server" class="form-label">区服搜索</label>
                            <input type="text" class="form-control" id="server" name="server" value="{{ server }}" placeholder="输入区服名称">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">价格范围</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" class="form-control" placeholder="最低价" name="price_min" value="{{ price_min|default:'' }}">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control" placeholder="最高价" name="price_max" value="{{ price_max|default:'' }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="q" class="form-label">关键词搜索</label>
                            <input type="text" class="form-control" id="q" name="q" value="{{ keyword }}" placeholder="输入关键词">
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">应用筛选</button>
                        </div>
                        
                        {% if account_level or profession or server or price_min or price_max or keyword %}
                        <div class="d-grid mt-2">
                            <a href="{% url 'services:account_list' %}" class="btn btn-outline-secondary">清除筛选</a>
                        </div>
                        {% endif %}
                    </form>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">快速链接</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{% url 'services:sell_account' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle me-2"></i>出售我的账号
                        </a>
                        <a href="{% url 'services:my_selling_accounts' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-list-ul me-2"></i>我出售的账号
                        </a>
                        <a href="{% url 'services:my_account_orders' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-bag me-2"></i>我的购买申请
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 账号列表 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">账号列表</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            {% if sort == 'price' %}
                                价格从低到高
                            {% elif sort == '-price' %}
                                价格从高到低
                            {% elif sort == 'level' %}
                                庄园等级从低到高
                            {% elif sort == '-level' %}
                                庄园等级从高到低
                            {% elif sort == 'created_at' %}
                                最早发布
                            {% elif sort == '-created_at' %}
                                最新发布
                            {% elif sort == 'view_count' %}
                                浏览量从高到低
                            {% else %}
                                排序方式
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=price">价格从低到高</a></li>
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=-price">价格从高到低</a></li>
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=level">庄园等级从低到高</a></li>
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=-level">庄园等级从高到低</a></li>
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=-created_at">最新发布</a></li>
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=created_at">最早发布</a></li>
                            <li><a class="dropdown-item" href="?{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}sort=-view_count">浏览量从高到低</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if accounts %}
                        <div class="row row-cols-1 row-cols-md-3 g-4">
                            {% for account in accounts %}
                            <div class="col">
                                <div class="card h-100">
                                    <div class="position-relative">
                                        {% if account.main_image %}
                                        <img src="{{ account.main_image.image.url }}" class="card-img-top" alt="{{ account.title }}" style="height: 180px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="height: 180px;">
                                            <span class="text-muted">无图片</span>
                                        </div>
                                        {% endif %}
                                        <span class="position-absolute top-0 end-0 badge bg-danger m-2">¥ {{ account.price }}</span>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">{{ account.title }}</h5>
                                        <div class="mb-2">
                                            <span class="badge bg-info">{{ account.get_account_level_display }}</span>
                                            {% if account.profession %}
                                            <span class="badge bg-success">{{ account.get_profession_display }}</span>
                                            {% endif %}
                                            {% if account.server %}
                                            <span class="badge bg-warning text-dark">{{ account.server }}</span>
                                            {% endif %}
                                        </div>
                                        <p class="card-text small">{{ account.description|truncatechars:80 }}</p>
                                    </div>
                                    <div class="card-footer bg-transparent d-flex justify-content-between align-items-center">
                                        <small class="text-muted">浏览: {{ account.view_count }}</small>
                                        <a href="{% url 'services:account_detail' account_id=account.id %}" class="btn btn-sm btn-primary">查看详情</a>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- 分页 -->
                        {% if accounts.has_other_pages %}
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if accounts.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ accounts.previous_page_number }}">上一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">上一页</span>
                                </li>
                                {% endif %}
                                
                                {% for num in accounts.paginator.page_range %}
                                    {% if accounts.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                    {% elif num > accounts.number|add:'-3' and num < accounts.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if accounts.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ accounts.next_page_number }}">下一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">下一页</span>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>没有找到符合条件的账号。您可以尝试调整筛选条件。
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 