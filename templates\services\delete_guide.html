{% extends 'base.html' %}
{% load static %}

{% block title %}删除攻略 - {{ guide.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:guide_list' %}">攻略专区</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:guide_detail' guide_id=guide.id %}">{{ guide.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">删除攻略</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h2 class="card-title h4 mb-0">确认删除</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5><i class="bi bi-exclamation-triangle-fill"></i> 警告</h5>
                        <p>您即将删除攻略 "{{ guide.title }}"。此操作<strong>不可撤销</strong>，攻略的所有内容、图片和评论将被永久删除。</p>
                    </div>
                    
                    <h6>攻略信息：</h6>
                    <ul>
                        <li><strong>标题:</strong> {{ guide.title }}</li>
                        <li><strong>分类:</strong> {{ guide.get_category_display }}</li>
                        <li><strong>发布时间:</strong> {{ guide.created_at|date:"Y-m-d H:i" }}</li>
                        <li><strong>浏览次数:</strong> {{ guide.view_count }}</li>
                        <li><strong>图片数量:</strong> {{ guide.images.count }}</li>
                        <li><strong>评论数量:</strong> {{ guide.comments.count }}</li>
                    </ul>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'services:guide_detail' guide_id=guide.id %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-danger">确认删除</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 