{% extends "admin/base_site.html" %}

{% block title %}账号审核 | 梦羽明日之后{% endblock %}

{% block extra_admin_css %}
<style>
    .account-review-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    .account-screenshot {
        max-height: 300px;
        object-fit: contain;
    }
    .status-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        display: inline-block;
    }
    .status-pending {
        background-color: #ffc107;
        color: #212529;
    }
    .status-approved {
        background-color: #28a745;
        color: white;
    }
    .status-rejected {
        background-color: #dc3545;
        color: white;
    }
</style>
{% endblock %}

{% block admin_content %}
<div class="account-review-container">
    <div id="content-main">
        <h1>账号审核管理</h1>
        
        <div class="module">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>提交时间</th>
                        <th>卖家</th>
                        <th>账号标题</th>
                        <th>区服</th>
                        <th>价格</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for account in pending_accounts %}
                    <tr>
                        <td>{{ account.id }}</td>
                        <td>{{ account.created_at|date:"Y-m-d H:i" }}</td>
                        <td>{{ account.seller.get_display_name }}</td>
                        <td>{{ account.title }}</td>
                        <td>{{ account.server }}</td>
                        <td>¥{{ account.price }}</td>
                        <td>
                            <span class="status-badge status-pending">待审核</span>
                        </td>
                        <td>
                            <a href="{% url 'services:admin_account_review_detail' account.id %}" class="btn btn-sm btn-primary">审核</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 20px;">
                            没有待审核的账号
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <h2>最近处理记录</h2>
        <div class="module">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>处理时间</th>
                        <th>卖家</th>
                        <th>账号标题</th>
                        <th>状态</th>
                        <th>审核人</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    {% for review in recent_reviews %}
                    <tr>
                        <td>{{ review.account.id }}</td>
                        <td>{{ review.reviewed_at|date:"Y-m-d H:i" }}</td>
                        <td>{{ review.account.seller.get_display_name }}</td>
                        <td>{{ review.account.title }}</td>
                        <td>
                            {% if review.approved %}
                            <span class="status-badge status-approved">已通过</span>
                            {% else %}
                            <span class="status-badge status-rejected">已拒绝</span>
                            {% endif %}
                        </td>
                        <td>{{ review.reviewer.get_display_name }}</td>
                        <td>{{ review.comments }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px;">
                            没有最近处理记录
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 