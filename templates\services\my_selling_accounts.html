{% extends 'base.html' %}
{% load static %}

{% block title %}我出售的账号{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item active" aria-current="page">我出售的账号</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">账号交易</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="{% url 'services:account_list' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-search me-2"></i>浏览账号
                        </a>
                        <a href="{% url 'services:sell_account' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle me-2"></i>出售我的账号
                        </a>
                        <a href="{% url 'services:my_selling_accounts' %}" class="list-group-item list-group-item-action active">
                            <i class="bi bi-list-ul me-2"></i>我出售的账号
                        </a>
                        <a href="{% url 'services:my_account_orders' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-bag me-2"></i>我的购买申请
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 出售统计 -->
            <div class="card mt-3">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">出售统计</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h3 class="mb-0">{{ total_accounts }}</h3>
                            <small class="text-muted">账号总数</small>
                        </div>
                        <div class="col-6">
                            <h3 class="mb-0">{{ pending_orders }}</h3>
                            <small class="text-muted">待处理申请</small>
                        </div>
                    </div>
                    <hr>
                    <div class="d-grid">
                        <a href="{% url 'services:sell_account' %}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i> 出售新账号
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">我出售的账号</h5>
                </div>
                <div class="card-body">
                    {% if accounts %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">账号信息</th>
                                    <th scope="col">价格</th>
                                    <th scope="col">状态</th>
                                    <th scope="col">创建时间</th>
                                    <th scope="col">浏览量</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr>
                                    <td style="min-width: 200px;">
                                        <div class="d-flex align-items-center">
                                            {% if account.main_image %}
                                            <img src="{{ account.main_image.url }}" alt="{{ account.title }}" class="me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                            <div class="me-3 bg-light text-center d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-image text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <a href="{% url 'services:account_detail' account.id %}" class="text-decoration-none">
                                                    <strong>{{ account.title|truncatechars:30 }}</strong>
                                                </a>
                                                <div class="small text-muted">{{ account.get_game_type_display }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-danger fw-bold">¥{{ account.price }}</span>
                                    </td>
                                    <td>
                                        {% if account.status == 'pending' %}
                                        <span class="badge bg-warning text-dark">待审核</span>
                                        {% elif account.status == 'rejected' %}
                                        <span class="badge bg-danger">审核未通过</span>
                                        {% elif account.status == 'active' %}
                                        <span class="badge bg-success">已上架</span>
                                        {% elif account.status == 'sold' %}
                                        <span class="badge bg-primary">已售出</span>
                                        {% elif account.status == 'inactive' %}
                                        <span class="badge bg-secondary">已下架</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ account.created_at|date:"Y-m-d" }}</td>
                                    <td>{{ account.view_count }}</td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ account.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                操作
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ account.id }}">
                                                <li><a class="dropdown-item" href="{% url 'services:account_detail' account.id %}">查看详情</a></li>
                                                
                                                {% if account.status == 'active' %}
                                                <li><a class="dropdown-item" href="{% url 'services:edit_account' account.id %}">编辑账号</a></li>
                                                <li><a class="dropdown-item text-danger account-deactivate" href="#" data-account-id="{{ account.id }}" data-bs-toggle="modal" data-bs-target="#deactivateModal">下架账号</a></li>
                                                {% elif account.status == 'inactive' %}
                                                <li><a class="dropdown-item account-activate" href="#" data-account-id="{{ account.id }}">重新上架</a></li>
                                                {% elif account.status == 'rejected' %}
                                                <li><a class="dropdown-item" href="{% url 'services:edit_account' account.id %}">修改并重新提交</a></li>
                                                {% endif %}
                                                
                                                {% if account.status != 'sold' %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger account-delete" href="#" data-account-id="{{ account.id }}" data-bs-toggle="modal" data-bs-target="#deleteModal">删除账号</a></li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if accounts.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if accounts.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ accounts.previous_page_number }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in accounts.paginator.page_range %}
                                {% if accounts.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > accounts.number|add:'-3' and i < accounts.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if accounts.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ accounts.next_page_number }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="bi bi-shop fs-1 text-muted"></i>
                        </div>
                        <h5>您还没有任何出售中的账号</h5>
                        <p class="text-muted">出售您的游戏账号，轻松赚取收益</p>
                        <a href="{% url 'services:sell_account' %}" class="btn btn-primary mt-3">
                            <i class="bi bi-plus-circle me-1"></i> 出售账号
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 审核说明 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">审核说明</h5>
                </div>
                <div class="card-body">
                    <div class="small">
                        <p class="mb-2"><span class="badge bg-warning text-dark">待审核</span> - 您的账号正在等待平台审核，通常1-2个工作日内完成</p>
                        <p class="mb-2"><span class="badge bg-danger">审核未通过</span> - 账号信息不符合平台要求，请查看审核意见并修改</p>
                        <p class="mb-2"><span class="badge bg-success">已上架</span> - 账号已通过审核并上架，买家可以浏览并申请购买</p>
                        <p class="mb-2"><span class="badge bg-primary">已售出</span> - 账号已完成交易并交付给买家</p>
                        <p class="mb-0"><span class="badge bg-secondary">已下架</span> - 账号已被您主动下架，不再向买家显示</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 下架账号确认Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1" aria-labelledby="deactivateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deactivateModalLabel">确认下架账号</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要下架该账号吗？下架后买家将无法查看和购买该账号。</p>
                <p>您可以随时重新上架该账号。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmDeactivate">确认下架</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除账号确认Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除账号</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>警告：</strong>删除操作无法撤销！
                </div>
                <p>确定要删除该账号吗？删除后所有相关信息将无法恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 下架账号
    let accountToDeactivate = null;
    
    document.querySelectorAll('.account-deactivate').forEach(button => {
        button.addEventListener('click', function() {
            accountToDeactivate = this.getAttribute('data-account-id');
        });
    });
    
    document.getElementById('confirmDeactivate').addEventListener('click', function() {
        if (accountToDeactivate) {
            window.location.href = "{% url 'services:deactivate_account' 0 %}".replace('0', accountToDeactivate);
        }
    });
    
    // 重新上架账号
    document.querySelectorAll('.account-activate').forEach(button => {
        button.addEventListener('click', function() {
            const accountId = this.getAttribute('data-account-id');
            if (confirm('确定要重新上架该账号吗？')) {
                window.location.href = "{% url 'services:activate_account' 0 %}".replace('0', accountId);
            }
        });
    });
    
    // 删除账号
    let accountToDelete = null;
    
    document.querySelectorAll('.account-delete').forEach(button => {
        button.addEventListener('click', function() {
            accountToDelete = this.getAttribute('data-account-id');
        });
    });
    
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (accountToDelete) {
            window.location.href = "{% url 'services:delete_account' 0 %}".replace('0', accountToDelete);
        }
    });
});
</script>
{% endblock %}
{% endblock %} 