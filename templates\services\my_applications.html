{% extends 'base.html' %}

{% block title %}我的申请 - 心愿工坊{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>我的交易申请</h1>
        <a href="{% url 'services:wishlist_list' %}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> 返回心愿工坊
        </a>
    </div>

    <!-- 申请列表 -->
    {% if applications %}
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-list-check"></i> 申请记录</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>服务标题</th>
                            <th>类型</th>
                            <th>申请时间</th>
                            <th>联系方式</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for application in applications %}
                        <tr>
                            <td>
                                <a href="{% url 'services:wishlist_detail' service_id=application.service.id %}" class="text-decoration-none">
                                    {{ application.service.title }}
                                </a>
                            </td>
                            <td>
                                <span class="badge {% if application.service.is_offering %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if application.service.is_offering %}购买服务{% else %}提供服务{% endif %}
                                </span>
                            </td>
                            <td>{{ application.created_at|date:"Y-m-d H:i" }}</td>
                            <td>{{ application.get_contact_preference_display }}</td>
                            <td>
                                {% if application.status == 'pending' %}
                                <span class="badge bg-warning text-dark">待处理</span>
                                {% elif application.status == 'processing' %}
                                <span class="badge bg-info">处理中</span>
                                {% elif application.status == 'completed' %}
                                <span class="badge bg-success">已完成</span>
                                {% elif application.status == 'rejected' %}
                                <span class="badge bg-danger">已拒绝</span>
                                {% elif application.status == 'canceled' %}
                                <span class="badge bg-secondary">已取消</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if application.status == 'pending' or application.status == 'processing' %}
                                <form method="post" action="{% url 'services:cancel_application' application_id=application.id %}" class="d-inline" onsubmit="return confirm('确定要取消此申请吗？');">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="bi bi-x-circle"></i> 取消
                                    </button>
                                </form>
                                {% else %}
                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="bi bi-x-circle"></i> 取消
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if applications.has_other_pages %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if applications.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ applications.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&laquo;</a>
            </li>
            {% endif %}

            {% for i in applications.paginator.page_range %}
                {% if applications.number == i %}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ i }}</span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                </li>
                {% endif %}
            {% endfor %}

            {% if applications.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ applications.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&raquo;</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    {% else %}
    <div class="text-center py-5">
        <div class="display-1 text-muted mb-4">
            <i class="bi bi-clipboard-check"></i>
        </div>
        <h3 class="text-muted mb-3">您还没有提交过交易申请</h3>
        <p>浏览心愿工坊，找到您需要的服务或者您可以提供的服务吧！</p>
        <div class="mt-3">
            <a href="{% url 'services:wishlist_list' %}" class="btn btn-primary">前往心愿工坊</a>
        </div>
    </div>
    {% endif %}
</div>

<!-- 通知提示 -->
<div class="container mt-5">
    <div class="card bg-light">
        <div class="card-body">
            <h5 class="card-title"><i class="bi bi-info-circle-fill text-primary"></i> 申请处理流程</h5>
            <ol class="mb-0">
                <li>客服人员将在1-24小时内处理您的申请</li>
                <li>处理过程中，客服会创建交易群聊并邀请您和对方加入</li>
                <li>群聊中您可以与对方协商具体的交易细节</li>
                <li>达成一致后，可通过平台提供的安全支付方式完成交易</li>
                <li>交易完成后，平台会保存相关记录作为凭证</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %} 