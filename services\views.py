from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.db.models import Q, Count, Exists, OuterRef
from django.utils import timezone
from django.http import JsonResponse
import json
from .models import (
    CommunityPost, PostComment, WishlistService, TradeApplication,
    GameQuestion, QuestionAnswer, GameGuide, GuideImage, GuideComment,
    ServiceGameAccount, AccountOrder, AccountTag, AccountImage
)
from accounts.models import Notification
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.db.models import ProtectedError
from django.db import transaction

# Create your views here.

def account_adjustment(request):
    return render(request, 'services/account_adjustment.html')

def game_qa(request):
    """游戏答疑页面"""
    # 获取公开的问题列表，按状态和时间排序
    public_questions = GameQuestion.objects.filter(is_public=True).annotate(
        answer_count=Count('answers')
    ).order_by('-status', '-created_at')
    
    # 分类筛选
    category = request.GET.get('category', '')
    if category:
        public_questions = public_questions.filter(category=category)
    
    # 搜索
    search_query = request.GET.get('search', '')
    if search_query:
        public_questions = public_questions.filter(
            Q(title__icontains=search_query) | 
            Q(content__icontains=search_query)
        )
    
    # 分页
    paginator = Paginator(public_questions, 10)
    page_number = request.GET.get('page', 1)
    page_questions = paginator.get_page(page_number)
    
    # 处理提交问题
    if request.method == 'POST' and request.user.is_authenticated:
        title = request.POST.get('title')
        content = request.POST.get('content')
        category = request.POST.get('category')
        is_public = request.POST.get('is_public') == 'on'
        
        if title and content and category:
            GameQuestion.objects.create(
                user=request.user,
                title=title,
                content=content,
                category=category,
                is_public=is_public
            )
            messages.success(request, '您的问题已提交，我们会尽快回复！')
            return redirect('services:my_questions')
        else:
            messages.error(request, '请填写所有必填字段！')
    
    # 获取用户自己提问的问题
    user_questions = []
    if request.user.is_authenticated:
        user_questions = GameQuestion.objects.filter(
            user=request.user
        ).order_by('-created_at')[:5]  # 只显示最近5个问题
    
    return render(request, 'services/game_qa.html', {
        'public_questions': page_questions,
        'user_questions': user_questions,
        'category': category,
        'search_query': search_query,
        'categories': GameQuestion.CATEGORY_CHOICES
    })

@login_required
def my_questions(request):
    """用户查看自己的问题列表"""
    questions = GameQuestion.objects.filter(user=request.user).annotate(
        has_new_answers=Exists(
            QuestionAnswer.objects.filter(
                question=OuterRef('pk'),
                created_at__gt=OuterRef('updated_at')
            )
        )
    ).order_by('-created_at')
    
    # 筛选
    status = request.GET.get('status', '')
    if status:
        questions = questions.filter(status=status)
    
    # 分页
    paginator = Paginator(questions, 10)
    page_number = request.GET.get('page', 1)
    page_questions = paginator.get_page(page_number)
    
    return render(request, 'services/my_questions.html', {
        'questions': page_questions,
        'status': status,
        'statuses': GameQuestion.STATUS_CHOICES
    })

@login_required
def question_detail(request, question_id):
    """问题详情页面"""
    question = get_object_or_404(GameQuestion, id=question_id)
    
    # 检查查看权限
    if not question.is_public and question.user != request.user and not request.user.is_staff:
        messages.error(request, '您没有权限查看此问题')
        return redirect('services:game_qa')
    
    # 增加浏览量
    question.view_count += 1
    question.save(update_fields=['view_count'])
    
    # 处理提交回复
    if request.method == 'POST' and 'answer_content' in request.POST:
        content = request.POST.get('answer_content')
        
        if content:
            # 检查是否是管理员回复
            is_staff_answer = request.user.is_staff
            
            # 创建回复
            QuestionAnswer.objects.create(
                question=question,
                responder=request.user,
                content=content,
                is_staff_answer=is_staff_answer
            )
            
            messages.success(request, '回复已提交！')
            return redirect('services:question_detail', question_id=question.id)
        else:
            messages.error(request, '回复内容不能为空')
    
    # 获取所有回复
    answers = question.answers.all().order_by('created_at')
    
    # 更新问题的更新时间
    if request.user == question.user:
        question.updated_at = timezone.now()
        question.save(update_fields=['updated_at'])
    
    return render(request, 'services/question_detail.html', {
        'question': question,
        'answers': answers,
        'can_answer': request.user.is_authenticated
    })

@login_required
def ask_question(request):
    """提问页面"""
    if request.method == 'POST':
        title = request.POST.get('title')
        content = request.POST.get('content')
        category = request.POST.get('category')
        is_public = request.POST.get('is_public') == 'on'
        
        if title and content and category:
            question = GameQuestion.objects.create(
                user=request.user,
                title=title,
                content=content,
                category=category,
                is_public=is_public
            )
            messages.success(request, '您的问题已提交，我们会尽快回复！')
            return redirect('services:question_detail', question_id=question.id)
        else:
            messages.error(request, '请填写所有必填字段！')
    
    return render(request, 'services/ask_question.html', {
        'categories': GameQuestion.CATEGORY_CHOICES
    })

@user_passes_test(lambda u: u.is_staff)
def admin_questions(request):
    """管理员查看所有问题"""
    questions = GameQuestion.objects.all().annotate(
        answer_count=Count('answers')
    ).order_by('-created_at')
    
    # 筛选
    status = request.GET.get('status', '')
    if status:
        questions = questions.filter(status=status)
    
    category = request.GET.get('category', '')
    if category:
        questions = questions.filter(category=category)
    
    # 只显示待回复的问题
    pending_only = request.GET.get('pending_only') == 'true'
    if pending_only:
        questions = questions.filter(status='pending')
    
    # 搜索
    search_query = request.GET.get('search', '')
    if search_query:
        questions = questions.filter(
            Q(title__icontains=search_query) | 
            Q(content__icontains=search_query) |
            Q(user__username__icontains=search_query)
        )
    
    # 分页
    paginator = Paginator(questions, 20)
    page_number = request.GET.get('page', 1)
    page_questions = paginator.get_page(page_number)
    
    return render(request, 'services/admin_questions.html', {
        'questions': page_questions,
        'status': status,
        'category': category,
        'pending_only': pending_only,
        'search_query': search_query,
        'statuses': GameQuestion.STATUS_CHOICES,
        'categories': GameQuestion.CATEGORY_CHOICES,
        'pending_count': GameQuestion.objects.filter(status='pending').count()
    })

def live_stream(request):
    return render(request, 'services/live_stream.html')

# 社区功能视图
def community(request):
    """社区首页，显示帖子列表"""
    posts = CommunityPost.objects.all()
    
    # 搜索功能
    search_query = request.GET.get('search', '')
    if search_query:
        posts = posts.filter(
            Q(title__icontains=search_query) | 
            Q(content__icontains=search_query)
        )
    
    # 分页
    paginator = Paginator(posts, 10)  # 每页显示10篇帖子
    page_number = request.GET.get('page', 1)
    page_posts = paginator.get_page(page_number)
    
    return render(request, 'services/community.html', {
        'posts': page_posts,
        'search_query': search_query
    })

@login_required
def create_post(request):
    """创建新帖子"""
    if request.method == 'POST':
        title = request.POST.get('title')
        content = request.POST.get('content')
        
        if title and content:
            post = CommunityPost.objects.create(
                author=request.user,
                title=title,
                content=content
            )
            messages.success(request, '帖子发布成功！')
            return redirect('services:post_detail', post_id=post.id)
        else:
            messages.error(request, '请填写标题和内容！')
    
    return render(request, 'services/create_post.html')

def post_detail(request, post_id):
    """帖子详情页面"""
    post = get_object_or_404(CommunityPost, id=post_id)
    
    # 增加浏览量
    post.view_count += 1
    post.save()
    
    # 处理评论提交
    if request.method == 'POST' and request.user.is_authenticated:
        comment_content = request.POST.get('comment')
        if comment_content:
            PostComment.objects.create(
                post=post,
                author=request.user,
                content=comment_content
            )
            messages.success(request, '评论发布成功！')
            return redirect('services:post_detail', post_id=post.id)
    
    # 获取评论
    comments = post.comments.all()
    
    return render(request, 'services/post_detail.html', {
        'post': post,
        'comments': comments
    })

# 心愿工坊功能视图
def wishlist_service(request):
    """发布需求服务"""
    if request.method == 'POST' and request.user.is_authenticated:
        service_type = request.POST.get('service_type')
        title = request.POST.get('title')
        description = request.POST.get('description')
        price = request.POST.get('price')
        price_negotiable = 'price_negotiable' in request.POST
        contact_info = request.POST.get('contact_info')
        
        if service_type and title and description and contact_info:
            WishlistService.objects.create(
                user=request.user,
                service_type=service_type,
                title=title,
                description=description,
                price=price if price else None,
                price_negotiable=price_negotiable,
                is_offering=False,  # 需求服务
                contact_info=contact_info
            )
            messages.success(request, '需求发布成功！')
            return redirect('services:wishlist_list')
        else:
            messages.error(request, '请填写所有必填字段！')
    
    return render(request, 'services/wishlist_service.html')

def wishlist_provide(request):
    """提供服务"""
    if request.method == 'POST' and request.user.is_authenticated:
        service_type = request.POST.get('service_type')
        title = request.POST.get('title')
        description = request.POST.get('description')
        price = request.POST.get('price')
        price_negotiable = 'price_negotiable' in request.POST
        contact_info = request.POST.get('contact_info')
        
        if service_type and title and description and contact_info:
            WishlistService.objects.create(
                user=request.user,
                service_type=service_type,
                title=title,
                description=description,
                price=price if price else None,
                price_negotiable=price_negotiable,
                is_offering=True,  # 提供服务
                contact_info=contact_info
            )
            messages.success(request, '服务发布成功！')
            return redirect('services:wishlist_list')
        else:
            messages.error(request, '请填写所有必填字段！')
    
    return render(request, 'services/wishlist_provide.html')

def wishlist_list(request):
    """心愿工坊服务列表"""
    services = WishlistService.objects.filter(is_active=True)
    
    # 过滤条件
    service_type = request.GET.get('type', '')
    is_offering = request.GET.get('offering', '')
    search_query = request.GET.get('search', '')
    
    if service_type:
        services = services.filter(service_type=service_type)
    
    if is_offering:
        services = services.filter(is_offering=(is_offering == 'true'))
    
    if search_query:
        services = services.filter(
            Q(title__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # 分页
    paginator = Paginator(services, 12)  # 每页显示12个服务
    page_number = request.GET.get('page', 1)
    page_services = paginator.get_page(page_number)
    
    return render(request, 'services/wishlist_list.html', {
        'services': page_services,
        'service_type': service_type,
        'is_offering': is_offering,
        'search_query': search_query
    })

def wishlist_detail(request, service_id):
    """心愿工坊服务详情"""
    service = get_object_or_404(WishlistService, id=service_id)
    return render(request, 'services/wishlist_detail.html', {'service': service})

@login_required
def my_services(request):
    """我的服务/需求"""
    services = WishlistService.objects.filter(user=request.user)
    return render(request, 'services/my_services.html', {'services': services})

@login_required
def apply_trade(request, service_id):
    """申请交易"""
    service = get_object_or_404(WishlistService, id=service_id)
    
    # 检查是否是自己的服务
    if service.user == request.user:
        messages.error(request, '不能申请自己发布的服务！')
        return redirect('services:wishlist_detail', service_id=service_id)
    
    # 检查是否已经提交过申请
    existing_application = TradeApplication.objects.filter(
        service=service,
        applicant=request.user,
        status__in=['pending', 'processing']
    ).exists()
    
    if existing_application:
        messages.warning(request, '您已经提交过申请，请等待处理或查看申请状态。')
        return redirect('services:my_applications')
    
    if request.method == 'POST':
        contact_preference = request.POST.get('contact_preference')
        message = request.POST.get('message', '')
        agree_terms = request.POST.get('agree_terms', False)
        
        if not agree_terms:
            messages.error(request, '您必须同意平台交易规则和服务条款才能继续。')
            return redirect('services:wishlist_detail', service_id=service_id)
        
        # 创建申请记录
        application = TradeApplication.objects.create(
            service=service,
            applicant=request.user,
            message=message,
            contact_preference=contact_preference
        )
        
        messages.success(request, '申请已提交成功！我们的客服会在1-24小时内与您联系，请保持联系方式畅通。')
        
        # 发送邮件通知管理员（这里可以添加邮件发送代码）
        
        return redirect('services:my_applications')
    
    # GET请求直接重定向到详情页
    return redirect('services:wishlist_detail', service_id=service_id)

@login_required
def my_applications(request):
    """我的申请记录"""
    applications = TradeApplication.objects.filter(applicant=request.user)
    
    # 分页
    paginator = Paginator(applications, 10)
    page_number = request.GET.get('page', 1)
    page_applications = paginator.get_page(page_number)
    
    return render(request, 'services/my_applications.html', {
        'applications': page_applications
    })

@login_required
def cancel_application(request, application_id):
    """取消申请"""
    application = get_object_or_404(TradeApplication, id=application_id, applicant=request.user)
    
    if application.status not in ['pending', 'processing']:
        messages.error(request, '当前状态无法取消申请。')
    else:
        application.status = 'canceled'
        application.updated_at = timezone.now()
        application.save()
        messages.success(request, '申请已成功取消。')
    
    return redirect('services:my_applications')

# 游戏攻略相关视图
def guide_list(request):
    """攻略列表页面"""
    guides = GameGuide.objects.all()
    
    # 分类筛选
    category = request.GET.get('category', '')
    category_display = None
    if category:
        guides = guides.filter(category=category)
        # 获取分类显示名称
        for cat_code, cat_name in GameGuide.CATEGORY_CHOICES:
            if cat_code == category:
                category_display = cat_name
                break
    
    # 搜索
    search_query = request.GET.get('search', '')
    if search_query:
        guides = guides.filter(
            Q(title__icontains=search_query) | 
            Q(content__icontains=search_query)
        )
    
    # 分页
    paginator = Paginator(guides, 12)  # 每页显示12个攻略
    page_number = request.GET.get('page', 1)
    page_guides = paginator.get_page(page_number)
    
    # 获取精选攻略
    featured_guides = GameGuide.objects.filter(is_featured=True)[:3]
    
    return render(request, 'services/guide_list.html', {
        'guides': page_guides,
        'featured_guides': featured_guides,
        'category': category,
        'category_display': category_display,
        'search_query': search_query,
        'categories': GameGuide.CATEGORY_CHOICES
    })

def guide_detail(request, guide_id):
    """攻略详情页面"""
    guide = get_object_or_404(GameGuide, id=guide_id)
    
    # 增加浏览量
    guide.view_count += 1
    guide.save(update_fields=['view_count'])
    
    # 处理评论提交
    if request.method == 'POST' and request.user.is_authenticated:
        comment_content = request.POST.get('comment')
        if comment_content:
            GuideComment.objects.create(
                guide=guide,
                author=request.user,
                content=comment_content
            )
            messages.success(request, '评论发布成功！')
            return redirect('services:guide_detail', guide_id=guide.id)
    
    # 获取评论和图片
    comments = guide.comments.all()
    images = guide.images.all().order_by('order')
    
    # 获取相关攻略（同类别）
    related_guides = GameGuide.objects.filter(category=guide.category).exclude(id=guide.id)[:4]
    
    return render(request, 'services/guide_detail.html', {
        'guide': guide,
        'comments': comments,
        'images': images,
        'related_guides': related_guides
    })

@user_passes_test(lambda u: u.is_staff)
def create_guide(request):
    """创建攻略（仅管理员）"""
    if request.method == 'POST':
        title = request.POST.get('title')
        content = request.POST.get('content')
        category = request.POST.get('category')
        is_featured = request.POST.get('is_featured') == 'on'
        
        if title and content and category:
            # 创建攻略
            guide = GameGuide.objects.create(
                author=request.user,
                title=title,
                content=content,
                category=category,
                is_featured=is_featured
            )
            
            # 处理上传的图片
            images = request.FILES.getlist('images')
            captions = request.POST.getlist('captions')
            orders = request.POST.getlist('orders')
            
            for i, image in enumerate(images):
                caption = captions[i] if i < len(captions) else ""
                order = int(orders[i]) if i < len(orders) and orders[i].isdigit() else i+1
                GuideImage.objects.create(
                    guide=guide,
                    image=image,
                    caption=caption,
                    order=order
                )
            
            messages.success(request, '攻略发布成功！')
            return redirect('services:guide_detail', guide_id=guide.id)
        else:
            messages.error(request, '请填写所有必填字段！')
    
    return render(request, 'services/create_guide.html', {
        'categories': GameGuide.CATEGORY_CHOICES
    })

@user_passes_test(lambda u: u.is_staff)
def edit_guide(request, guide_id):
    """编辑攻略（仅管理员）"""
    guide = get_object_or_404(GameGuide, id=guide_id)
    
    if request.method == 'POST':
        title = request.POST.get('title')
        content = request.POST.get('content')
        category = request.POST.get('category')
        is_featured = request.POST.get('is_featured') == 'on'
        
        if title and content and category:
            # 更新攻略
            guide.title = title
            guide.content = content
            guide.category = category
            guide.is_featured = is_featured
            guide.updated_at = timezone.now()
            guide.save()
            
            # 处理上传的新图片
            images = request.FILES.getlist('new_images')
            captions = request.POST.getlist('new_captions')
            orders = request.POST.getlist('new_orders')
            
            for i, image in enumerate(images):
                if image:  # 只处理有效的图片
                    caption = captions[i] if i < len(captions) else ""
                    order = int(orders[i]) if i < len(orders) and orders[i].isdigit() else None
                    
                    if order is None:
                        # 如果没有指定顺序，使用当前最大顺序+1
                        current_max_order = guide.images.aggregate(models.Max('order'))['order__max'] or 0
                        order = current_max_order + 1
                    
                    GuideImage.objects.create(
                        guide=guide,
                        image=image,
                        caption=caption,
                        order=order
                    )
            
            # 处理现有图片的修改
            for image in guide.images.all():
                image_id = str(image.id)
                if f'delete_image_{image_id}' in request.POST:
                    image.delete()
                    continue
                
                caption = request.POST.get(f'caption_{image_id}')
                order = request.POST.get(f'order_{image_id}')
                
                if caption is not None:
                    image.caption = caption
                if order is not None and order.isdigit():
                    image.order = int(order)
                
                image.save()
            
            messages.success(request, '攻略更新成功！')
            return redirect('services:guide_detail', guide_id=guide.id)
        else:
            messages.error(request, '请填写所有必填字段！')
    
    # 获取图片
    images = guide.images.all().order_by('order')
    
    return render(request, 'services/edit_guide.html', {
        'guide': guide,
        'images': images,
        'categories': GameGuide.CATEGORY_CHOICES
    })

@user_passes_test(lambda u: u.is_staff)
def delete_guide(request, guide_id):
    """删除攻略（仅管理员）"""
    guide = get_object_or_404(GameGuide, id=guide_id)
    
    if request.method == 'POST':
        guide.delete()
        messages.success(request, '攻略已成功删除！')
        return redirect('services:guide_list')
    
    return render(request, 'services/delete_guide.html', {'guide': guide})

# 账号交易相关视图
@login_required
def account_list(request):
    """账号交易列表页面"""
    # 获取筛选参数
    account_level = request.GET.get('account_level', '')
    profession = request.GET.get('profession', '')
    server = request.GET.get('server', '')
    price_min = request.GET.get('price_min', '')
    price_max = request.GET.get('price_max', '')
    keyword = request.GET.get('q', '')
    sort = request.GET.get('sort', '-created_at')  # 默认按创建时间倒序
    
    # 只显示已激活的账号
    accounts = ServiceGameAccount.objects.filter(status='active')
    
    # 应用筛选条件
    if account_level:
        accounts = accounts.filter(account_level=account_level)
    if profession:
        accounts = accounts.filter(profession=profession)
    if server:
        accounts = accounts.filter(server__icontains=server)
    if price_min:
        try:
            price_min = float(price_min)
            accounts = accounts.filter(price__gte=price_min)
        except (ValueError, TypeError):
            pass
    if price_max:
        try:
            price_max = float(price_max)
            accounts = accounts.filter(price__lte=price_max)
        except (ValueError, TypeError):
            pass
    if keyword:
        accounts = accounts.filter(
            Q(title__icontains=keyword) | 
            Q(description__icontains=keyword) |
            Q(tags__name__icontains=keyword) |
            Q(server__icontains=keyword)
        ).distinct()
    
    # 应用排序
    if sort == 'price':
        accounts = accounts.order_by('price')
    elif sort == '-price':
        accounts = accounts.order_by('-price')
    elif sort == 'level':
        # 按庄园等级从低到高排序（因为存储的是字符串，需要转换为整数后排序）
        accounts = sorted(accounts, key=lambda x: int(x.account_level))
    elif sort == '-level':
        # 按庄园等级从高到低排序（因为存储的是字符串，需要转换为整数后排序）
        accounts = sorted(accounts, key=lambda x: -int(x.account_level))
    elif sort == 'created_at':
        accounts = accounts.order_by('created_at')
    elif sort == '-created_at':
        accounts = accounts.order_by('-created_at')
    elif sort == 'view_count':
        accounts = accounts.order_by('-view_count')
    
    # 获取精选账号
    featured_accounts = accounts.filter(is_featured=True)[:5]
    
    # 分页
    paginator = Paginator(accounts, 12)  # 每页显示12个账号
    page = request.GET.get('page')
    try:
        accounts_page = paginator.page(page)
    except PageNotAnInteger:
        # 如果页码不是整数，展示第一页
        accounts_page = paginator.page(1)
    except EmptyPage:
        # 如果页码超出范围，展示最后一页
        accounts_page = paginator.page(paginator.num_pages)
    
    # 获取账号等级和职业选择
    account_level_choices = ServiceGameAccount.ACCOUNT_LEVEL_CHOICES
    profession_choices = ServiceGameAccount.PROFESSION_CHOICES
    
    context = {
        'accounts': accounts_page,
        'featured_accounts': featured_accounts,
        'account_level_choices': account_level_choices,
        'profession_choices': profession_choices,
        'account_level': account_level,
        'profession': profession,
        'server': server,
        'price_min': price_min,
        'price_max': price_max,
        'keyword': keyword,
        'sort': sort
    }
    
    return render(request, 'services/account_list.html', context)

@login_required
def account_detail(request, account_id):
    """账号详情页面"""
    account = get_object_or_404(ServiceGameAccount, pk=account_id)
    
    # 检查用户是否为账号所有者，若不是则增加查看次数
    if account.seller != request.user:
        account.view_count += 1
        account.save()
    
    # 检查用户是否已经申请
    has_applied = False
    if request.user.is_authenticated:
        has_applied = AccountOrder.objects.filter(
            buyer=request.user,
            account=account,
            status__in=['pending', 'processing']
        ).exists()
    
    # 获取推荐账号：同游戏或同等级的其他账号
    related_accounts = ServiceGameAccount.objects.filter(
        Q(game_type=account.game_type) | Q(account_level=account.account_level),
        status='active'
    ).exclude(id=account.id).order_by('?')[:4]
    
    context = {
        'account': account,
        'has_applied': has_applied,
        'related_accounts': related_accounts
    }
    
    return render(request, 'services/account_detail.html', context)

@login_required
def sell_account(request):
    """Account selling view."""
    if request.method == 'POST':
        # Get form data
        title = request.POST.get('title')
        price = request.POST.get('price')
        description = request.POST.get('description')
        account_level = request.POST.get('account_level')
        profession = request.POST.get('profession', '')
        server = request.POST.get('server', '')
        device_type = request.POST.get('device_type', '')
        contact_type = request.POST.get('contact_type')
        contact_value = request.POST.get('contact_value')
        tags_str = request.POST.get('tags', '')
        tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]

        # Validate required fields
        if not all([title, price, description, account_level, profession, server, device_type, contact_type, contact_value]):
            messages.error(request, '请填写所有必填字段')
            return redirect('services:sell_account')

        try:
            price = float(price)
            if price <= 0:
                raise ValueError("Price must be positive")
        except ValueError:
            messages.error(request, '请输入有效的价格')
            return redirect('services:sell_account')

        # Create account
        account = ServiceGameAccount.objects.create(
            title=title,
            description=description,
            price=price,
            account_level=account_level,
            profession=profession,
            server=server,
            device_type=device_type,
            contact_type=contact_type,
            contact_value=contact_value,
            seller=request.user,
            status='pending',
            game_type='lifeafter',
            game_roles=[],
            game_items=[]
        )

        # Process tags
        for tag in tags[:5]:  # Limit to 5 tags
            AccountTag.objects.create(
                account=account,
                name=tag
            )

        # Process images
        images = request.FILES.getlist('images')
        sections = {}
        
        # Collect image sections
        for key, value in request.POST.items():
            if key.startswith('images') and 'section' in request.POST:
                file_index = key.split('_')[1]
                section = request.POST.get(f'section_{file_index}', '')
                if section:
                    sections[key] = section
        
        main_image_key = request.POST.get('main_image', '')
        main_image_set = False
        
        for i, img in enumerate(images):
            image_index = f"image_{i}"
            is_main = str(main_image_key) == str(image_index)
            section = ''
            
            # Get section from hidden fields
            for file_key in request.FILES.keys():
                if file_key.endswith(f"[data-image-index=\"{main_image_key}\"]"):
                    input_el = soup.select_one(f"input[data-image-index=\"{main_image_key}\"]")
                    if input_el and input_el.has_attr('data-section'):
                        section = input_el['data-section']
            
            caption = request.POST.get(f'captions_{i}', '')
            
            # Create image
            image = AccountImage.objects.create(
                account=account,
                image=img,
                caption=caption or '',
                is_main=is_main,
                section=section
            )
            
            if is_main:
                main_image_set = True
        
        # If no main image was set, set the first image as main
        if images and not main_image_set:
            first_image = AccountImage.objects.filter(account=account).first()
            if first_image:
                first_image.is_main = True
                first_image.save()
        
        messages.success(request, '账号信息已提交，等待审核')
        return redirect('services:my_selling_accounts')

    # Prepare form context
    context = {
        'profession_choices': ServiceGameAccount.PROFESSION_CHOICES,
        'account_level_choices': ServiceGameAccount.ACCOUNT_LEVEL_CHOICES,
        'form_data': {}
    }
    return render(request, 'services/sell_account.html', context)

@login_required
def edit_account(request, account_id):
    """Edit existing game account."""
    try:
        account = ServiceGameAccount.objects.get(id=account_id, seller=request.user)
    except ServiceGameAccount.DoesNotExist:
        messages.error(request, '账号不存在或您无权编辑')
        return redirect('services:my_selling_accounts')
    
    # Cannot edit accounts with certain statuses
    if account.status in ['sold', 'suspended']:
        messages.error(request, f'无法编辑状态为"{account.get_status_display()}"的账号')
        return redirect('services:my_selling_accounts')
    
    if request.method == 'POST':
        # Get form data
        title = request.POST.get('title')
        price = request.POST.get('price')
        description = request.POST.get('description')
        account_level = request.POST.get('account_level')
        profession = request.POST.get('profession', '')
        server = request.POST.get('server', '')
        device_type = request.POST.get('device_type', '')
        contact_type = request.POST.get('contact_type')
        contact_value = request.POST.get('contact_value')
        tags_str = request.POST.get('tags', '')
        new_tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]

        # Validate required fields
        if not all([title, price, description, account_level, profession, server, device_type, contact_type, contact_value]):
            messages.error(request, '请填写所有必填字段')
            return redirect('services:edit_account', account_id=account_id)

        try:
            price = float(price)
            if price <= 0:
                raise ValueError("Price must be positive")
        except ValueError:
            messages.error(request, '请输入有效的价格')
            return redirect('services:edit_account', account_id=account_id)

        # Update account
        account.title = title
        account.description = description
        account.price = price
        account.account_level = account_level
        account.profession = profession
        account.server = server
        account.device_type = device_type
        account.contact_type = contact_type
        account.contact_value = contact_value
        account.game_roles = []
        account.game_items = []
        
        # If account was rejected, set it back to pending for re-review
        if account.status == 'rejected':
            account.status = 'pending'
            account.rejection_reason = ''
        
        account.save()

        # Process tags - first delete existing tags
        AccountTag.objects.filter(account=account).delete()
        
        # Then add new tags
        for tag in new_tags[:5]:  # Limit to 5 tags
            AccountTag.objects.create(
                account=account,
                name=tag
            )

        # Process deleted images
        deleted_images = request.POST.getlist('deleted_images', [])
        for image_id in deleted_images:
            try:
                AccountImage.objects.filter(id=image_id, account=account).delete()
            except (ValueError, AccountImage.DoesNotExist):
                pass
                
        # Process existing images captions and sections
        for key, value in request.POST.items():
            if key.startswith('caption_'):
                try:
                    image_id = int(key.split('_')[1])
                    image = AccountImage.objects.get(id=image_id, account=account)
                    image.caption = value
                    
                    # Update section if provided
                    section_key = f'section_{image_id}'
                    if section_key in request.POST:
                        image.section = request.POST.get(section_key, '')
                    
                    image.save()
                except (ValueError, AccountImage.DoesNotExist):
                    pass
        
        # Process new images
        images = request.FILES.getlist('images')
        main_image_key = request.POST.get('main_image', '')
        sections = {}
        
        # Collect image sections
        for key, value in request.POST.items():
            if key.startswith('images') and 'section' in request.POST:
                file_index = key.split('_')[1]
                section = request.POST.get(f'section_{file_index}', '')
                if section:
                    sections[key] = section
        
        for i, img in enumerate(images):
            image_index = f"image_{i}"
            is_main = str(main_image_key) == str(image_index)
            section = ''
            
            # Get section from hidden fields
            for file_key in request.FILES.keys():
                if file_key.endswith(f"[data-image-index=\"{main_image_key}\"]"):
                    input_el = soup.select_one(f"input[data-image-index=\"{main_image_key}\"]")
                    if input_el and input_el.has_attr('data-section'):
                        section = input_el['data-section']
            
            caption = request.POST.get(f'captions_{i}', '')
            
            # Create new image
            AccountImage.objects.create(
                account=account,
                image=img,
                caption=caption or '',
                is_main=is_main,
                section=section
            )
            
            # If this is the main image, unmark all others
            if is_main:
                AccountImage.objects.filter(account=account).exclude(image=img).update(is_main=False)
        
        # If a main image is specified from existing images
        main_existing = request.POST.get('main_existing')
        if main_existing:
            try:
                main_id = int(main_existing)
                # Set the specified image as main and unmark others
                AccountImage.objects.filter(account=account).update(is_main=False)
                AccountImage.objects.filter(id=main_id, account=account).update(is_main=True)
            except ValueError:
                pass
        
        # Ensure there's a main image
        if not AccountImage.objects.filter(account=account, is_main=True).exists():
            first_image = AccountImage.objects.filter(account=account).first()
            if first_image:
                first_image.is_main = True
                first_image.save()
        
        messages.success(request, '账号信息已更新')
        return redirect('services:my_selling_accounts')
    
    # Prepare form context
    context = {
        'account': account,
        'profession_choices': ServiceGameAccount.PROFESSION_CHOICES,
        'account_level_choices': ServiceGameAccount.ACCOUNT_LEVEL_CHOICES,
        'tags': ', '.join([tag.name for tag in account.accounttag_set.all()]),
        'images': account.accountimage_set.all(),
    }
    return render(request, 'services/edit_account.html', context)

@login_required
def my_selling_accounts(request):
    """我的出售账号列表"""
    accounts = ServiceGameAccount.objects.filter(seller=request.user).order_by('-created_at')
    
    # 统计信息
    total_accounts = accounts.count()
    pending_orders = AccountOrder.objects.filter(
        account__seller=request.user,
        status='pending'
    ).count()
    
    # 分页
    paginator = Paginator(accounts, 10)  # 每页显示10个账号
    page = request.GET.get('page')
    try:
        accounts_page = paginator.page(page)
    except PageNotAnInteger:
        accounts_page = paginator.page(1)
    except EmptyPage:
        accounts_page = paginator.page(paginator.num_pages)
    
    context = {
        'accounts': accounts_page,
        'total_accounts': total_accounts,
        'pending_orders': pending_orders
    }
    
    return render(request, 'services/my_selling_accounts.html', context)

@login_required
def apply_for_account(request, account_id):
    """申请购买账号"""
    account = get_object_or_404(ServiceGameAccount, pk=account_id, status='active')
    
    # 验证用户不能购买自己的账号
    if account.seller == request.user:
        messages.error(request, '不能购买自己的账号')
        return redirect('services:account_detail', account_id=account.id)
    
    # 验证是否已有未完成的订单
    existing_order = AccountOrder.objects.filter(
        buyer=request.user,
        account=account,
        status__in=['pending', 'processing']
    ).first()
    
    if existing_order:
        messages.warning(request, '您已经申请过该账号，请在"我的购买申请"中查看')
        return redirect('services:my_account_orders')
    
    if request.method == 'POST':
        contact_method = request.POST.get('contact_type')
        contact_info = request.POST.get('contact_value')
        message = request.POST.get('message', '')
        agreed = request.POST.get('agree_terms') == 'on'
        
        if not all([contact_method, contact_info, agreed]):
            messages.error(request, '请填写所有必填字段并同意交易条款')
            return redirect('services:apply_for_account', account_id=account.id)
        
        # 创建订单
        order = AccountOrder.objects.create(
            buyer=request.user,
            account=account,
            price=account.price,  # 从账号对象获取价格
            contact_type=contact_method,
            contact_value=contact_info,
            message=message,
            status='pending'
        )
        
        # 向管理员发送通知，而不是卖家
        # 查找所有管理员用户
        User = get_user_model()
        admin_users = User.objects.filter(is_staff=True)
        
        for admin_user in admin_users:
            Notification.objects.create(
                user=admin_user,
                title='新的账号购买申请',
                content=f'买家 {request.user.get_display_name()} 申请购买账号 "{account.title}"，请尽快处理',
                link=f'/services/admin/order/{order.id}/'
            )
        
        messages.success(request, '申请提交成功，请等待管理员审核并联系您')
        return redirect('services:my_account_orders')
    
    context = {
        'account': account
    }
    
    return render(request, 'services/apply_for_account.html', context)

@login_required
def my_account_orders(request):
    """我的账号购买申请"""
    orders = AccountOrder.objects.filter(buyer=request.user).order_by('-created_at')
    
    # 分页
    paginator = Paginator(orders, 10)  # 每页显示10个申请
    page = request.GET.get('page')
    try:
        orders_page = paginator.page(page)
    except PageNotAnInteger:
        orders_page = paginator.page(1)
    except EmptyPage:
        orders_page = paginator.page(paginator.num_pages)
    
    context = {
        'orders': orders_page
    }
    
    return render(request, 'services/my_account_orders.html', context)

@login_required
def cancel_account_order(request, order_id):
    """取消账号购买申请"""
    order = get_object_or_404(AccountOrder, pk=order_id, buyer=request.user)
    
    # 只能取消待处理的申请
    if order.status != 'pending':
        messages.error(request, '只能取消待处理的申请')
        return redirect('services:my_account_orders')
    
    order.status = 'cancelled'
    order.save()
    
    # 向卖家发送通知
    notification = Notification.objects.create(
        user=order.account.seller,
        title='购买申请已取消',
        content=f'买家已取消对账号"{order.account.title}"的购买申请。',
        link=f'/services/accounts/orders/seller/'
    )
    
    messages.success(request, '申请已取消')
    return redirect('services:my_account_orders')

@login_required
def account_orders_seller(request):
    """卖家查看所有账号订单"""
    # 获取当前用户作为卖家的所有账号ID列表
    account_ids = request.user.selling_accounts.values_list('id', flat=True)
    
    # 获取这些账号的所有订单
    orders = AccountOrder.objects.filter(account__id__in=account_ids)
    
    # 根据状态过滤
    status_filter = request.GET.get('status')
    if status_filter and status_filter != 'all':
        orders = orders.filter(status=status_filter)
    
    # 根据账号过滤
    account_filter = request.GET.get('account')
    if account_filter and account_filter.isdigit():
        orders = orders.filter(account__id=int(account_filter))
    
    # 排序
    sort_by = request.GET.get('sort', '-created_at')
    orders = orders.order_by(sort_by)
    
    # 每页10条数据
    paginator = Paginator(orders, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 统计信息
    pending_count = orders.filter(status='pending').count()
    processing_count = orders.filter(status='processing').count()
    
    # 获取用户的所有在售账号，用于过滤
    selling_accounts = request.user.selling_accounts.all()
    
    context = {
        'page_obj': page_obj,
        'pending_count': pending_count,
        'processing_count': processing_count,
        'selling_accounts': selling_accounts,
        'status_filter': status_filter,
        'account_filter': account_filter,
        'sort_by': sort_by
    }
    
    return render(request, 'services/account_orders_seller.html', context)

@login_required
def process_order(request, order_id):
    """处理账号订单，允许卖家接受、拒绝或标记完成订单"""
    order = get_object_or_404(AccountOrder, id=order_id)
    
    # 确保只有账号所有者可以处理订单
    if order.account.seller != request.user:
        messages.error(request, "您无权处理此订单")
        return redirect('services:account_orders_seller')
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'accept' and order.status == 'pending':
            # 接受申请
            order.status = 'processing'
            order.handled_by = request.user
            order.save()
            
            # 将账号状态更新为已预订
            order.account.status = 'reserved'
            order.account.save()
            
            # 通知买家和卖家
            Notification.objects.create(
                user=order.buyer,
                title='您的购买申请已接受',
                content=f'您对账号"{order.account.title}"的购买申请已被管理员接受，请等待管理员与您联系。',
                link=f'/services/accounts/orders/'
            )
            
            Notification.objects.create(
                user=order.account.seller,
                title='您的账号有买家申请购买',
                content=f'您的账号"{order.account.title}"有买家申请购买，管理员已接受此申请，请等待管理员与您联系。',
                link=f'/services/accounts/my-selling/'
            )
            
            messages.success(request, f"已成功接受申请 #{order.id}")
            
        elif action == 'reject' and order.status == 'pending':
            # 拒绝申请
            reason = request.POST.get('reason', '')
            order.status = 'rejected'
            order.reject_reason = reason
            order.handled_by = request.user
            order.save()
            
            # 通知买家
            Notification.objects.create(
                user=order.buyer,
                title='您的购买申请已被拒绝',
                content=f'您对账号"{order.account.title}"的购买申请已被管理员拒绝。原因: {reason or "无"}',
                link=f'/services/accounts/orders/'
            )
            
            messages.success(request, f"已成功拒绝申请 #{order.id}")
            
        elif action == 'complete' and order.status == 'processing':
            # 完成交易
            order.status = 'completed'
            order.completed_at = timezone.now()
            order.handled_by = request.user
            order.save()
            
            # 将账号状态更新为已售出
            order.account.status = 'sold'
            order.account.save()
            
            # 取消所有其他针对此账号的待处理申请
            other_orders = AccountOrder.objects.filter(
                account=order.account, 
                status='pending'
            )
            
            for other_order in other_orders:
                other_order.status = 'cancelled'
                other_order.save()
                
                # 通知其他买家
                Notification.objects.create(
                    user=other_order.buyer,
                    title='您的购买申请已被取消',
                    content=f'您对账号"{order.account.title}"的购买申请已被取消，因为该账号已售出。',
                    link=f'/services/accounts/orders/'
                )
            
            # 通知买家和卖家
            Notification.objects.create(
                user=order.buyer,
                title='您的购买已完成',
                content=f'您对账号"{order.account.title}"的购买已完成。',
                link=f'/services/accounts/orders/'
            )
            
            Notification.objects.create(
                user=order.account.seller,
                title='您的账号已售出',
                content=f'您的账号"{order.account.title}"已成功售出。',
                link=f'/services/accounts/my-selling/'
            )
            
            messages.success(request, f"交易 #{order.id} 已完成")
        
        return redirect('services:admin_account_orders')
    
    return render(request, 'services/process_order.html', {'order': order})

@login_required
def activate_account(request, account_id):
    """重新上架账号"""
    account = get_object_or_404(ServiceGameAccount, pk=account_id, seller=request.user)
    
    # 只能上架已下架的账号
    if account.status != 'inactive':
        messages.error(request, '只能上架已下架的账号')
        return redirect('services:my_selling_accounts')
    
    account.status = 'active'
    account.save()
    
    messages.success(request, '账号已重新上架')
    return redirect('services:my_selling_accounts')

@login_required
def deactivate_account(request, account_id):
    """下架账号"""
    account = get_object_or_404(ServiceGameAccount, pk=account_id, seller=request.user)
    
    # 只能下架已上架的账号
    if account.status != 'active':
        messages.error(request, '只能下架已上架的账号')
        return redirect('services:my_selling_accounts')
    
    account.status = 'inactive'
    account.save()
    
    messages.success(request, '账号已下架')
    return redirect('services:my_selling_accounts')

@login_required
def delete_account(request, account_id):
    """删除账号"""
    account = get_object_or_404(ServiceGameAccount, pk=account_id, seller=request.user)
    
    # 不能删除已售出或已预订的账号
    if account.status in ['sold', 'reserved']:
        messages.error(request, '不能删除已售出或已预订的账号')
        return redirect('services:my_selling_accounts')
    
    # 删除账号
    account.delete()
    
    messages.success(request, '账号已删除')
    return redirect('services:my_selling_accounts')

@staff_member_required
def admin_account_review(request):
    """管理员审核账号列表"""
    accounts = ServiceGameAccount.objects.filter(status='pending').order_by('created_at')
    
    # 分页
    paginator = Paginator(accounts, 10)  # 每页显示10个账号
    page = request.GET.get('page')
    try:
        accounts_page = paginator.page(page)
    except PageNotAnInteger:
        accounts_page = paginator.page(1)
    except EmptyPage:
        accounts_page = paginator.page(paginator.num_pages)
    
    context = {
        'accounts': accounts_page
    }
    
    return render(request, 'services/admin_account_review.html', context)

@staff_member_required
def review_account(request, account_id):
    """审核账号"""
    account = get_object_or_404(ServiceGameAccount, pk=account_id)
    
    # 只能审核待审核的账号
    if account.status != 'pending':
        messages.error(request, '只能审核待审核的账号')
        return redirect('services:admin_account_review')
    
    if request.method == 'POST':
        # 添加调试消息
        print(f"接收到POST请求：{request.POST}")
        action = request.POST.get('action')
        print(f"Action: {action}")
        
        if action == 'approve':
            account.status = 'active'
            account.reviewed_by = request.user
            account.review_at = timezone.now()
            account.save()
            
            try:
                # 向卖家发送通知
                notification = Notification.objects.create(
                    user=account.seller,
                    title='账号审核通过',
                    content=f'您的账号"{account.title}"已通过审核，现已上架。',
                    link=f'/services/accounts/{account.id}/'
                )
                print(f"通知创建成功: {notification}")
            except Exception as e:
                print(f"创建通知时出错: {e}")
            
            messages.success(request, '账号已通过审核并上架')
        
        elif action == 'reject':
            reason = request.POST.get('reason', '')
            if not reason:
                messages.error(request, '拒绝审核需要提供拒绝原因')
                return redirect('services:review_account', account_id=account.id)
            
            account.status = 'rejected'
            account.reject_reason = reason
            account.reviewed_by = request.user
            account.review_at = timezone.now()
            account.save()
            
            try:
                # 向卖家发送通知
                notification = Notification.objects.create(
                    user=account.seller,
                    title='账号审核未通过',
                    content=f'您的账号"{account.title}"未通过审核。原因: {reason}',
                    link=f'/services/accounts/{account.id}/edit/'
                )
            except Exception as e:
                print(f"创建通知时出错: {e}")
            
            messages.success(request, '账号审核已拒绝')
        
        return redirect('services:admin_account_review')
    
    # 获取卖家账号统计信息
    seller_account_count = ServiceGameAccount.objects.filter(seller=account.seller).count()
    
    # 准备图片数据为JSON格式
    account_images = []
    for image in account.images.all():
        account_images.append({
            'url': image.image.url,
            'caption': image.caption or ''
        })
    
    context = {
        'account': account,
        'seller_account_count': seller_account_count,
        'account_images_json': json.dumps(account_images)
    }
    
    return render(request, 'services/review_account.html', context)

@staff_member_required
def admin_view_order(request, order_id):
    """管理员查看订单详情，包括联系方式"""
    order = get_object_or_404(AccountOrder, id=order_id)
    
    context = {
        'order': order,
        'title': f'订单详情 #{order.id}'
    }
    
    return render(request, 'admin/order_detail.html', context)

@staff_member_required
def admin_account_orders(request):
    """管理员查看所有账号订单"""
    # 获取所有订单用于统计
    all_orders = AccountOrder.objects.all()
    
    # 用于显示的订单（可能会被筛选）
    orders = all_orders.order_by('-created_at')
    
    # 根据状态过滤
    status_filter = request.GET.get('status')
    if status_filter and status_filter != 'all':
        orders = orders.filter(status=status_filter)
        
    # 搜索功能
    search_query = request.GET.get('search', '')
    if search_query:
        orders = orders.filter(
            Q(buyer__username__icontains=search_query) |
            Q(account__title__icontains=search_query) |
            Q(buyer__email__icontains=search_query) |
            Q(account__seller__username__icontains=search_query) |
            Q(account__seller__email__icontains=search_query)
        ).distinct()
    
    # 分页
    paginator = Paginator(orders, 20)  # 每页显示20个订单
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 统计信息 - 使用所有订单计算，不受筛选影响
    pending_count = all_orders.filter(status='pending').count()
    processing_count = all_orders.filter(status='processing').count()
    completed_count = all_orders.filter(status='completed').count()
    
    context = {
        'page_obj': page_obj,
        'pending_count': pending_count,
        'processing_count': processing_count,
        'completed_count': completed_count,
        'status_filter': status_filter,
        'search_query': search_query
    }
    
    return render(request, 'services/admin_orders.html', context)

@staff_member_required
def admin_process_order(request, order_id):
    """管理员处理订单"""
    order = get_object_or_404(AccountOrder, id=order_id)
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'accept' and order.status == 'pending':
            # 接受申请
            order.status = 'processing'
            order.handled_by = request.user
            order.save()
            
            # 将账号状态更新为已预订
            order.account.status = 'reserved'
            order.account.save()
            
            # 通知买家和卖家
            Notification.objects.create(
                user=order.buyer,
                title='您的购买申请已接受',
                content=f'您对账号"{order.account.title}"的购买申请已被管理员接受，请等待管理员与您联系。',
                link=f'/services/accounts/orders/'
            )
            
            Notification.objects.create(
                user=order.account.seller,
                title='您的账号有买家申请购买',
                content=f'您的账号"{order.account.title}"有买家申请购买，管理员已接受此申请，请等待管理员与您联系。',
                link=f'/services/accounts/my-selling/'
            )
            
            messages.success(request, f"已成功接受申请 #{order.id}")
            
        elif action == 'reject' and order.status == 'pending':
            # 拒绝申请
            reason = request.POST.get('reason', '')
            order.status = 'rejected'
            order.reject_reason = reason
            order.handled_by = request.user
            order.save()
            
            # 通知买家
            Notification.objects.create(
                user=order.buyer,
                title='您的购买申请已被拒绝',
                content=f'您对账号"{order.account.title}"的购买申请已被管理员拒绝。原因: {reason or "无"}',
                link=f'/services/accounts/orders/'
            )
            
            messages.success(request, f"已成功拒绝申请 #{order.id}")
            
        elif action == 'complete' and order.status == 'processing':
            # 完成交易
            order.status = 'completed'
            order.completed_at = timezone.now()
            order.handled_by = request.user
            order.save()
            
            # 将账号状态更新为已售出
            order.account.status = 'sold'
            order.account.save()
            
            # 取消所有其他针对此账号的待处理申请
            other_orders = AccountOrder.objects.filter(
                account=order.account, 
                status='pending'
            )
            
            for other_order in other_orders:
                other_order.status = 'cancelled'
                other_order.save()
                
                # 通知其他买家
                Notification.objects.create(
                    user=other_order.buyer,
                    title='您的购买申请已被取消',
                    content=f'您对账号"{order.account.title}"的购买申请已被取消，因为该账号已售出。',
                    link=f'/services/accounts/orders/'
                )
            
            # 通知买家和卖家
            Notification.objects.create(
                user=order.buyer,
                title='您的购买已完成',
                content=f'您对账号"{order.account.title}"的购买已完成。',
                link=f'/services/accounts/orders/'
            )
            
            Notification.objects.create(
                user=order.account.seller,
                title='您的账号已售出',
                content=f'您的账号"{order.account.title}"已成功售出。',
                link=f'/services/accounts/my-selling/'
            )
            
            messages.success(request, f"交易 #{order.id} 已完成")
        
        return redirect('services:admin_account_orders')
    
    return render(request, 'admin/process_order.html', {'order': order})

@user_passes_test(lambda u: u.is_staff)
def admin_dashboard(request):
    """管理员仪表盘视图，显示系统统计信息和快速入口"""
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    # 统计数据
    pending_accounts_count = ServiceGameAccount.objects.filter(status='pending').count()
    pending_orders_count = AccountOrder.objects.filter(status='pending').count()
    processing_orders_count = AccountOrder.objects.filter(status='processing').count()
    users_count = User.objects.count()
    
    context = {
        'pending_accounts_count': pending_accounts_count,
        'pending_orders_count': pending_orders_count,
        'processing_orders_count': processing_orders_count,
        'users_count': users_count,
    }
    
    return render(request, 'services/admin_dashboard.html', context)

@user_passes_test(lambda u: u.is_staff)
def admin_user_management(request):
    """管理员用户管理视图"""
    from django.contrib.auth import get_user_model
    from django.db.models import Q
    
    User = get_user_model()
    
    # 获取所有用户
    users = User.objects.all().order_by('-date_joined')
    
    # 搜索功能
    search_query = request.GET.get('search', '')
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(nickname__icontains=search_query) |
            Q(phone__icontains=search_query)
        ).distinct()
    
    # 过滤功能
    user_type = request.GET.get('type', '')
    if user_type == 'staff':
        users = users.filter(is_staff=True)
    elif user_type == 'normal':
        users = users.filter(is_staff=False)
    elif user_type == 'verified':
        users = users.filter(is_verified=True)
    elif user_type == 'unverified':
        users = users.filter(is_verified=False)
    
    # 分页
    paginator = Paginator(users, 10)  # 每页显示10条
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 统计信息
    total_users = User.objects.count()
    staff_users = User.objects.filter(is_staff=True).count()
    verified_users = User.objects.filter(is_verified=True).count()
    
    context = {
        'page_obj': page_obj,
        'total_users': total_users,
        'staff_users': staff_users,
        'verified_users': verified_users,
        'search_query': search_query,
        'user_type': user_type
    }
    
    return render(request, 'services/admin_users.html', context)

@user_passes_test(lambda u: u.is_staff)
def edit_user_permissions(request, user_id):
    """编辑用户权限视图"""
    from django.contrib.auth import get_user_model
    from django.contrib.auth.models import Permission
    from django.utils.translation import gettext as _
    
    User = get_user_model()
    target_user = get_object_or_404(User, id=user_id)
    
    # 检查权限（只有超级管理员可以编辑其他管理员的权限）
    if target_user.is_staff and not request.user.is_superuser:
        messages.error(request, '您没有权限修改其他管理员的权限')
        return redirect('services:admin_user_management')
    
    if request.method == 'POST':
        # 更新管理员状态
        is_staff = request.POST.get('is_staff') == 'on'
        target_user.is_staff = is_staff
        
        # 更新权限
        selected_permissions = request.POST.getlist('permissions')
        
        # 清空现有权限并添加新选择的权限
        target_user.user_permissions.clear()
        
        if selected_permissions:
            permissions = Permission.objects.filter(id__in=selected_permissions)
            target_user.user_permissions.add(*permissions)
        
        target_user.save()
        
        messages.success(request, f'用户 {target_user.username} 的权限已更新')
        return redirect('services:admin_user_management')
    
    # 获取所有权限并按应用分组
    permissions = Permission.objects.all().select_related('content_type')
    permissions_by_app = {}
    
    # 中文应用名称映射
    app_names = {
        'auth': '用户认证',
        'accounts': '账号管理',
        'admin': '后台管理',
        'contenttypes': '内容类型',
        'sessions': '会话管理',
        'services': '服务管理',
        'trades': '交易管理',
        'games': '游戏管理',
    }
    
    # 模型名称映射
    model_names = {
        'user': '用户',
        'permission': '权限',
        'group': '用户组',
        'contenttype': '内容类型',
        'session': '会话',
        'notification': '通知',
        'servicegameaccount': '游戏账号',
        'accountorder': '账号订单',
        'accounttag': '账号标签',
        'accountimage': '账号图片',
        'gamequestion': '游戏问题',
        'questionanswer': '问题回答',
        'gameguide': '游戏攻略',
        'guideimage': '攻略图片',
        'guidecomment': '攻略评论',
        'communitypost': '社区帖子',
        'postcomment': '帖子评论',
        'wishlistservice': '心愿服务',
        'dispute': '交易纠纷',
        'tradeapplication': '交易申请',
    }
    
    # 动作描述映射
    action_desc = {
        'add': {'name': '新增', 'desc': '可以创建新的'},
        'change': {'name': '修改', 'desc': '可以编辑已有的'},
        'delete': {'name': '删除', 'desc': '可以删除已有的'},
        'view': {'name': '查看', 'desc': '可以查看所有'}
    }
    
    for permission in permissions:
        app_label = permission.content_type.app_label
        model_name = permission.content_type.model
        
        # 获取应用的中文名称
        app_chinese = app_names.get(app_label, app_label.title())
        
        if app_chinese not in permissions_by_app:
            permissions_by_app[app_chinese] = []
        
        # 解析权限代码
        codename_parts = permission.codename.split('_')
        action = codename_parts[0]
        
        # 获取模型的中文名称
        model_chinese = model_names.get(model_name, model_name.replace('_', ' ').title())
        
        # 获取动作的中文描述
        action_info = action_desc.get(action, {'name': action, 'desc': '可以'})
        
        # 生成友好的权限名称和描述
        friendly_name = f"{action_info['name']}{model_chinese}"
        description = f"{action_info['desc']}{model_chinese}"
        
        permissions_by_app[app_chinese].append({
            'id': permission.id,
            'name': friendly_name,
            'description': description,
            'codename': permission.codename,
        })
    
    # 获取用户当前权限ID列表
    user_permissions = list(target_user.user_permissions.values_list('id', flat=True))
    
    # 为每个应用的权限列表按动作优先级排序（查看、新增、修改、删除）
    action_priority = {'view': 1, 'add': 2, 'change': 3, 'delete': 4}
    
    for app, perms in permissions_by_app.items():
        permissions_by_app[app] = sorted(perms, key=lambda p: (
            action_priority.get(p['codename'].split('_')[0], 99), 
            p['name']
        ))
    
    # 按照应用名称字母顺序排序，但将"用户认证"和"账号管理"放在最前面
    ordered_permissions = {}
    priority_apps = ['用户认证', '账号管理', '服务管理', '交易管理']
    
    # 先添加优先应用
    for app in priority_apps:
        if app in permissions_by_app:
            ordered_permissions[app] = permissions_by_app.pop(app)
    
    # 再添加其他应用（按字母排序）
    for app in sorted(permissions_by_app.keys()):
        ordered_permissions[app] = permissions_by_app[app]
    
    context = {
        'user': target_user,
        'permissions_by_app': ordered_permissions,
        'user_permissions': user_permissions,
    }
    
    return render(request, 'services/admin_user_permissions.html', context)

@user_passes_test(lambda u: u.is_staff)
def admin_system_settings(request):
    """系统设置页面视图"""
    from django.utils import timezone
    
    # 提供系统当前时间
    now = timezone.now()
    
    context = {
        'now': now,
    }
    
    return render(request, 'services/admin_dashboard_system.html', context)

@staff_member_required
def delete_user(request, user_id):
    """管理员删除用户功能"""
    from django.contrib.auth import get_user_model
    from django.db.models import ProtectedError
    from django.db import transaction
    User = get_user_model()
    
    user = get_object_or_404(User, id=user_id)
    
    # 阻止删除超级用户
    if user.is_superuser and not request.user.is_superuser:
        messages.error(request, '您没有权限删除超级用户。')
        return redirect('services:admin_user_management')
    
    # 防止自删
    if user == request.user:
        messages.error(request, '您不能删除您自己的账号。')
        return redirect('services:admin_user_management')
    
    # 检查关联数据
    has_accounts = ServiceGameAccount.objects.filter(seller=user).exists()
    has_orders = AccountOrder.objects.filter(Q(buyer=user) | Q(account__seller=user)).exists()
    
    # 检查用户是否有活跃交易
    active_orders = AccountOrder.objects.filter(
        Q(buyer=user) | Q(account__seller=user),
        status__in=['pending', 'processing']
    )
    has_active_orders = active_orders.exists()
    active_orders_count = active_orders.count()
    
    # 获取活跃交易的详细信息
    active_orders_details = []
    if has_active_orders:
        for order in active_orders:
            active_orders_details.append({
                'id': order.id,
                'account': order.account.title,
                'status': order.get_status_display(),
                'created_at': order.created_at,
                'is_buyer': order.buyer == user,
                'is_seller': order.account.seller == user,
                'price': order.price
            })
    
    context = {
        'user_to_delete': user,
        'has_accounts': has_accounts,
        'has_orders': has_orders,
        'has_active_orders': has_active_orders,
        'active_orders_count': active_orders_count,
        'active_orders_details': active_orders_details
    }
    
    if request.method == 'POST':
        confirm_delete_data = request.POST.get('confirm_delete_data') == 'on'
        force_delete_active = request.POST.get('force_delete_active') == 'on'
        
        # 如果有活跃交易但没有勾选强制删除，阻止操作
        if has_active_orders and not force_delete_active:
            messages.error(request, '此用户有正在进行的交易。请勾选"我已确认并强制删除活跃交易"以继续。')
            return render(request, 'services/admin_delete_user.html', context)
        
        if has_accounts or has_orders:
            if not confirm_delete_data:
                messages.error(request, '此用户有关联的游戏账号或订单。请勾选"同时删除所有关联数据"以确认删除。')
                return render(request, 'services/admin_delete_user.html', context)
            
            try:
                with transaction.atomic():
                    # 删除用户的账号订单
                    AccountOrder.objects.filter(buyer=user).delete()
                    
                    # 删除用户发布的游戏账号及其相关订单
                    accounts = ServiceGameAccount.objects.filter(seller=user)
                    for account in accounts:
                        AccountOrder.objects.filter(account=account).delete()
                    accounts.delete()
                    
                    # 删除用户
                    user.delete()
                    
                messages.success(request, f'用户 {user.username} 及其所有关联数据已被成功删除。')
            except ProtectedError as e:
                messages.error(request, f'无法删除此用户，因为还有其他关联数据未处理: {str(e)}')
                return render(request, 'services/admin_delete_user.html', context)
        else:
            # 没有关联数据，直接删除
            user.delete()
            messages.success(request, f'用户 {user.username} 已被成功删除。')
        
        return redirect('services:admin_user_management')
    
    return render(request, 'services/admin_delete_user.html', context)


def popular_servers_api(request):
    """热门区服API接口"""
    try:
        # 统计每个区服的账号数量，排除空的区服名称
        server_stats = (ServiceGameAccount.objects
                       .filter(server__isnull=False, server__gt='', status='active')
                       .values('server')
                       .annotate(account_count=Count('id'))
                       .order_by('-account_count')[:10])  # 取前10个热门区服

        # 构建返回数据
        servers = []
        for i, stat in enumerate(server_stats):
            servers.append({
                'name': stat['server'],
                'account_count': stat['account_count'],
                'is_hot': i < 3  # 前3名标记为热门
            })

        # 如果没有数据，返回一些示例数据
        if not servers:
            servers = [
                {'name': '多人协作', 'account_count': 15, 'is_hot': True},
                {'name': '新手村', 'account_count': 12, 'is_hot': True},
                {'name': '废土世界', 'account_count': 8, 'is_hot': True},
                {'name': '庄园建造', 'account_count': 6, 'is_hot': False},
                {'name': '生存挑战', 'account_count': 4, 'is_hot': False},
            ]

        return JsonResponse({
            'servers': servers,
            'last_updated': timezone.now().isoformat(),
            'success': True
        })

    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'success': False
        }, status=500)
