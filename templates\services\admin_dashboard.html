{% extends "base.html" %}

{% block title %}管理员仪表盘 | 梦羽明日之后{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    .dashboard-header {
        background: linear-gradient(135deg, #3494e6, #ec6ead);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    .dashboard-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
    }
    .dashboard-header p {
        margin: 10px 0 0;
        opacity: 0.9;
    }
    
    .stats-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 40px;
    }
    
    .stat-card {
        flex: 1;
        min-width: 240px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.12);
    }
    
    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        z-index: 1;
        position: relative;
    }
    
    .stat-value {
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
    }
    
    .stat-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #555;
        position: relative;
        z-index: 1;
    }
    
    .stat-link {
        display: inline-block;
        padding: 10px 20px;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s;
        position: relative;
        z-index: 1;
        border: none;
    }
    
    .stat-link:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    /* 统计卡片类型 */
    .pending-accounts-card {
        background: linear-gradient(135deg, #ffffff, #f9f4ff);
        border-left: 5px solid #8e44ad;
    }
    
    .pending-accounts-card .stat-icon {
        color: #8e44ad;
    }
    
    .pending-accounts-card .stat-link {
        background-color: #8e44ad;
        color: white;
    }
    
    .pending-orders-card {
        background: linear-gradient(135deg, #ffffff, #fff4e3);
        border-left: 5px solid #f39c12;
    }
    
    .pending-orders-card .stat-icon {
        color: #f39c12;
    }
    
    .pending-orders-card .stat-link {
        background-color: #f39c12;
        color: white;
    }
    
    .processing-orders-card {
        background: linear-gradient(135deg, #ffffff, #e3f6ff);
        border-left: 5px solid #3498db;
    }
    
    .processing-orders-card .stat-icon {
        color: #3498db;
    }
    
    .processing-orders-card .stat-link {
        background-color: #3498db;
        color: white;
    }
    
    .users-card {
        background: linear-gradient(135deg, #ffffff, #e8fff1);
        border-left: 5px solid #2ecc71;
    }
    
    .users-card .stat-icon {
        color: #2ecc71;
    }
    
    .users-card .stat-link {
        background-color: #2ecc71;
        color: white;
    }
    
    /* 模块卡片 */
    .section-title {
        font-size: 1.5rem;
        margin-bottom: 25px;
        font-weight: 700;
        color: #333;
        position: relative;
        padding-left: 15px;
        border-left: 4px solid #3498db;
    }
    
    .modules-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }
    
    .module-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        padding: 25px;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
    }
    
    .module-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .module-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .module-icon {
        font-size: 1.8rem;
        margin-right: 15px;
        background-color: #f8f9fa;
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
    
    .account-review-icon { color: #8e44ad; background-color: #f9f4ff; }
    .order-management-icon { color: #f39c12; background-color: #fff4e3; }
    .qa-icon { color: #3498db; background-color: #e3f6ff; }
    .guide-icon { color: #2ecc71; background-color: #e8fff1; }
    .system-icon { color: #e74c3c; background-color: #ffe3e3; }
    .notification-icon { color: #1abc9c; background-color: #e3fffc; }
    
    .module-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
    }
    
    .module-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.5;
        flex-grow: 1;
    }
    
    .module-link {
        display: inline-block;
        padding: 10px 20px;
        background-color: #f8f9fa;
        color: #333;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.2s;
        text-align: center;
        border: 1px solid #e9ecef;
    }
    
    .module-link:hover {
        background-color: #e9ecef;
        color: #333;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container py-4">
    <div class="dashboard-header">
        <h1>管理员仪表盘</h1>
        <p>欢迎回来，{{ request.user.get_display_name }}！这里是系统管理面板，您可以管理账号审核、交易订单和其他管理功能。</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-row">
        <div class="stat-card pending-accounts-card">
            <div class="stat-icon">
                <i class="bi bi-clipboard-check"></i>
            </div>
            <div class="stat-value">{{ pending_accounts_count|default:"0" }}</div>
            <div class="stat-title">待审核账号</div>
            <a href="{% url 'services:admin_account_review' %}" class="stat-link">审核账号</a>
        </div>
        
        <div class="stat-card pending-orders-card">
            <div class="stat-icon">
                <i class="bi bi-cart"></i>
            </div>
            <div class="stat-value">{{ pending_orders_count|default:"0" }}</div>
            <div class="stat-title">待处理订单</div>
            <a href="{% url 'services:admin_account_orders' %}" class="stat-link">管理订单</a>
        </div>
        
        <div class="stat-card processing-orders-card">
            <div class="stat-icon">
                <i class="bi bi-arrow-repeat"></i>
            </div>
            <div class="stat-value">{{ processing_orders_count|default:"0" }}</div>
            <div class="stat-title">处理中订单</div>
            <a href="{% url 'services:admin_account_orders' %}?status=processing" class="stat-link">查看详情</a>
        </div>
        
        <div class="stat-card users-card">
            <div class="stat-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stat-value">{{ users_count|default:"0" }}</div>
            <div class="stat-title">用户总数</div>
            <a href="{% url 'services:admin_user_management' %}" class="stat-link">管理用户</a>
        </div>
    </div>
    
    <!-- 管理功能 -->
    <h2 class="section-title">管理功能</h2>
    
    <div class="modules-grid">
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon account-review-icon">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="module-title">账号审核</div>
            </div>
            <div class="module-description">审核用户提交的游戏账号信息，通过或拒绝上架申请。</div>
            <a href="{% url 'services:admin_account_review' %}" class="module-link">进入管理</a>
        </div>
        
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon order-management-icon">
                    <i class="bi bi-bag-check"></i>
                </div>
                <div class="module-title">订单管理</div>
            </div>
            <div class="module-description">管理账号交易订单，查看买家卖家信息，协助完成交易。</div>
            <a href="{% url 'services:admin_account_orders' %}" class="module-link">进入管理</a>
        </div>
        
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon users-card">
                    <i class="bi bi-people"></i>
                </div>
                <div class="module-title">用户管理</div>
            </div>
            <div class="module-description">管理平台用户账号，查看用户信息，设置用户权限。</div>
            <a href="{% url 'services:admin_user_management' %}" class="module-link">进入管理</a>
        </div>
        
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon qa-icon">
                    <i class="bi bi-question-circle"></i>
                </div>
                <div class="module-title">问答管理</div>
            </div>
            <div class="module-description">管理用户提问，回复常见问题，维护游戏问答专区。</div>
            <a href="/services/admin/questions/" class="module-link">进入管理</a>
        </div>
        
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon guide-icon">
                    <i class="bi bi-journal-text"></i>
                </div>
                <div class="module-title">攻略管理</div>
            </div>
            <div class="module-description">管理游戏攻略内容，添加、编辑或删除攻略文章。</div>
            <a href="/services/guides/create/" class="module-link">进入管理</a>
        </div>
        
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon system-icon">
                    <i class="bi bi-gear"></i>
                </div>
                <div class="module-title">系统设置</div>
            </div>
            <div class="module-description">管理网站设置，包括权限、通知和其他系统参数。</div>
            <a href="{% url 'services:admin_system_settings' %}" class="module-link">进入管理</a>
        </div>
        
        <div class="module-card">
            <div class="module-header">
                <div class="module-icon notification-icon">
                    <i class="bi bi-bell"></i>
                </div>
                <div class="module-title">通知管理</div>
            </div>
            <div class="module-description">管理系统通知，发布公告，与用户进行沟通。</div>
            <a href="/admin/accounts/notification/" class="module-link">进入管理</a>
        </div>
    </div>
</div>
{% endblock %} 