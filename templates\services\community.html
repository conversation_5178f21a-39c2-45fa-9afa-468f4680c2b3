{% extends 'base.html' %}

{% block title %}玩家社区 - 梦羽明日之后{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>玩家社区</h1>
        {% if user.is_authenticated %}
        <a href="{% url 'services:create_post' %}" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> 发布新帖子
        </a>
        {% endif %}
    </div>

    <!-- 搜索框 -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body">
            <form method="get" action="{% url 'services:community' %}" class="d-flex">
                <input type="text" name="search" class="form-control me-2" placeholder="搜索帖子..." value="{{ search_query }}">
                <button type="submit" class="btn btn-outline-primary">搜索</button>
            </form>
        </div>
    </div>

    <!-- 帖子列表 -->
    {% if posts %}
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="list-group list-group-flush">
                        {% for post in posts %}
                            <a href="{% url 'services:post_detail' post_id=post.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <h5 class="mb-1">
                                        {% if post.is_pinned %}
                                        <span class="badge bg-danger me-2">置顶</span>
                                        {% endif %}
                                        {{ post.title }}
                                    </h5>
                                    <small>{{ post.created_at|date:"Y-m-d H:i" }}</small>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <small class="text-muted">
                                            <i class="bi bi-person"></i> {{ post.author.username }}
                                        </small>
                                        <small class="text-muted ms-3">
                                            <i class="bi bi-chat"></i> {{ post.comments.count }} 评论
                                        </small>
                                        <small class="text-muted ms-3">
                                            <i class="bi bi-eye"></i> {{ post.view_count }} 浏览
                                        </small>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        {% if posts.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if posts.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ posts.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&laquo;</a>
                </li>
                {% endif %}

                {% for i in posts.paginator.page_range %}
                    {% if posts.number == i %}
                    <li class="page-item active" aria-current="page">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if posts.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ posts.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">&raquo;</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <div class="display-1 text-muted mb-4">
                <i class="bi bi-clipboard"></i>
            </div>
            <h3 class="text-muted mb-3">没有找到帖子</h3>
            {% if search_query %}
                <p>没有符合"{{ search_query }}"的搜索结果。</p>
                <a href="{% url 'services:community' %}" class="btn btn-outline-primary">查看所有帖子</a>
            {% else %}
                <p>社区里还没有任何帖子。</p>
                {% if user.is_authenticated %}
                    <a href="{% url 'services:create_post' %}" class="btn btn-primary">
                        成为第一个发帖的人
                    </a>
                {% else %}
                    <a href="{% url 'accounts:login' %}?next={% url 'services:create_post' %}" class="btn btn-primary">
                        登录并发布帖子
                    </a>
                {% endif %}
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %} 