# Generated by Django 4.2 on 2025-03-26 08:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='servicegameaccount',
            name='profession',
            field=models.CharField(blank=True, choices=[('miner', '挖矿工'), ('logger', '伐木工'), ('hemp_collector', '采麻工'), ('gunsmith', '枪械工'), ('furniture_maker', '家具工'), ('armorer', '护甲工'), ('treasure_hunter', '探宝者'), ('rifleman', '步枪兵'), ('sniper', '狙击手'), ('warrior', '武士'), ('ark_knight', '方舟骑士'), ('serum_expert', '血清专家'), ('spore_hunter', '孢子猎手'), ('rune_master', '符师')], max_length=30, verbose_name='职业'),
        ),
        migrations.AddField(
            model_name='servicegameaccount',
            name='reject_reason',
            field=models.TextField(blank=True, verbose_name='拒绝原因'),
        ),
        migrations.AddField(
            model_name='servicegameaccount',
            name='server',
            field=models.CharField(blank=True, max_length=50, verbose_name='区服'),
        ),
        migrations.AlterField(
            model_name='servicegameaccount',
            name='account_level',
            field=models.CharField(choices=[('1', '1级庄园'), ('2', '2级庄园'), ('3', '3级庄园'), ('4', '4级庄园'), ('5', '5级庄园'), ('6', '6级庄园'), ('7', '7级庄园'), ('8', '8级庄园'), ('9', '9级庄园'), ('10', '10级庄园'), ('11', '11级庄园'), ('12', '12级庄园'), ('13', '13级庄园'), ('14', '14级庄园'), ('15', '15级庄园'), ('16', '16级庄园'), ('17', '17级庄园'), ('18', '18级庄园'), ('19', '19级庄园'), ('20', '20级庄园'), ('21', '21级庄园'), ('22', '22级庄园'), ('23', '23级庄园'), ('24', '24级庄园'), ('25', '25级庄园'), ('26', '26级庄园'), ('27', '27级庄园'), ('28', '28级庄园'), ('29', '29级庄园')], max_length=20, verbose_name='庄园等级'),
        ),
        migrations.AlterField(
            model_name='servicegameaccount',
            name='game_type',
            field=models.CharField(choices=[('lifeafter', '明日之后')], default='lifeafter', max_length=50, verbose_name='游戏类型'),
        ),
        migrations.AlterField(
            model_name='servicegameaccount',
            name='status',
            field=models.CharField(choices=[('pending', '待审核'), ('active', '已上架'), ('sold', '已售出'), ('rejected', '未通过审核'), ('inactive', '已下架'), ('reserved', '已预订')], default='pending', max_length=20, verbose_name='状态'),
        ),
    ]
