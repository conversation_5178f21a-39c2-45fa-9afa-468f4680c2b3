from django.db import models
from django.conf import settings
from django.utils import timezone
from django.urls import reverse
import random
import string

class CommunityPost(models.Model):
    """社区帖子模型"""
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='community_posts', verbose_name='作者')
    title = models.CharField('标题', max_length=200)
    content = models.TextField('内容')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    is_pinned = models.BooleanField('置顶', default=False)
    view_count = models.PositiveIntegerField('查看次数', default=0)
    
    class Meta:
        verbose_name = '社区帖子'
        verbose_name_plural = '社区帖子'
        ordering = ['-is_pinned', '-created_at']
    
    def __str__(self):
        return self.title

class PostComment(models.Model):
    """帖子评论模型"""
    post = models.ForeignKey(CommunityPost, on_delete=models.CASCADE, related_name='comments', verbose_name='帖子')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='post_comments', verbose_name='作者')
    content = models.TextField('内容')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '帖子评论'
        verbose_name_plural = '帖子评论'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.author.username}的评论"

class WishlistService(models.Model):
    """心愿工坊服务类型"""
    SERVICE_TYPES = (
        ('daigan', '代肝服务'),
        ('map', '跑图服务'),
        ('pvp', 'PVP代打'),
        ('gold', '金条交易'),
        ('other', '其他服务'),
    )
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='wishlist_services', verbose_name='用户')
    service_type = models.CharField('服务类型', max_length=20, choices=SERVICE_TYPES)
    title = models.CharField('标题', max_length=100)
    description = models.TextField('描述')
    price = models.DecimalField('价格', max_digits=10, decimal_places=2, null=True, blank=True)
    price_negotiable = models.BooleanField('价格可议', default=True)
    is_offering = models.BooleanField('提供服务', default=True)  # True为提供服务，False为需求服务
    contact_info = models.CharField('联系方式', max_length=100)
    is_active = models.BooleanField('有效状态', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '心愿工坊服务'
        verbose_name_plural = '心愿工坊服务'
        ordering = ['-created_at']
    
    def __str__(self):
        status = '提供' if self.is_offering else '需求'
        return f"{self.get_service_type_display()} ({status}) - {self.title}"

class TradeApplication(models.Model):
    """交易申请模型"""
    STATUS_CHOICES = (
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('rejected', '已拒绝'),
        ('canceled', '已取消'),
    )
    
    CONTACT_PREFERENCE_CHOICES = (
        ('wechat', '微信'),
        ('qq', 'QQ'),
        ('phone', '电话'),
    )
    
    service = models.ForeignKey(WishlistService, on_delete=models.CASCADE, related_name='applications', verbose_name='服务')
    applicant = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='trade_applications', verbose_name='申请人')
    message = models.TextField('申请信息', blank=True)
    contact_preference = models.CharField('联系方式偏好', max_length=20, choices=CONTACT_PREFERENCE_CHOICES)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    admin_notes = models.TextField('管理员备注', blank=True)
    created_at = models.DateTimeField('申请时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    processed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='processed_applications', verbose_name='处理人')
    processed_at = models.DateTimeField('处理时间', null=True, blank=True)
    
    class Meta:
        verbose_name = '交易申请'
        verbose_name_plural = '交易申请'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.applicant.username}的申请 - {self.service.title} - {self.get_status_display()}"

class GameQuestion(models.Model):
    """游戏答疑问题模型"""
    STATUS_CHOICES = (
        ('pending', '待回复'),
        ('answered', '已回复'),
        ('closed', '已关闭'),
    )
    
    CATEGORY_CHOICES = (
        ('gameplay', '游戏玩法'),
        ('technical', '技术问题'),
        ('account', '账号问题'),
        ('payment', '支付问题'),
        ('other', '其他问题'),
    )
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='game_questions', verbose_name='提问用户')
    title = models.CharField('问题标题', max_length=200)
    content = models.TextField('问题内容')
    category = models.CharField('问题类别', max_length=20, choices=CATEGORY_CHOICES, default='gameplay')
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    is_public = models.BooleanField('公开问题', default=True, help_text='公开的问题会显示在常见问题列表中')
    created_at = models.DateTimeField('提问时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    view_count = models.PositiveIntegerField('查看次数', default=0)
    
    class Meta:
        verbose_name = '游戏问题'
        verbose_name_plural = '游戏问题'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def has_answer(self):
        return self.answers.exists()
    has_answer.boolean = True
    has_answer.short_description = '已回复'

class QuestionAnswer(models.Model):
    """问题回复模型"""
    question = models.ForeignKey(GameQuestion, on_delete=models.CASCADE, related_name='answers', verbose_name='问题')
    responder = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='question_answers', verbose_name='回复者')
    content = models.TextField('回复内容')
    created_at = models.DateTimeField('回复时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    is_staff_answer = models.BooleanField('官方回复', default=False)
    
    class Meta:
        verbose_name = '问题回复'
        verbose_name_plural = '问题回复'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.responder.username}对问题的回复"
    
    def save(self, *args, **kwargs):
        """当保存回复时，自动更新问题状态为已回复"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        # 如果是新回复且是官方回复，则更新问题状态
        if is_new and self.is_staff_answer and self.question.status == 'pending':
            self.question.status = 'answered'
            self.question.updated_at = timezone.now()
            self.question.save(update_fields=['status', 'updated_at'])

class GameGuide(models.Model):
    """游戏攻略模型"""
    CATEGORY_CHOICES = (
        ('beginner', '新手指南'),
        ('advanced', '进阶技巧'),
        ('boss', 'BOSS攻略'),
        ('map', '地图攻略'),
        ('weapon', '武器装备'),
        ('quest', '任务攻略'),
        ('event', '活动攻略'),
        ('other', '其他攻略'),
    )
    
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='game_guides', verbose_name='作者')
    title = models.CharField('标题', max_length=200)
    content = models.TextField('内容')
    category = models.CharField('攻略分类', max_length=20, choices=CATEGORY_CHOICES, default='other')
    is_featured = models.BooleanField('精选攻略', default=False)
    view_count = models.PositiveIntegerField('查看次数', default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '游戏攻略'
        verbose_name_plural = '游戏攻略'
        ordering = ['-is_featured', '-created_at']
    
    def __str__(self):
        return self.title

class GuideImage(models.Model):
    """攻略图片模型"""
    guide = models.ForeignKey(GameGuide, on_delete=models.CASCADE, related_name='images', verbose_name='攻略')
    image = models.ImageField('图片', upload_to='guide_images/')
    caption = models.CharField('图片说明', max_length=200, blank=True)
    order = models.PositiveSmallIntegerField('排序', default=0)
    
    class Meta:
        verbose_name = '攻略图片'
        verbose_name_plural = '攻略图片'
        ordering = ['order']
    
    def __str__(self):
        return f"{self.guide.title}的图片 {self.order}"

class GuideComment(models.Model):
    """攻略评论模型"""
    guide = models.ForeignKey(GameGuide, on_delete=models.CASCADE, related_name='comments', verbose_name='攻略')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='guide_comments', verbose_name='作者')
    content = models.TextField('内容')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '攻略评论'
        verbose_name_plural = '攻略评论'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.author.username}对攻略的评论"

# 账号交易相关模型
class ServiceGameAccount(models.Model):
    """游戏账号模型"""
    GAME_CHOICES = (
        ('lifeafter', '明日之后'),
    )
    
    STATUS_CHOICES = (
        ('pending', '待审核'),
        ('active', '已上架'),
        ('sold', '已售出'),
        ('rejected', '未通过审核'),
        ('inactive', '已下架'),
        ('reserved', '已预订'),
    )
    
    # 庄园等级选项（倒序排列）
    ACCOUNT_LEVEL_CHOICES = (
        ('29', '29级庄园'),
        ('28', '28级庄园'),
        ('27', '27级庄园'),
        ('26', '26级庄园'),
        ('25', '25级庄园'),
        ('24', '24级庄园'),
        ('23', '23级庄园'),
        ('22', '22级庄园'),
        ('21', '21级庄园'),
        ('20', '20级庄园'),
        ('19', '19级庄园'),
        ('18', '18级庄园'),
        ('17', '17级庄园'),
        ('16', '16级庄园'),
        ('15', '15级庄园'),
        ('14', '14级庄园'),
        ('13', '13级庄园'),
        ('12', '12级庄园'),
        ('11', '11级庄园'),
        ('10', '10级庄园'),
        ('9', '9级庄园'),
        ('8', '8级庄园'),
        ('7', '7级庄园'),
        ('6', '6级庄园'),
        ('5', '5级庄园'),
        ('4', '4级庄园'),
        ('3', '3级庄园'),
        ('2', '2级庄园'),
        ('1', '1级庄园'),
    )
    
    # 职业选择
    PROFESSION_CHOICES = (
        ('miner', '挖矿工'),
        ('logger', '伐木工'),
        ('hemp_collector', '采麻工'),
        ('gunsmith', '枪械工'),
        ('furniture_maker', '家具工'),
        ('armorer', '护甲工'),
        ('treasure_hunter', '探宝者'),
        ('rifleman', '步枪兵'),
        ('sniper', '狙击手'),
        ('warrior', '武士'),
        ('ark_knight', '方舟骑士'),
        ('serum_expert', '血清专家'),
        ('spore_hunter', '孢子猎手'),
        ('rune_master', '符师'),
    )
    
    # 基本信息
    seller = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='selling_accounts', verbose_name='卖家')
    game_type = models.CharField('游戏类型', max_length=50, choices=GAME_CHOICES, default='lifeafter')
    title = models.CharField('标题', max_length=200)
    description = models.TextField('账号描述')
    price = models.DecimalField('价格', max_digits=10, decimal_places=2)
    account_level = models.CharField('庄园等级', max_length=20, choices=ACCOUNT_LEVEL_CHOICES)
    profession = models.CharField('职业', max_length=30, choices=PROFESSION_CHOICES, blank=True)
    server = models.CharField('区服', max_length=50, blank=True)
    device_type = models.CharField('设备类型', max_length=50, blank=True, help_text='如Android, iOS等')
    
    # 详细信息
    game_roles = models.JSONField('游戏角色', default=dict, blank=True, null=True)
    game_items = models.JSONField('游戏物品', default=dict, blank=True, null=True)
    
    # 状态与管理
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    is_featured = models.BooleanField('推荐账号', default=False)
    view_count = models.PositiveIntegerField('浏览量', default=0)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    publish_at = models.DateTimeField('发布时间', null=True, blank=True)
    expire_at = models.DateTimeField('下架时间', null=True, blank=True)
    
    # 审核信息
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='reviewed_accounts', 
        verbose_name='审核人员',
        null=True, blank=True
    )
    review_note = models.TextField('审核备注', blank=True)
    reject_reason = models.TextField('拒绝原因', blank=True)
    review_at = models.DateTimeField('审核时间', null=True, blank=True)
    
    # 联系方式 (仅管理员可见)
    contact_type = models.CharField('联系方式类型', max_length=50, default='phone')
    contact_value = models.CharField('联系方式', max_length=100)
    
    class Meta:
        verbose_name = '游戏账号'
        verbose_name_plural = '游戏账号'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.get_game_type_display()})"
    
    def get_absolute_url(self):
        return reverse('services:account_detail', kwargs={'account_id': self.id})
    
    @property
    def main_image(self):
        """获取主图片"""
        return self.images.filter(is_main=True).first() or self.images.first()
    
    def publish(self, admin_user=None):
        """发布账号"""
        if self.status == 'pending':
            self.status = 'active'
            self.publish_at = timezone.now()
            if admin_user:
                self.reviewed_by = admin_user
                self.review_at = timezone.now()
            self.save()
            return True
        return False
    
    def reject(self, admin_user, note=''):
        """拒绝账号发布"""
        if self.status in ['pending', 'active']:
            self.status = 'rejected'
            self.reviewed_by = admin_user
            self.review_at = timezone.now()
            self.review_note = note
            self.reject_reason = note
            self.save()
            return True
        return False
    
    def mark_as_sold(self):
        """标记为已售出"""
        if self.status == 'active':
            self.status = 'sold'
            self.save()
            return True
        return False


class AccountImage(models.Model):
    """Images for game accounts."""
    account = models.ForeignKey(ServiceGameAccount, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='account_images/%Y/%m/')
    caption = models.CharField(max_length=255, blank=True)
    is_main = models.BooleanField(default=False)
    section = models.CharField(max_length=30, blank=True, help_text="图片分类，如人物主页、属性面板等")
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.account.title} - {self.caption or '图片'}"


class AccountTag(models.Model):
    """账号标签模型"""
    account = models.ForeignKey(ServiceGameAccount, on_delete=models.CASCADE, related_name='tags', verbose_name='账号')
    name = models.CharField('标签名', max_length=50)
    
    class Meta:
        verbose_name = '账号标签'
        verbose_name_plural = '账号标签'
        unique_together = ['account', 'name']
    
    def __str__(self):
        return self.name


class AccountOrder(models.Model):
    """账号订单模型"""
    STATUS_CHOICES = (
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    )
    
    account = models.ForeignKey(ServiceGameAccount, on_delete=models.PROTECT, related_name='orders', verbose_name='账号')
    buyer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='buying_orders', verbose_name='买家')
    order_number = models.CharField('订单号', max_length=50, unique=True)
    price = models.DecimalField('交易价格', max_digits=10, decimal_places=2)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 联系信息
    contact_type = models.CharField('联系方式类型', max_length=50)
    contact_value = models.CharField('联系方式', max_length=100)
    message = models.TextField('留言', blank=True)
    
    # 订单时间节点
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    paid_at = models.DateTimeField('支付时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    cancelled_at = models.DateTimeField('取消时间', null=True, blank=True)
    
    # 管理员处理
    handled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='handled_orders', 
        verbose_name='处理人员',
        null=True, blank=True
    )
    admin_note = models.TextField('管理员备注', blank=True)
    
    class Meta:
        verbose_name = '账号订单'
        verbose_name_plural = '账号订单'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"订单 {self.order_number} - {self.account.title}"
    
    def generate_order_number(self):
        """生成订单号"""
        prefix = "ACC"
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_part = ''.join(random.choices(string.digits, k=4))
        return f"{prefix}{timestamp}{random_part}"
    
    def save(self, *args, **kwargs):
        """保存前生成订单号"""
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)
    
    def mark_as_paid(self):
        """标记为已支付"""
        if self.status == 'pending':
            self.status = 'processing'
            self.paid_at = timezone.now()
            self.save()
            return True
        return False
    
    def complete(self, admin_user=None):
        """完成订单"""
        if self.status == 'processing':
            self.status = 'completed'
            self.completed_at = timezone.now()
            if admin_user:
                self.handled_by = admin_user
            self.save()
            # 更新账号状态为已售出
            self.account.mark_as_sold()
            return True
        return False
    
    def cancel(self, note=''):
        """取消订单"""
        if self.status in ['pending', 'processing']:
            self.status = 'cancelled'
            self.cancelled_at = timezone.now()
            self.admin_note = note
            self.save()
            return True
        return False
