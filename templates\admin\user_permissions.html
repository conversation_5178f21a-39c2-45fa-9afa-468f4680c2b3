{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block title %}用户权限管理 | 明日之后游戏账号交易平台{% endblock %}

{% block extrahead %}
<style>
    .user-permission-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    .search-bar {
        margin-bottom: 20px;
    }
    .staff-badge {
        background-color: #28a745;
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
    }
    .superuser-badge {
        background-color: #dc3545;
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="user-permission-container">
    <div id="content-main">
        <h1>用户权限管理</h1>
        
        <!-- 搜索用户 -->
        <div class="search-bar">
            <form method="get" action=".">
                <div class="form-row">
                    <div class="input-group">
                        <input type="text" name="q" value="{{ query }}" class="form-control" placeholder="搜索用户名或邮箱">
                        <div class="input-group-append">
                            <button type="submit" class="button">搜索</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 用户列表 -->
        <div class="module">
            <h2>用户列表</h2>
            <div class="results">
                <table id="result_list" class="table">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>姓名</th>
                            <th>注册时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr class="{% cycle 'row1' 'row2' %}">
                            <td>
                                {{ user.username }}
                                {% if user.is_superuser %}
                                <span class="superuser-badge">超级管理员</span>
                                {% elif user.is_staff %}
                                <span class="staff-badge">管理员</span>
                                {% endif %}
                            </td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.get_full_name }}</td>
                            <td>{{ user.date_joined|date:"Y-m-d H:i" }}</td>
                            <td>
                                {% if user.is_active %}
                                <span style="color: green;">活跃</span>
                                {% else %}
                                <span style="color: red;">禁用</span>
                                {% endif %}
                            </td>
                            <td>
                                <form method="post" action="{% url 'admin:toggle_staff_status' user.id %}">
                                    {% csrf_token %}
                                    {% if not user.is_superuser %}
                                        {% if user.is_staff %}
                                        <button type="submit" class="button" style="background-color: #dc3545;">
                                            移除管理员权限
                                        </button>
                                        {% else %}
                                        <button type="submit" class="button" style="background-color: #28a745;">
                                            授予管理员权限
                                        </button>
                                        {% endif %}
                                    {% else %}
                                        <span class="help">不能修改超级管理员</span>
                                    {% endif %}
                                </form>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" style="text-align: center;">没有找到用户</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if is_paginated %}
            <div class="paginator">
                {% if page_obj.has_previous %}
                <a href="?page=1" class="showall">首页</a>
                <a href="?page={{ page_obj.previous_page_number }}" class="showall">上一页</a>
                {% endif %}
                
                <span class="page-info">
                    第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                </span>
                
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="showall">下一页</a>
                <a href="?page={{ page_obj.paginator.num_pages }}" class="showall">末页</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
        
        <!-- 权限说明 -->
        <div class="module">
            <h2>权限说明</h2>
            <div class="help">
                <p><strong>管理员权限</strong>：具有管理员权限的用户能够访问管理后台，审核游戏账号，管理平台内容。</p>
                <p><strong>超级管理员权限</strong>：超级管理员拥有最高权限，可以管理所有内容，包括用户权限。只有超级管理员可以设置或移除其他用户的管理员权限。</p>
                <p class="text-danger">注意：请谨慎授予管理员权限，建议只授予信任的用户。</p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 