{% extends "admin/base_site.html" %}

{% block title %}处理订单 #{{ order.id }}{% endblock %}

{% block extrahead %}
<style>
    .order-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    .status-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        display: inline-block;
    }
    .status-pending {
        background-color: #ffc107;
        color: #212529;
    }
    .status-processing {
        background-color: #17a2b8;
        color: #fff;
    }
    .status-completed {
        background-color: #28a745;
        color: #fff;
    }
    .status-rejected {
        background-color: #dc3545;
        color: #fff;
    }
    .status-cancelled {
        background-color: #6c757d;
        color: #fff;
    }
    .contact-box {
        background-color: #f8f9fa;
        border: 1px solid #eaeaea;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .contact-warning {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="order-container">
    <div id="content-main">
        <h1>处理订单 #{{ order.id }}</h1>
        
        <div class="module">
            <h2>订单基本信息</h2>
            <div class="form-row">
                <div>
                    <table>
                        <tr>
                            <th>订单ID:</th>
                            <td>#{{ order.id }}</td>
                        </tr>
                        <tr>
                            <th>账号标题:</th>
                            <td><a href="{% url 'services:account_detail' order.account.id %}" target="_blank">{{ order.account.title }}</a></td>
                        </tr>
                        <tr>
                            <th>账号价格:</th>
                            <td>¥{{ order.price }}</td>
                        </tr>
                        <tr>
                            <th>状态:</th>
                            <td>
                                <span class="status-badge status-{{ order.status }}">
                                    {% if order.status == 'pending' %}待处理
                                    {% elif order.status == 'processing' %}处理中
                                    {% elif order.status == 'completed' %}已完成
                                    {% elif order.status == 'rejected' %}已拒绝
                                    {% elif order.status == 'cancelled' %}已取消
                                    {% endif %}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>创建时间:</th>
                            <td>{{ order.created_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                        <tr>
                            <th>更新时间:</th>
                            <td>{{ order.updated_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                        {% if order.handled_by %}
                        <tr>
                            <th>处理人员:</th>
                            <td>{{ order.handled_by.username }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
        
        <div class="module">
            <h2>买家信息</h2>
            <div class="form-row">
                <div class="contact-box">
                    <p><strong>用户名:</strong> {{ order.buyer.get_display_name }}</p>
                    <p><strong>邮箱:</strong> {{ order.buyer.email }}</p>
                    <p><strong>联系方式:</strong> {{ order.get_contact_type_display }}: {{ order.contact_value }}</p>
                    <div class="contact-warning">
                        <strong>重要提示:</strong> 买家的联系方式仅供管理员使用，请不要直接将买家和卖家的联系方式透露给对方，避免绕过平台交易。
                    </div>
                </div>
            </div>
        </div>
        
        <div class="module">
            <h2>卖家信息</h2>
            <div class="form-row">
                <div class="contact-box">
                    <p><strong>用户名:</strong> {{ order.account.seller.get_display_name }}</p>
                    <p><strong>邮箱:</strong> {{ order.account.seller.email }}</p>
                    <p><strong>联系方式:</strong> {{ order.account.get_contact_type_display }}: {{ order.account.contact_value }}</p>
                </div>
            </div>
        </div>
        
        {% if order.message %}
        <div class="module">
            <h2>买家留言</h2>
            <div class="form-row">
                <div style="padding: 15px; background: #f9f9f9; border-radius: 4px;">
                    {{ order.message|linebreaks }}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if order.status == 'pending' %}
        <div class="module">
            <h2>处理订单</h2>
            <div class="form-row" style="display: flex; gap: 20px;">
                <div style="flex: 1;">
                    <h3>接受申请</h3>
                    <p>接受申请后，请联系买家和卖家，协调交易。账号将变为"已预订"状态。</p>
                    <form method="post" action="{% url 'services:admin_process_order' order.id %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="accept">
                        <button type="submit" class="button default">接受申请</button>
                    </form>
                </div>
                
                <div style="flex: 1;">
                    <h3>拒绝申请</h3>
                    <p>拒绝申请后，买家将无法再次申请该账号。</p>
                    <form method="post" action="{% url 'services:admin_process_order' order.id %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="reject">
                        <div style="margin-bottom: 10px;">
                            <label for="reason" style="display: block; margin-bottom: 5px;">拒绝原因:</label>
                            <textarea name="reason" id="reason" rows="3" style="width: 100%;"></textarea>
                        </div>
                        <button type="submit" class="button default" style="background-color: #dc3545; color: white;">拒绝申请</button>
                    </form>
                </div>
            </div>
        </div>
        {% elif order.status == 'processing' %}
        <div class="module">
            <h2>完成交易</h2>
            <div class="form-row">
                <p>请确认以下事项：</p>
                <ul>
                    <li>买家已支付完成</li>
                    <li>账号信息已提供给买家</li>
                    <li>买家已确认可以正常使用账号</li>
                </ul>
                
                <p style="color: #721c24; margin-top: 10px;">注意：完成交易后，账号状态将变为"已售出"，不可逆转！</p>
                
                <form method="post" action="{% url 'services:admin_process_order' order.id %}" style="margin-top: 20px;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="complete">
                    <button type="submit" class="button default">确认完成交易</button>
                </form>
            </div>
        </div>
        {% endif %}
        
        <div style="margin-top: 20px;">
            <a href="{% url 'services:admin_account_orders' %}" class="button">返回订单列表</a>
        </div>
    </div>
</div>
{% endblock %} 