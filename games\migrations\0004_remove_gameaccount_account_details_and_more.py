# Generated by Django 4.2 on 2025-03-25 16:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('games', '0003_add_lifeafter_game'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='gameaccount',
            name='account_details',
        ),
        migrations.RemoveField(
            model_name='gameaccount',
            name='character_level',
        ),
        migrations.RemoveField(
            model_name='gameaccount',
            name='has_screenshots',
        ),
        migrations.RemoveField(
            model_name='gameaccount',
            name='has_special_items',
        ),
        migrations.RemoveField(
            model_name='gameaccount',
            name='is_verified',
        ),
        migrations.RemoveField(
            model_name='gameaccount',
            name='manor_level',
        ),
        migrations.RemoveField(
            model_name='gameaccount',
            name='server_region',
        ),
        migrations.AddField(
            model_name='gameaccount',
            name='image',
            field=models.ImageField(blank=True, upload_to='accounts/%Y/%m/%d/', verbose_name='账号截图'),
        ),
        migrations.AddField(
            model_name='gameaccount',
            name='level',
            field=models.IntegerField(default=1, verbose_name='等级'),
        ),
        migrations.AlterField(
            model_name='accountscreenshot',
            name='caption',
            field=models.CharField(blank=True, max_length=100, verbose_name='说明'),
        ),
        migrations.AlterField(
            model_name='accountscreenshot',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='accountscreenshot',
            name='image',
            field=models.ImageField(upload_to='account_screenshots/%Y/%m/%d/', verbose_name='截图'),
        ),
        migrations.AlterField(
            model_name='gameaccount',
            name='character_class',
            field=models.CharField(blank=True, max_length=50, verbose_name='职业'),
        ),
        migrations.AlterField(
            model_name='gameaccount',
            name='description',
            field=models.TextField(verbose_name='描述'),
        ),
        migrations.AlterField(
            model_name='gameaccount',
            name='server',
            field=models.CharField(max_length=50, verbose_name='服务器'),
        ),
        migrations.AlterField(
            model_name='gameaccount',
            name='status',
            field=models.CharField(choices=[('available', '可购买'), ('reserved', '已预订'), ('sold', '已售出')], default='available', max_length=10, verbose_name='状态'),
        ),
        migrations.AlterField(
            model_name='gameaccount',
            name='title',
            field=models.CharField(max_length=100, verbose_name='标题'),
        ),
        migrations.CreateModel(
            name='AccountReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('approved', models.BooleanField(verbose_name='是否批准')),
                ('comments', models.TextField(blank=True, verbose_name='审核意见')),
                ('reviewed_at', models.DateTimeField(auto_now_add=True, verbose_name='审核时间')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='games.gameaccount', verbose_name='游戏账号')),
                ('reviewer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_reviews', to=settings.AUTH_USER_MODEL, verbose_name='审核人')),
            ],
            options={
                'verbose_name': '账号审核记录',
                'verbose_name_plural': '账号审核记录',
                'ordering': ['-reviewed_at'],
            },
        ),
    ]
