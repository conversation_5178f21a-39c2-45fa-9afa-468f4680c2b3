{% extends "base.html" %}

{% block title %}用户管理 | 梦羽明日之后{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    .dashboard-header {
        background: linear-gradient(135deg, #6a11cb, #2575fc);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    .dashboard-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
    }
    .dashboard-header p {
        margin: 10px 0 0;
        opacity: 0.9;
    }
    
    /* 统计卡片 */
    .stats-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 40px;
    }
    
    .stat-card {
        flex: 1;
        min-width: 240px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.12);
    }
    
    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        z-index: 1;
        position: relative;
    }
    
    .stat-value {
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
    }
    
    .stat-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #555;
        position: relative;
        z-index: 1;
    }

    /* 卡片类型 */
    .total-users-card {
        background: linear-gradient(135deg, #ffffff, #e8f4ff);
        border-left: 5px solid #3498db;
    }
    
    .total-users-card .stat-icon {
        color: #3498db;
    }
    
    .staff-users-card {
        background: linear-gradient(135deg, #ffffff, #fff0e5);
        border-left: 5px solid #e67e22;
    }
    
    .staff-users-card .stat-icon {
        color: #e67e22;
    }
    
    .verified-users-card {
        background: linear-gradient(135deg, #ffffff, #e8fff1);
        border-left: 5px solid #2ecc71;
    }
    
    .verified-users-card .stat-icon {
        color: #2ecc71;
    }
    
    /* 用户表格样式 */
    .user-table-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .user-table-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .user-table-header h2 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .user-avatar-placeholder {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }
    
    .user-name {
        font-weight: 600;
        color: #333;
    }
    
    .user-email {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .user-role {
        padding: 5px 10px;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .role-admin {
        background-color: #e74c3c;
        color: white;
    }
    
    .role-staff {
        background-color: #e67e22;
        color: white;
    }
    
    .role-user {
        background-color: #3498db;
        color: white;
    }
    
    .verified-badge {
        background-color: #2ecc71;
        color: white;
        padding: 5px 10px;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .unverified-badge {
        background-color: #95a5a6;
        color: white;
        padding: 5px 10px;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .filter-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .action-btn {
        padding: 6px 12px;
        border-radius: 50px;
        font-size: 0.85rem;
        font-weight: 600;
        transition: all 0.2s;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
    }
    
    .action-btn-view {
        background-color: #3498db;
        color: white;
        border: none;
    }
    
    .action-btn-edit {
        background-color: #2ecc71;
        color: white;
        border: none;
    }
    
    .action-btn-delete {
        background-color: #e74c3c;
        color: white;
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container py-4">
    <div class="dashboard-header">
        <h1>用户管理</h1>
        <p>管理平台用户账号，查看用户信息，设置用户权限。</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-row">
        <div class="stat-card total-users-card">
            <div class="stat-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stat-value">{{ total_users }}</div>
            <div class="stat-title">用户总数</div>
        </div>
        
        <div class="stat-card staff-users-card">
            <div class="stat-icon">
                <i class="bi bi-person-badge"></i>
            </div>
            <div class="stat-value">{{ staff_users }}</div>
            <div class="stat-title">管理员</div>
        </div>
        
        <div class="stat-card verified-users-card">
            <div class="stat-icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-value">{{ verified_users }}</div>
            <div class="stat-title">已认证用户</div>
        </div>
    </div>
    
    <!-- 过滤和搜索 -->
    <div class="filter-card">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="type" class="form-label">用户类型</label>
                <select id="type" name="type" class="form-select">
                    <option value="" {% if not user_type %}selected{% endif %}>全部用户</option>
                    <option value="staff" {% if user_type == 'staff' %}selected{% endif %}>管理员</option>
                    <option value="normal" {% if user_type == 'normal' %}selected{% endif %}>普通用户</option>
                    <option value="verified" {% if user_type == 'verified' %}selected{% endif %}>已认证用户</option>
                    <option value="unverified" {% if user_type == 'unverified' %}selected{% endif %}>未认证用户</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">搜索用户</label>
                <input type="text" id="search" name="search" class="form-control" placeholder="搜索用户名、昵称、邮箱或手机号" value="{{ search_query }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">筛选</button>
                <a href="{% url 'services:admin_user_management' %}" class="btn btn-outline-secondary">重置</a>
            </div>
        </form>
    </div>
    
    <!-- 用户列表 -->
    <div class="user-table-card">
        <div class="user-table-header d-flex justify-content-between align-items-center">
            <h2>用户列表</h2>
            <a href="/admin/auth/user/add/" class="btn btn-sm btn-success">
                <i class="bi bi-plus"></i> 添加用户
            </a>
        </div>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>用户</th>
                        <th>邮箱/手机</th>
                        <th>角色</th>
                        <th>认证状态</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in page_obj %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if user.avatar %}
                                <img src="{{ user.avatar.url }}" alt="{{ user.username }}" class="user-avatar me-2">
                                {% else %}
                                <div class="user-avatar-placeholder me-2">
                                    <i class="bi bi-person"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <div class="user-name">{{ user.get_display_name }}</div>
                                    <div class="user-email">@{{ user.username }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>{{ user.email }}</div>
                            {% if user.phone %}
                            <small class="text-muted">{{ user.phone }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_superuser %}
                            <span class="user-role role-admin">超级管理员</span>
                            {% elif user.is_staff %}
                            <span class="user-role role-staff">管理员</span>
                            {% else %}
                            <span class="user-role role-user">普通用户</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_verified %}
                            <span class="verified-badge">已认证</span>
                            {% else %}
                            <span class="unverified-badge">未认证</span>
                            {% endif %}
                        </td>
                        <td>{{ user.date_joined|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="/admin/auth/user/{{ user.id }}/change/" class="action-btn action-btn-edit">编辑</a>
                                <div class="action-btn action-btn-edit">
                                    <a href="{% url 'services:edit_user_permissions' user_id=user.id %}" class="text-white">
                                        <i class="bi bi-shield-lock"></i> 编辑权限
                                    </a>
                                </div>
                                {% if not user.is_superuser %}
                                <a href="{% url 'services:delete_user' user.id %}" class="action-btn action-btn-delete ms-1">删除</a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-search me-2"></i>没有找到符合条件的用户
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <div class="p-3 border-top">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if user_type %}&type={{ user_type }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">首页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if user_type %}&type={{ user_type }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">上一页</a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item disabled">
                        <span class="page-link">第 {{ page_obj.number }} 页 / 共 {{ page_obj.paginator.num_pages }} 页</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if user_type %}&type={{ user_type }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">下一页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if user_type %}&type={{ user_type }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">末页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %} 