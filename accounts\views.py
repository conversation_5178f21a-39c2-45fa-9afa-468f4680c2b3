from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth import login
from .models import User
from .forms import UserRegistrationForm, UserProfileForm, UserVerificationForm
from django.db import models

def register(request):
    """用户注册视图"""
    try:
        if request.method == 'POST':
            form = UserRegistrationForm(request.POST)
            if form.is_valid():
                user = form.save()
                login(request, user)
                messages.success(request, '注册成功！欢迎加入游戏账号交易平台。')
                return redirect('accounts:dashboard')
        else:
            form = UserRegistrationForm()
        return render(request, 'accounts/register.html', {'form': form})
    except Exception as e:
        # 打印异常信息以进行调试
        import traceback
        print(f"注册视图错误: {str(e)}")
        print(traceback.format_exc())
        raise

@login_required
def profile(request):
    """用户个人资料视图"""
    return render(request, 'accounts/profile.html', {'user': request.user})

@login_required
def edit_profile(request):
    """编辑个人资料视图"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, '个人资料已更新。')
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=request.user)
    return render(request, 'accounts/edit_profile.html', {'form': form})

def user_profile(request, username):
    """查看其他用户资料视图"""
    user = get_object_or_404(User, username=username)
    # 获取用户评价信息
    reviews = user.reviews_received.all()
    avg_rating = reviews.aggregate(avg_rating=models.Avg('rating'))['avg_rating'] or 0
    
    context = {
        'profile_user': user,
        'reviews': reviews,
        'avg_rating': avg_rating,
    }
    return render(request, 'accounts/user_profile.html', context)

@login_required
def dashboard(request):
    """用户仪表盘视图"""
    # 获取用户的游戏账号列表
    game_accounts = request.user.game_accounts.all()
    # 获取用户的交易记录
    purchases = request.user.purchases.all()
    sales = request.user.sales.all()
    
    context = {
        'game_accounts': game_accounts,
        'purchases': purchases,
        'sales': sales,
    }
    return render(request, 'accounts/dashboard.html', context)

@login_required
def verification(request):
    """实名认证视图"""
    if request.user.is_verified:
        messages.info(request, '您已完成实名认证。')
        return redirect('dashboard')
        
    if request.method == 'POST':
        form = UserVerificationForm(request.POST, request.FILES)
        if form.is_valid():
            # 处理实名认证逻辑（实际应用中可能需要调用第三方API）
            request.user.is_verified = True
            request.user.save()
            messages.success(request, '实名认证已提交，正在审核中。')
            return redirect('dashboard')
    else:
        form = UserVerificationForm()
    
    return render(request, 'accounts/verification.html', {'form': form}) 