{% extends 'base.html' %}

{% block title %}我的交易 | 梦羽明日之后{% endblock %}

{% block extra_css %}
<style>
    .transaction-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .page-header {
        background: linear-gradient(135deg, #3498db, #2c3e50);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
    }
    
    .page-header p {
        margin: 10px 0 0;
        opacity: 0.9;
    }
    
    .filter-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .nav-tabs {
        border-bottom: none;
        margin-bottom: 20px;
    }
    
    .nav-tabs .nav-link {
        border: none;
        border-radius: 50px;
        padding: 8px 20px;
        margin-right: 10px;
        font-weight: 600;
        color: #6c757d;
        transition: all 0.2s;
    }
    
    .nav-tabs .nav-link:hover {
        color: #495057;
        background-color: #f8f9fa;
    }
    
    .nav-tabs .nav-link.active {
        color: white;
        background: linear-gradient(135deg, #3498db, #2c3e50);
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    
    .transaction-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .transaction-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .transaction-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .transaction-id {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .transaction-date {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .transaction-status {
        padding: 5px 12px;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .status-pending {
        background-color: #f39c12;
        color: white;
    }
    
    .status-processing {
        background-color: #3498db;
        color: white;
    }
    
    .status-completed {
        background-color: #2ecc71;
        color: white;
    }
    
    .status-cancelled {
        background-color: #e74c3c;
        color: white;
    }
    
    .transaction-body {
        padding: 20px;
        display: flex;
        gap: 20px;
    }
    
    .transaction-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        object-fit: cover;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .transaction-details {
        flex: 1;
    }
    
    .transaction-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .transaction-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #e74c3c;
        margin-bottom: 15px;
    }
    
    .transaction-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        font-size: 0.9rem;
    }
    
    .info-label {
        color: #6c757d;
    }
    
    .info-value {
        font-weight: 500;
        color: #333;
    }
    
    .transaction-footer {
        border-top: 1px solid #f0f0f0;
        padding: 15px 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    .action-btn {
        padding: 8px 16px;
        border-radius: 50px;
        font-size: 0.85rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
    }
    
    .btn-view {
        background-color: #3498db;
        color: white;
    }
    
    .btn-cancel {
        background-color: #e74c3c;
        color: white;
    }
    
    .btn-contact {
        background-color: #2ecc71;
        color: white;
    }
    
    .empty-state {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.06);
        padding: 50px 20px;
        text-align: center;
    }
    
    .empty-icon {
        font-size: 4rem;
        color: #d1d8e0;
        margin-bottom: 20px;
    }
    
    .empty-text {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="transaction-container py-4">
    <div class="page-header">
        <h1>我的交易</h1>
        <p>查看您的账号交易记录、订单状态和交易历史。</p>
    </div>
    
    <!-- 过滤选项卡 -->
    <div class="filter-card">
        <ul class="nav nav-tabs" id="transactionTab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if not status or status == 'all' %}active{% endif %}" href="?status=all">全部 ({{ stats.total }})</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if status == 'pending' %}active{% endif %}" href="?status=pending">待处理 ({{ stats.pending }})</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if status == 'processing' %}active{% endif %}" href="?status=processing">处理中 ({{ stats.processing }})</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if status == 'completed' %}active{% endif %}" href="?status=completed">已完成 ({{ stats.completed }})</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if status == 'cancelled' %}active{% endif %}" href="?status=cancelled">已取消 ({{ stats.cancelled }})</a>
            </li>
        </ul>
        
        <form method="get" action="{% url 'trades:transaction_list' %}">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">搜索交易</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="输入订单号、账号名称等" value="{{ search_query|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="date-range" class="form-label">日期范围</label>
                    <select class="form-select" id="date-range" name="date_range">
                        <option value="all" {% if not date_range or date_range == 'all' %}selected{% endif %}>全部时间</option>
                        <option value="today" {% if date_range == 'today' %}selected{% endif %}>今天</option>
                        <option value="week" {% if date_range == 'week' %}selected{% endif %}>最近7天</option>
                        <option value="month" {% if date_range == 'month' %}selected{% endif %}>最近30天</option>
                        <option value="year" {% if date_range == 'year' %}selected{% endif %}>最近一年</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="sort" class="form-label">排序方式</label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="newest" {% if not sort or sort == 'newest' %}selected{% endif %}>最新交易</option>
                        <option value="oldest" {% if sort == 'oldest' %}selected{% endif %}>最早交易</option>
                        <option value="price_high" {% if sort == 'price_high' %}selected{% endif %}>价格从高到低</option>
                        <option value="price_low" {% if sort == 'price_low' %}selected{% endif %}>价格从低到高</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">筛选</button>
                </div>
                {% if status %}<input type="hidden" name="status" value="{{ status }}">{% endif %}
            </div>
        </form>
    </div>
    
    <!-- 交易内容 -->
    <div class="mb-4">
        {% if transactions %}
            {% for transaction in transactions %}
                <div class="transaction-card">
                    <div class="transaction-header">
                        <span class="transaction-id">订单号: {{ transaction.transaction_id }}</span>
                        <div class="d-flex align-items-center">
                            <span class="transaction-date me-3">{{ transaction.created_at|date:"Y-m-d H:i" }}</span>
                            <span class="transaction-status 
                                {% if transaction.status == 'pending' %}status-pending
                                {% elif transaction.status == 'paid' or transaction.status == 'delivering' %}status-processing
                                {% elif transaction.status == 'completed' %}status-completed
                                {% else %}status-cancelled{% endif %}">
                                {% if transaction.status == 'pending' %}待处理
                                {% elif transaction.status == 'paid' %}已支付
                                {% elif transaction.status == 'delivering' %}交付中
                                {% elif transaction.status == 'completed' %}已完成
                                {% elif transaction.status == 'canceled' %}已取消
                                {% elif transaction.status == 'refunding' %}退款中
                                {% elif transaction.status == 'refunded' %}已退款
                                {% else %}{{ transaction.status }}{% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="transaction-body">
                        <div class="transaction-image">
                            {% if transaction.game_account.images.first %}
                                <img src="{{ transaction.game_account.images.first.image.url }}" alt="账号图片">
                            {% else %}
                                <img src="https://via.placeholder.com/120?text=账号图片" alt="账号图片">
                            {% endif %}
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">{{ transaction.game_account.title }}</div>
                            <div class="transaction-price">￥{{ transaction.amount }}</div>
                            <div class="transaction-info">
                                <div>
                                    <span class="info-label">卖家: </span>
                                    <span class="info-value">{{ transaction.seller.username }}</span>
                                </div>
                                <div>
                                    <span class="info-label">账号等级: </span>
                                    <span class="info-value">{{ transaction.game_account.level }}级</span>
                                </div>
                                <div>
                                    <span class="info-label">区服: </span>
                                    <span class="info-value">{{ transaction.game_account.server }}</span>
                                </div>
                                <div>
                                    <span class="info-label">设备: </span>
                                    <span class="info-value">{{ transaction.game_account.get_device_display }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-footer">
                        <a href="{% url 'trades:transaction_detail' transaction_id=transaction.transaction_id %}" class="action-btn btn-view">查看详情</a>
                        
                        {% if transaction.status == 'pending' and user == transaction.buyer %}
                            <a href="{% url 'trades:payment' transaction_id=transaction.transaction_id %}" class="action-btn btn-contact">去支付</a>
                            <a href="{% url 'trades:cancel_transaction' transaction_id=transaction.transaction_id %}" class="action-btn btn-cancel">取消订单</a>
                        {% elif transaction.status == 'paid' and user == transaction.seller %}
                            <a href="{% url 'trades:confirm_transaction' transaction_id=transaction.transaction_id %}" class="action-btn btn-contact">确认处理</a>
                        {% elif transaction.status == 'delivering' and user == transaction.buyer %}
                            <a href="{% url 'trades:complete_transaction' transaction_id=transaction.transaction_id %}" class="action-btn btn-contact">确认完成</a>
                        {% endif %}
                        
                        {% if transaction.status != 'completed' and transaction.status != 'canceled' and transaction.status != 'refunded' %}
                            <a href="{% url 'trades:create_dispute' transaction_id=transaction.transaction_id %}" class="action-btn btn-cancel">申请售后</a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
            
            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if status %}&status={{ status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_range %}&date_range={{ date_range }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status %}&status={{ status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_range %}&date_range={{ date_range }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if status %}&status={{ status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_range %}&date_range={{ date_range }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status %}&status={{ status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_range %}&date_range={{ date_range }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status %}&status={{ status }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_range %}&date_range={{ date_range }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <!-- 无交易记录显示 -->
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="bi bi-inbox"></i>
                </div>
                <h3 class="empty-text">暂无交易记录</h3>
                <a href="{% url 'services:account_list' %}" class="btn btn-primary">浏览游戏账号</a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 