{% extends 'base.html' %}
{% load static %}

{% block title %}{{ question.title }} - 游戏答疑{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:game_qa' %}">游戏答疑</a></li>
            <li class="breadcrumb-item active" aria-current="page">问题详情</li>
        </ol>
    </nav>
    
    <!-- 问题详情 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="mb-0 fs-4">{{ question.title }}</h2>
            <span class="badge {% if question.status == 'pending' %}bg-warning{% elif question.status == 'answered' %}bg-success{% elif question.status == 'closed' %}bg-secondary{% endif %}">
                {{ question.get_status_display }}
            </span>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <div>
                        <small class="text-muted">提问者: {{ question.user.username }}</small>
                        <small class="text-muted ms-3">分类: {{ question.get_category_display }}</small>
                    </div>
                    <div>
                        <small class="text-muted">提问时间: {{ question.created_at|date:"Y-m-d H:i" }}</small>
                        <small class="text-muted ms-3">浏览: {{ question.view_count }}</small>
                    </div>
                </div>
            </div>
            <div class="question-content">
                {{ question.content|linebreaks }}
            </div>
        </div>
    </div>
    
    <!-- 回答列表 -->
    <h3 class="mb-3 fs-5">回答 ({{ answers.count }})</h3>
    {% if answers %}
        {% for answer in answers %}
        <div class="card mb-3 {% if answer.is_staff_answer %}border-primary{% endif %}">
            <div class="card-header {% if answer.is_staff_answer %}bg-primary text-white{% endif %} d-flex justify-content-between">
                <div>
                    {{ answer.responder.username }}
                    {% if answer.is_staff_answer %}
                    <span class="badge bg-light text-primary ms-2">官方回复</span>
                    {% endif %}
                </div>
                <small>{{ answer.created_at|date:"Y-m-d H:i" }}</small>
            </div>
            <div class="card-body">
                {{ answer.content|linebreaks }}
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="alert alert-info">
            暂无回答，请耐心等待...
        </div>
    {% endif %}
    
    <!-- 回复表单 -->
    {% if can_answer %}
    <div class="card mt-4">
        <div class="card-header">
            添加回复
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                <div class="mb-3">
                    <label for="answer_content" class="form-label">您的回复</label>
                    <textarea class="form-control" id="answer_content" name="answer_content" rows="5" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">提交回复</button>
            </form>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">
        请<a href="{% url 'login' %}?next={{ request.path }}">登录</a>后回复问题
    </div>
    {% endif %}
</div>
{% endblock %} 