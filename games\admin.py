from django.contrib import admin
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import path
from django.contrib import messages
from django.utils import timezone
from .models import Game, GameServer, GameAccount, AccountScreenshot, AccountReview

class AccountScreenshotInline(admin.TabularInline):
    model = AccountScreenshot
    extra = 1

@admin.register(Game)
class GameAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'is_active')
    prepopulated_fields = {'slug': ('name',)}

@admin.register(GameServer)
class GameServerAdmin(admin.ModelAdmin):
    list_display = ('name', 'game')
    list_filter = ('game',)
    search_fields = ('name',)

@admin.register(GameAccount)
class GameAccountAdmin(admin.ModelAdmin):
    list_display = ('title', 'seller', 'game', 'server', 'price', 'status', 'created_at')
    list_filter = ('game', 'status', 'is_featured')
    search_fields = ('title', 'description', 'seller__username')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [AccountScreenshotInline]
    
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('review/', self.admin_site.admin_view(self.review_accounts_view), name='review_accounts'),
            path('review/<int:account_id>/', self.admin_site.admin_view(self.review_account_detail_view), name='review_account_detail'),
            path('approve/<int:account_id>/', self.admin_site.admin_view(self.approve_account_view), name='approve_account'),
            path('reject/<int:account_id>/', self.admin_site.admin_view(self.reject_account_view), name='reject_account'),
        ]
        return custom_urls + urls
    
    def review_accounts_view(self, request):
        """显示待审核的账号列表"""
        pending_accounts = GameAccount.objects.filter(status='pending').order_by('-created_at')
        recent_reviews = AccountReview.objects.all().order_by('-reviewed_at')[:10]
        
        context = {
            'title': '账号审核',
            'pending_accounts': pending_accounts,
            'recent_reviews': recent_reviews,
            'opts': self.model._meta,
            **self.admin_site.each_context(request),
        }
        return render(request, 'admin/account_review.html', context)
    
    def review_account_detail_view(self, request, account_id):
        """显示账号详情用于审核"""
        account = get_object_or_404(GameAccount, id=account_id)
        
        context = {
            'title': f'审核账号: {account.title}',
            'account': account,
            'opts': self.model._meta,
            **self.admin_site.each_context(request),
        }
        return render(request, 'admin/account_review_detail.html', context)
    
    def approve_account_view(self, request, account_id):
        """批准账号上架"""
        account = get_object_or_404(GameAccount, id=account_id)
        
        if request.method == 'POST':
            comments = request.POST.get('comments', '')
            
            # 更新账号状态
            account.status = 'available'
            account.save()
            
            # 创建审核记录
            AccountReview.objects.create(
                account=account,
                reviewer=request.user,
                approved=True,
                comments=comments,
                reviewed_at=timezone.now()
            )
            
            messages.success(request, f'账号 "{account.title}" 已审核通过并上架')
            return redirect('admin:review_accounts')
        
        context = {
            'title': f'批准账号: {account.title}',
            'account': account,
            'opts': self.model._meta,
            **self.admin_site.each_context(request),
        }
        return render(request, 'admin/account_approve.html', context)
    
    def reject_account_view(self, request, account_id):
        """拒绝账号上架"""
        account = get_object_or_404(GameAccount, id=account_id)
        
        if request.method == 'POST':
            reason = request.POST.get('reason', '')
            
            # 更新账号状态
            account.status = 'rejected'
            account.save()
            
            # 创建审核记录
            AccountReview.objects.create(
                account=account,
                reviewer=request.user,
                approved=False,
                comments=reason,
                reviewed_at=timezone.now()
            )
            
            messages.success(request, f'账号 "{account.title}" 已被拒绝上架')
            return redirect('admin:review_accounts')
        
        context = {
            'title': f'拒绝账号: {account.title}',
            'account': account,
            'opts': self.model._meta,
            **self.admin_site.each_context(request),
        }
        return render(request, 'admin/account_reject.html', context)

@admin.register(AccountScreenshot)
class AccountScreenshotAdmin(admin.ModelAdmin):
    list_display = ('account', 'caption', 'created_at')
    list_filter = ('account__game',)
    search_fields = ('account__title', 'caption')

@admin.register(AccountReview)
class AccountReviewAdmin(admin.ModelAdmin):
    list_display = ('account', 'reviewer', 'approved', 'reviewed_at')
    list_filter = ('approved', 'reviewed_at')
    search_fields = ('account__title', 'reviewer__username', 'comments')
    readonly_fields = ('reviewed_at',) 