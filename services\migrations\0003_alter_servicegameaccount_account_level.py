# Generated by Django 4.2 on 2025-03-26 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0002_servicegameaccount_profession_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='servicegameaccount',
            name='account_level',
            field=models.CharField(choices=[('29', '29级庄园'), ('28', '28级庄园'), ('27', '27级庄园'), ('26', '26级庄园'), ('25', '25级庄园'), ('24', '24级庄园'), ('23', '23级庄园'), ('22', '22级庄园'), ('21', '21级庄园'), ('20', '20级庄园'), ('19', '19级庄园'), ('18', '18级庄园'), ('17', '17级庄园'), ('16', '16级庄园'), ('15', '15级庄园'), ('14', '14级庄园'), ('13', '13级庄园'), ('12', '12级庄园'), ('11', '11级庄园'), ('10', '10级庄园'), ('9', '9级庄园'), ('8', '8级庄园'), ('7', '7级庄园'), ('6', '6级庄园'), ('5', '5级庄园'), ('4', '4级庄园'), ('3', '3级庄园'), ('2', '2级庄园'), ('1', '1级庄园')], max_length=20, verbose_name='庄园等级'),
        ),
    ]
