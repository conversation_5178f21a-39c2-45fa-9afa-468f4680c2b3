# Gunicorn配置文件

# 绑定地址和端口
bind = "127.0.0.1:8000"

# 工作进程数（建议为CPU核心数的2倍）
workers = 2

# 工作进程类型
worker_class = "sync"

# 每个工作进程的线程数
threads = 2

# 最大客户端连接数
worker_connections = 1000

# 超时设置
timeout = 30
keepalive = 2

# 最大请求数（防止内存泄漏）
max_requests = 1000
max_requests_jitter = 100

# 预加载应用
preload_app = True

# 用户和组
# user = "www-data"
# group = "www-data"

# 进程名称
proc_name = "game_trade"

# 日志配置
accesslog = "/root/web/logs/gunicorn_access.log"
errorlog = "/root/web/logs/gunicorn_error.log"
loglevel = "info"

# PID文件
pidfile = "/root/web/gunicorn.pid"

# 守护进程模式
daemon = False

# 临时目录
tmp_upload_dir = None
