{% extends 'base.html' %}
{% load static %}
{% load guide_filters %}

{% block title %}{{ guide.title }} - 攻略专区{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:guide_list' %}">攻略专区</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ guide.title }}</li>
        </ol>
    </nav>
    
    <!-- 管理员操作 -->
    {% if user.is_staff %}
    <div class="d-flex justify-content-end mb-3">
        <a href="{% url 'services:edit_guide' guide_id=guide.id %}" class="btn btn-outline-primary me-2">
            <i class="bi bi-pencil-square"></i> 编辑攻略
        </a>
        <a href="{% url 'services:delete_guide' guide_id=guide.id %}" class="btn btn-outline-danger">
            <i class="bi bi-trash"></i> 删除攻略
        </a>
    </div>
    {% endif %}
    
    <!-- 攻略标题和信息 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h1 class="card-title mb-0 fs-3">{{ guide.title }}</h1>
            <span class="badge bg-secondary">{{ guide.get_category_display }}</span>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <div>
                        <small class="text-muted">作者: {{ guide.author.username }}</small>
                        {% if guide.is_featured %}
                        <span class="badge bg-warning text-dark ms-2">精选攻略</span>
                        {% endif %}
                    </div>
                    <div>
                        <small class="text-muted">发布时间: {{ guide.created_at|date:"Y-m-d H:i" }}</small>
                        <small class="text-muted ms-3">浏览: {{ guide.view_count }}</small>
                    </div>
                </div>
            </div>
            
            <!-- 攻略内容 -->
            <div class="guide-content">
                {{ guide.content|process_guide_content:guide.images.all }}
            </div>
        </div>
    </div>
    
    <!-- 相关攻略 -->
    {% if related_guides %}
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title h5 mb-0">相关攻略</h3>
        </div>
        <div class="card-body">
            <div class="row row-cols-1 row-cols-md-4 g-3">
                {% for related in related_guides %}
                <div class="col">
                    <div class="card h-100">
                        {% if related.images.first %}
                        <img src="{{ related.images.first.image.url }}" class="card-img-top" style="height: 120px; object-fit: cover;" alt="{{ related.title }}">
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title fs-6">{{ related.title }}</h5>
                            <a href="{% url 'services:guide_detail' guide_id=related.id %}" class="stretched-link"></a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- 评论区 -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title h5 mb-0">评论区 ({{ comments.count }})</h3>
        </div>
        <div class="card-body">
            {% if comments %}
                {% for comment in comments %}
                <div class="d-flex mb-3">
                    <div class="flex-shrink-0">
                        <i class="bi bi-person-circle fs-3"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="d-flex justify-content-between">
                            <h6 class="mb-0">{{ comment.author.username }}</h6>
                            <small class="text-muted">{{ comment.created_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        <p class="mb-0">{{ comment.content }}</p>
                    </div>
                </div>
                {% if not forloop.last %}<hr>{% endif %}
                {% endfor %}
            {% else %}
                <div class="alert alert-light">
                    暂无评论，快来发表您的看法吧！
                </div>
            {% endif %}
            
            <!-- 评论表单 -->
            {% if user.is_authenticated %}
            <form method="post" class="mt-4">
                {% csrf_token %}
                <div class="mb-3">
                    <label for="comment" class="form-label">发表评论</label>
                    <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">提交评论</button>
            </form>
            {% else %}
            <div class="alert alert-warning">
                请<a href="{% url 'login' %}?next={{ request.path }}" class="alert-link">登录</a>后发表评论
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 为Django模板添加自定义过滤器
window.onload = function() {
    // 模板中的自定义过滤器在JavaScript中实现
    const content = document.querySelector('.guide-content');
    if (content) {
        const text = content.innerHTML;
        // 正则表达式替换[IMG:数字]为实际图片
        const newText = text.replace(/\[IMG:(\d+)\]/g, function(match, imgId) {
            const imageElement = document.querySelector(`.image-${imgId}`);
            return imageElement ? imageElement.outerHTML : '';
        });
        content.innerHTML = newText;
    }
};
</script>
{% endblock %} 