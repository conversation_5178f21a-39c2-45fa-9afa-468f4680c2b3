{% extends 'base.html' %}
{% load static %}

{% block title %}处理购买申请 - #{{ order.id }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_orders_seller' %}">购买申请管理</a></li>
            <li class="breadcrumb-item active" aria-current="page">处理申请 #{{ order.id }}</li>
        </ol>
    </nav>
    
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">购买申请详情</h5>
                </div>
                <div class="card-body">
                    <!-- 账号信息 -->
                    <div class="d-flex mb-4">
                        {% if order.account.main_image %}
                        <img src="{{ order.account.main_image.url }}" alt="{{ order.account.title }}" class="me-3 rounded" style="width: 100px; height: 100px; object-fit: cover;">
                        {% else %}
                        <div class="me-3 bg-light text-center d-flex align-items-center justify-content-center rounded" style="width: 100px; height: 100px;">
                            <i class="bi bi-image text-muted" style="font-size: 2rem;"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h4>{{ order.account.title }}</h4>
                            <div class="text-danger fw-bold fs-5">¥{{ order.account.price }}</div>
                            <div class="text-muted">{{ order.account.get_game_type_display }} | {{ order.account.get_account_level_display }}</div>
                            <a href="{% url 'services:account_detail' order.account.id %}" class="mt-2 btn btn-sm btn-outline-primary" target="_blank">查看账号详情</a>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- 订单信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>申请信息</h5>
                            <p class="mb-2"><strong>订单编号：</strong> #{{ order.id }}</p>
                            <p class="mb-2"><strong>申请时间：</strong> {{ order.created_at|date:"Y-m-d H:i" }}</p>
                            <p class="mb-2"><strong>状态：</strong> 
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning text-dark">待处理</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">处理中</span>
                                {% elif order.status == 'completed' %}
                                <span class="badge bg-success">已完成</span>
                                {% elif order.status == 'rejected' %}
                                <span class="badge bg-danger">已拒绝</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-secondary">已取消</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>买家信息</h5>
                            <p class="mb-2"><strong>用户名：</strong> {{ order.buyer.get_display_name }}</p>
                            <p class="mb-2"><strong>注意：</strong> <span class="text-danger">买家联系方式仅限平台管理员可见，请通过平台进行沟通</span></p>
                        </div>
                    </div>
                    
                    {% if order.message %}
                    <div class="mb-4">
                        <h5>买家留言</h5>
                        <div class="p-3 bg-light rounded">
                            {{ order.message|linebreaks }}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if order.status == 'pending' %}
                    <hr>
                    
                    <!-- 操作区域 -->
                    <h5 class="mb-3">处理申请</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-success">接受申请</h5>
                                    <p class="card-text">接受申请后，账号将变为"已预订"状态，其他买家将无法再提交购买申请。</p>
                                    <form method="post" action="{% url 'services:process_order' order.id %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="accept">
                                        <button type="submit" class="btn btn-success w-100">接受申请</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">拒绝申请</h5>
                                    <p class="card-text">拒绝申请后，买家将无法再次申请该账号，除非重新提交申请。</p>
                                    <form method="post" action="{% url 'services:process_order' order.id %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="reject">
                                        <div class="mb-3">
                                            <label for="reason" class="form-label">拒绝原因（选填）</label>
                                            <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="请输入拒绝原因，以便买家了解情况"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-danger w-100">拒绝申请</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% elif order.status == 'processing' %}
                    <hr>
                    
                    <!-- 完成交易区域 -->
                    <h5 class="mb-3">完成交易</h5>
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-success">确认交易完成</h5>
                            <div class="alert alert-info mb-3">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                在确认完成交易前，请确保以下事项已完成：
                                <ul class="mb-0 mt-2">
                                    <li>买家已支付完成</li>
                                    <li>账号信息已提供给买家</li>
                                    <li>买家已确认可以正常使用账号</li>
                                </ul>
                            </div>
                            <p class="card-text">完成交易后，账号将变为"已售出"状态，其他所有待处理的购买申请将被自动取消。<strong>此操作不可撤销。</strong></p>
                            <form method="post" action="{% url 'services:process_order' order.id %}">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="complete">
                                <button type="submit" class="btn btn-success">确认完成交易</button>
                            </form>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert {% if order.status == 'completed' %}alert-success{% elif order.status == 'rejected' %}alert-danger{% else %}alert-secondary{% endif %} mb-0">
                        <div class="d-flex">
                            <div class="me-3">
                                {% if order.status == 'completed' %}
                                <i class="bi bi-check-circle-fill fs-3"></i>
                                {% elif order.status == 'rejected' %}
                                <i class="bi bi-x-circle-fill fs-3"></i>
                                {% else %}
                                <i class="bi bi-info-circle-fill fs-3"></i>
                                {% endif %}
                            </div>
                            <div>
                                {% if order.status == 'completed' %}
                                <h5 class="alert-heading">交易已完成</h5>
                                <p class="mb-0">此交易已于 {{ order.updated_at|date:"Y-m-d H:i" }} 完成，账号已设为已售出状态。</p>
                                {% elif order.status == 'rejected' %}
                                <h5 class="alert-heading">申请已拒绝</h5>
                                <p class="mb-0">此申请已于 {{ order.updated_at|date:"Y-m-d H:i" }} 被拒绝。</p>
                                {% if order.reject_reason %}
                                <p class="mb-0"><strong>拒绝原因：</strong> {{ order.reject_reason }}</p>
                                {% endif %}
                                {% elif order.status == 'cancelled' %}
                                <h5 class="alert-heading">申请已取消</h5>
                                <p class="mb-0">此申请已于 {{ order.updated_at|date:"Y-m-d H:i" }} 被买家取消。</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-end">
                    <a href="{% url 'services:account_orders_seller' %}" class="btn btn-secondary">返回列表</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 