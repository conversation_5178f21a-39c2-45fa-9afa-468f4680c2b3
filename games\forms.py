from django import forms
from .models import GameAccount, AccountScreenshot

class GameAccountForm(forms.ModelForm):
    """游戏账号表单"""
    # 明日之后特有的职业选项
    CHARACTER_CLASS_CHOICES = [
        ('', '----请选择----'),
        ('铁匠', '铁匠'),
        ('枪手', '枪手'),
        ('木匠', '木匠'),
        ('厨师', '厨师'),
        ('采集者', '采集者'),
        ('医疗师', '医疗师'),
        ('矿工', '矿工'),
        ('服装师', '服装师'),
        ('多职业', '多职业')
    ]
    
    character_class = forms.ChoiceField(choices=CHARACTER_CLASS_CHOICES, label='角色职业')
    
    # 明日之后特有的区服字段
    server_region = forms.ChoiceField(
        choices=[
            ('', '----请选择----'),
            ('雪国', '雪国'),
            ('沙城', '沙城'),
            ('希望谷', '希望谷'),
            ('新世界', '新世界'),
            ('圣托帕', '圣托帕'),
            ('其他', '其他')
        ],
        label='区服地区'
    )
    
    has_special_items = forms.BooleanField(
        required=False, 
        label='是否有稀有物品',
        help_text='例如：限定家具、皮肤、武器等'
    )
    
    manor_level = forms.IntegerField(
        min_value=1, 
        max_value=20,
        label='庄园等级',
        required=False,
        help_text='如果有请填写，范围1-20'
    )
    
    class Meta:
        model = GameAccount
        fields = ['title', 'server', 'level', 'character_class', 'description', 'price', 'image']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
        }
    
    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price <= 0:
            raise forms.ValidationError('价格必须大于0')
        return price

class ScreenshotForm(forms.ModelForm):
    """账号截图表单"""
    class Meta:
        model = AccountScreenshot
        fields = ['image', 'caption']
        widgets = {
            'caption': forms.TextInput(attrs={'placeholder': '请输入截图说明（例如：角色界面、背包、技能等）'})
        } 