from django.urls import path
from . import views

app_name = 'trades'

urlpatterns = [
    # 交易
    path('', views.transaction_list, name='transaction_list'),
    path('<uuid:transaction_id>/', views.transaction_detail, name='transaction_detail'),
    path('create/<int:account_id>/', views.create_transaction, name='create_transaction'),
    
    # 支付
    path('<uuid:transaction_id>/pay/', views.payment, name='payment'),
    path('<uuid:transaction_id>/pay/success/', views.payment_success, name='payment_success'),
    path('<uuid:transaction_id>/pay/cancel/', views.payment_cancel, name='payment_cancel'),
    
    # 交易流程
    path('<uuid:transaction_id>/confirm/', views.confirm_transaction, name='confirm_transaction'),
    path('<uuid:transaction_id>/complete/', views.complete_transaction, name='complete_transaction'),
    path('<uuid:transaction_id>/cancel/', views.cancel_transaction, name='cancel_transaction'),
    
    # 评价
    path('<uuid:transaction_id>/review/', views.create_review, name='create_review'),
    path('reviews/<int:review_id>/', views.review_detail, name='review_detail'),
    
    # 纠纷
    path('<uuid:transaction_id>/dispute/', views.create_dispute, name='create_dispute'),
    path('disputes/<int:dispute_id>/', views.dispute_detail, name='dispute_detail'),
] 