# Systemd服务文件 - 放置在 /etc/systemd/system/game_trade.service

[Unit]
Description=Game Trade Django Application
After=network.target

[Service]
Type=notify
User=root
Group=root
WorkingDirectory=/root/web
Environment=PATH=/root/web/venv/bin
ExecStart=/root/web/venv/bin/gunicorn --config /root/web/gunicorn.conf.py game_trade.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-failure
RestartSec=5
KillMode=mixed
TimeoutStopSec=5

[Install]
WantedBy=multi-user.target
