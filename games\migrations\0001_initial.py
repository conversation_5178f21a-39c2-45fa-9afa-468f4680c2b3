# Generated by Django 4.2.20 on 2025-03-25 08:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Game',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='游戏名称')),
                ('slug', models.SlugField(max_length=100, unique=True, verbose_name='URL别名')),
                ('description', models.TextField(verbose_name='游戏描述')),
                ('logo', models.ImageField(upload_to='game_logos/', verbose_name='游戏logo')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '游戏',
                'verbose_name_plural': '游戏',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GameServer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='服务器名称')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='servers', to='games.game', verbose_name='所属游戏')),
            ],
            options={
                'verbose_name': '游戏服务器',
                'verbose_name_plural': '游戏服务器',
                'unique_together': {('game', 'name')},
            },
        ),
        migrations.CreateModel(
            name='GameAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('description', models.TextField(verbose_name='详细描述')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格')),
                ('character_level', models.PositiveIntegerField(verbose_name='角色等级')),
                ('character_class', models.CharField(max_length=50, verbose_name='角色职业')),
                ('account_details', models.JSONField(help_text='存储账号详细信息的JSON数据', verbose_name='账号详情')),
                ('status', models.CharField(choices=[('pending', '待审核'), ('selling', '出售中'), ('reserved', '已预订'), ('sold', '已售出'), ('canceled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('has_screenshots', models.BooleanField(default=False, verbose_name='有截图')),
                ('is_featured', models.BooleanField(default=False, verbose_name='推荐账号')),
                ('is_verified', models.BooleanField(default=False, verbose_name='已验证')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accounts', to='games.game', verbose_name='游戏')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_accounts', to=settings.AUTH_USER_MODEL, verbose_name='卖家')),
                ('server', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accounts', to='games.gameserver', verbose_name='服务器')),
            ],
            options={
                'verbose_name': '游戏账号',
                'verbose_name_plural': '游戏账号',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AccountScreenshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='account_screenshots/', verbose_name='截图')),
                ('caption', models.CharField(blank=True, max_length=200, verbose_name='说明')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='screenshots', to='games.gameaccount', verbose_name='游戏账号')),
            ],
            options={
                'verbose_name': '账号截图',
                'verbose_name_plural': '账号截图',
            },
        ),
    ]
