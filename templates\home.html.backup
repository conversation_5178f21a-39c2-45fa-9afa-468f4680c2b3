{% extends 'base.html' %}
{% load static %}

{% block title %}梦羽明日之后 - 安全可靠的账号交易服务{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('{% static "img/hero-bg.jpg" %}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        position: relative;
        overflow: hidden;
        min-height: 80vh;
        display: flex;
        align-items: center;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.2;
    }

    .hero-content {
        position: relative;
        z-index: 3;
        backdrop-filter: blur(1px);
        background: rgba(0,0,0,0.1);
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(255,255,255,0.1);
    }

    .floating-icon {
        position: absolute;
        animation: float 6s ease-in-out infinite;
        opacity: 0.15;
        font-size: 3rem;
        color: white;
        text-shadow: 0 0 20px rgba(255,255,255,0.3);
        z-index: 2;
    }

    .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
    .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 2s; }
    .floating-icon:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 4s; }
    .floating-icon:nth-child(4) { bottom: 20%; right: 10%; animation-delay: 1s; }
    .floating-icon:nth-child(5) { top: 50%; left: 5%; animation-delay: 3s; }
    .floating-icon:nth-child(6) { top: 60%; right: 8%; animation-delay: 5s; }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .feature-card {
        transition: all 0.3s ease;
        border: none;
        background: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
        color: white;
        background: linear-gradient(45deg, #667eea, #764ba2);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .stats-counter {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
    }

    .game-bg {
        background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)),
                    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><rect width="1000" height="1000" fill="%23000"/><g fill="none" stroke="%23333" stroke-width="1"><circle cx="500" cy="500" r="100"/><circle cx="500" cy="500" r="200"/><circle cx="500" cy="500" r="300"/></g></svg>');
        background-size: cover;
        background-position: center;
    }

    .process-step {
        position: relative;
    }

    .process-step::after {
        content: '';
        position: absolute;
        top: 30px;
        right: -50%;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, #667eea, transparent);
        z-index: 1;
    }

    .process-step:last-child::after {
        display: none;
    }

    .btn-game {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-game:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 3rem;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 2px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 覆盖主内容容器，使用全宽布局 -->
</main>

<!-- 全宽内容开始 -->

<!-- 英雄区域 - 全屏宽度 -->
<section class="hero-section">
    <div class="floating-icon"><i class="bi bi-controller"></i></div>
    <div class="floating-icon"><i class="bi bi-trophy"></i></div>
    <div class="floating-icon"><i class="bi bi-gem"></i></div>
    <div class="floating-icon"><i class="bi bi-shield-check"></i></div>
    <div class="floating-icon"><i class="bi bi-star"></i></div>
    <div class="floating-icon"><i class="bi bi-heart"></i></div>

    <div class="container-fluid px-4">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-5 col-lg-6 hero-content">
                <h1 class="display-3 text-white fw-bold mb-4">
                    <span class="d-block">梦羽明日之后</span>
                    <small class="text-white-50 fs-4">专业游戏账号交易平台</small>
                </h1>
                <p class="lead text-white-75 mb-4">
                    找《明日之后》游戏服务，上梦羽明日之后<br>
                    无论您是想回游、退坑，还是寻求代肝、出/收金或其它明日服务，我们满足您的一切需求
                </p>
                <div class="d-flex flex-wrap gap-3 mb-4">
                    <a class="btn btn-game btn-lg" href="{% url 'services:account_list' %}">
                        <i class="bi bi-search me-2"></i>浏览账号
                    </a>
                    {% if user.is_authenticated %}
                    <a class="btn btn-outline-light btn-lg" href="{% url 'services:sell_account' %}">
                        <i class="bi bi-plus-circle me-2"></i>出售账号
                    </a>
                    {% else %}
                    <a class="btn btn-outline-light btn-lg" href="{% url 'accounts:register' %}">
                        <i class="bi bi-person-plus me-2"></i>立即注册
                    </a>
                    {% endif %}
                </div>

                <!-- 统计数据 -->
                <div class="row text-white mt-5">
                    <div class="col-4 text-center">
                        <div class="stats-counter text-white">1000+</div>
                        <small class="text-white-50">账号交易</small>
                    </div>
                    <div class="col-4 text-center">
                        <div class="stats-counter text-white">500+</div>
                        <small class="text-white-50">活跃用户</small>
                    </div>
                    <div class="col-4 text-center">
                        <div class="stats-counter text-white">99%</div>
                        <small class="text-white-50">好评率</small>
                    </div>
                </div>
            </div>
            <div class="col-xl-5 col-lg-6 text-center">
                <div class="position-relative">
                    <img src="https://via.placeholder.com/600x450/667eea/ffffff?text=明日之后"
                         class="img-fluid rounded-3 shadow-lg"
                         alt="明日之后游戏截图"
                         style="max-width: 100%;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 游戏介绍 -->
<section class="py-5 bg-light">
    <div class="container-fluid px-4">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-5 col-lg-6 mb-4 mb-lg-0">
                <h2 class="section-title text-center text-lg-start">关于《明日之后》</h2>
                <div class="game-info">
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="bi bi-globe"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">末日生存世界</h5>
                                <p class="text-muted mb-0">病毒席卷全球，在废土世界中求生</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="bi bi-house"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">庄园建造</h5>
                                <p class="text-muted mb-0">建造专属庄园，打造理想家园</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="bi bi-people"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">多人协作</h5>
                                <p class="text-muted mb-0">与好友组队探索，共同面对挑战</p>
                            </div>
                        </div>
                    </div>

                    <p class="lead text-muted">
                        无论您是生存达人还是收藏爱好者，这里都能找到适合您的游戏账号。
                    </p>
                </div>
            </div>
            <div class="col-xl-5 col-lg-6">
                <div class="position-relative">
                    <div class="row g-3">
                        <div class="col-6">
                            <img src="https://via.placeholder.com/350x250/667eea/ffffff?text=庄园建造"
                                 class="img-fluid rounded shadow" alt="庄园建造">
                        </div>
                        <div class="col-6">
                            <img src="https://via.placeholder.com/350x250/764ba2/ffffff?text=武器装备"
                                 class="img-fluid rounded shadow" alt="武器装备">
                        </div>
                        <div class="col-12">
                            <img src="https://via.placeholder.com/700x300/5a67d8/ffffff?text=游戏世界"
                                 class="img-fluid rounded shadow" alt="游戏世界">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 平台特色 -->
<section class="py-5">
    <div class="container-fluid px-4">
        <h2 class="section-title text-center">平台特色</h2>
        <div class="row g-4 justify-content-center">
            <!-- 渠道服账号 -->
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                <div class="feature-card h-100 text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h3 class="h4 mb-3">渠道服账号</h3>
                    <p class="text-muted mb-4">提供iOS、安卓等多渠道服务器账号，满足不同玩家需求</p>
                    <ul class="list-unstyled text-start mb-4">
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> iOS渠道服</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 安卓渠道服</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 跨服账号</li>
                    </ul>
                    <a href="{% url 'services:account_list' %}" class="btn btn-game">
                        浏览账号 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <!-- 大神攻略 -->
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                <div class="feature-card h-100 text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-journal-text"></i>
                    </div>
                    <h3 class="h4 mb-3">大神攻略</h3>
                    <p class="text-muted mb-4">七年老牌攻略博主提供的高质量游戏攻略和技巧分享</p>
                    <ul class="list-unstyled text-start mb-4">
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 新手指南</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 进阶技巧</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 职业攻略</li>
                    </ul>
                    <a href="{% url 'services:guide_list' %}" class="btn btn-game">
                        查看攻略 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <!-- 特色服务 -->
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                <div class="feature-card h-100 text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-star"></i>
                    </div>
                    <h3 class="h4 mb-3">特色服务</h3>
                    <p class="text-muted mb-4">7年资深博主提供的专业游戏服务</p>
                    <ul class="list-unstyled text-start mb-4">
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 账号调整</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 游戏答疑</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 在线直播</li>
                    </ul>
                    <div class="dropdown">
                        <button class="btn btn-game dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            选择服务
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'services:account_adjustment' %}">
                                <i class="bi bi-gear-fill me-2"></i> 账号调整
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'services:game_qa' %}">
                                <i class="bi bi-question-circle-fill me-2"></i> 游戏答疑
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'services:live_stream' %}">
                                <i class="bi bi-broadcast me-2"></i> 在线直播
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 玩家社区 -->
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                <div class="feature-card h-100 text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <h3 class="h4 mb-3">玩家社区</h3>
                    <p class="text-muted mb-4">参与游戏讨论，分享游戏经验，与其他玩家交流互动</p>
                    <ul class="list-unstyled text-start mb-4">
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 游戏攻略</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 问题解答</li>
                        <li class="mb-2"><i class="bi bi-check2 text-success me-2"></i> 经验分享</li>
                    </ul>
                    <a href="{% url 'services:community' %}" class="btn btn-game">
                        进入社区 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- 心愿工坊特色展示 -->
        <div class="row mt-5 justify-content-center">
            <div class="col-xl-10 col-12">
                <div class="feature-card p-5 text-center position-relative overflow-hidden">
                    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); z-index: 1;"></div>
                    <div class="position-relative" style="z-index: 2;">
                        <div class="feature-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem;">
                            <i class="bi bi-gift"></i>
                        </div>
                        <h2 class="mb-4">心愿工坊</h2>
                        <p class="lead text-muted mb-5">满足您的各类游戏需求，寻找您想要的服务或提供您的专业技能</p>

                        <div class="row g-4 mb-5 justify-content-center">
                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                <div class="d-flex flex-column align-items-center p-3 rounded" style="background: rgba(255,255,255,0.8);">
                                    <div class="feature-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        <i class="bi bi-controller"></i>
                                    </div>
                                    <h5 class="mb-2">代肝服务</h5>
                                    <p class="text-muted small mb-0">专业代练，快速升级</p>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                <div class="d-flex flex-column align-items-center p-3 rounded" style="background: rgba(255,255,255,0.8);">
                                    <div class="feature-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        <i class="bi bi-map"></i>
                                    </div>
                                    <h5 class="mb-2">跑图服务</h5>
                                    <p class="text-muted small mb-0">地图探索，资源收集</p>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                <div class="d-flex flex-column align-items-center p-3 rounded" style="background: rgba(255,255,255,0.8);">
                                    <div class="feature-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        <i class="bi bi-trophy"></i>
                                    </div>
                                    <h5 class="mb-2">PVP代打</h5>
                                    <p class="text-muted small mb-0">竞技场代打，提升排名</p>
                                </div>
                            </div>
                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                                <div class="d-flex flex-column align-items-center p-3 rounded" style="background: rgba(255,255,255,0.8);">
                                    <div class="feature-icon mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        <i class="bi bi-coin"></i>
                                    </div>
                                    <h5 class="mb-2">金条交易</h5>
                                    <p class="text-muted small mb-0">安全金条买卖</p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-wrap justify-content-center gap-3">
                            <a href="{% url 'services:wishlist_service' %}" class="btn btn-game btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>发布需求
                            </a>
                            <a href="{% url 'services:wishlist_provide' %}" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-briefcase me-2"></i>提供服务
                            </a>
                            <a href="{% url 'services:wishlist_list' %}" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-list me-2"></i>浏览全部
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 热门区服 -->
<section class="py-5">
    <div class="container-fluid px-4">
        <h2 class="text-center mb-4">热门区服</h2>
        <div class="row justify-content-center">
            <div class="col-xl-8 col-lg-10">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-bar-chart-fill"></i> 区服账号数量统计</h5>
                            <span id="last-updated" class="small"></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>区服名称</th>
                                        <th>账号数量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="popular-servers">
                                    <tr>
                                        <td colspan="4" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="text-end">
                            <a href="{% url 'services:account_list' %}" class="btn btn-outline-primary btn-sm">
                                浏览全部账号 <i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 交易流程 -->
<section class="py-5 bg-light">
    <div class="container-fluid px-4">
        <h2 class="section-title text-center">安全交易流程</h2>
        <p class="text-center text-muted mb-5 lead">7步安全交易，保障买卖双方权益</p>

        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-11">
                <!-- 流程步骤 -->
                <div class="row g-4 mb-5">
                    <div class="col-lg-3 col-md-6 process-step">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">1</span>
                            </div>
                            <h5 class="mb-3">提交订单</h5>
                            <p class="text-muted small">选择心仪账号后提交订单，确认交易意向</p>
                            <div class="mt-3">
                                <i class="bi bi-cart-plus text-primary fs-4"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 process-step">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">2</span>
                            </div>
                            <h5 class="mb-3">客服拉群</h5>
                            <p class="text-muted small">专业客服建立交易群聊，确保沟通顺畅</p>
                            <div class="mt-3">
                                <i class="bi bi-chat-dots text-primary fs-4"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 process-step">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">3</span>
                            </div>
                            <h5 class="mb-3">买家验号</h5>
                            <p class="text-muted small">验证账号信息、角色属性、物品真实性</p>
                            <div class="mt-3">
                                <i class="bi bi-search text-primary fs-4"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 process-step">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">4</span>
                            </div>
                            <h5 class="mb-3">合同签署</h5>
                            <p class="text-muted small">电子合同确保交易双方权益，明确交易细则</p>
                            <div class="mt-3">
                                <i class="bi bi-file-earmark-text text-primary fs-4"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行流程 -->
                <div class="row g-4 justify-content-center mb-5">
                    <div class="col-lg-4 col-md-6 process-step">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">5</span>
                            </div>
                            <h5 class="mb-3">买家付款</h5>
                            <p class="text-muted small">通过平台安全支付系统完成交易付款</p>
                            <div class="mt-3">
                                <i class="bi bi-credit-card text-primary fs-4"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 process-step">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">6</span>
                            </div>
                            <h5 class="mb-3">账号换绑</h5>
                            <p class="text-muted small">安全完成账号所有权转移，确认登录信息</p>
                            <div class="mt-3">
                                <i class="bi bi-arrow-left-right text-primary fs-4"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="feature-card h-100 text-center p-4 position-relative">
                            <div class="feature-icon mx-auto mb-3" style="width: 70px; height: 70px; font-size: 1.8rem;">
                                <span class="fw-bold">7</span>
                            </div>
                            <h5 class="mb-3">卖家收款</h5>
                            <p class="text-muted small">交易完成后卖家安全收款，平台结算佣金</p>
                            <div class="mt-3">
                                <i class="bi bi-check-circle text-success fs-4"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <a href="{% url 'services:account_list' %}" class="btn btn-game btn-lg">
                        <i class="bi bi-rocket me-2"></i>立即开始交易
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 用户评价 -->
<section class="py-5">
    <div class="container">
        <h2 class="section-title text-center">用户评价</h2>
        <p class="text-center text-muted mb-5 lead">听听用户的真实声音</p>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 position-relative">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="bi bi-person"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">李先生</h5>
                            <div class="text-warning">
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                            </div>
                        </div>
                    </div>
                    <blockquote class="blockquote mb-0">
                        <p class="text-muted">"在这里买到了一个满级庄园账号，还带有很多限定家具，非常满意！客服服务也很专业。"</p>
                    </blockquote>
                    <div class="position-absolute top-0 end-0 p-3">
                        <i class="bi bi-quote text-primary opacity-25" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 position-relative">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="bi bi-person"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">王女士</h5>
                            <div class="text-warning">
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-half"></i>
                            </div>
                        </div>
                    </div>
                    <blockquote class="blockquote mb-0">
                        <p class="text-muted">"出售了我的明日之后账号，价格合理，交易过程中平台提供了很好的安全保障，推荐！"</p>
                    </blockquote>
                    <div class="position-absolute top-0 end-0 p-3">
                        <i class="bi bi-quote text-success opacity-25" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 position-relative">
                    <div class="d-flex align-items-center mb-3">
                        <div class="rounded-circle bg-info text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="bi bi-person"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">张先生</h5>
                            <div class="text-warning">
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star"></i>
                            </div>
                        </div>
                    </div>
                    <blockquote class="blockquote mb-0">
                        <p class="text-muted">"寻找了很久的特殊皮肤账号，终于在这里找到了，交易很顺利，值得信赖的平台！"</p>
                    </blockquote>
                    <div class="position-absolute top-0 end-0 p-3">
                        <i class="bi bi-quote text-info opacity-25" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信任指标 -->
        <div class="row mt-5 text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="p-3">
                    <div class="display-4 text-primary mb-2">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h4 class="text-primary">安全保障</h4>
                    <p class="text-muted">多重安全验证</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="p-3">
                    <div class="display-4 text-success mb-2">
                        <i class="bi bi-clock"></i>
                    </div>
                    <h4 class="text-success">快速交易</h4>
                    <p class="text-muted">平均30分钟完成</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="p-3">
                    <div class="display-4 text-warning mb-2">
                        <i class="bi bi-headset"></i>
                    </div>
                    <h4 class="text-warning">专业客服</h4>
                    <p class="text-muted">24小时在线服务</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="p-3">
                    <div class="display-4 text-info mb-2">
                        <i class="bi bi-award"></i>
                    </div>
                    <h4 class="text-info">品质保证</h4>
                    <p class="text-muted">严格账号审核</p>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载热门区服数据
        loadPopularServers();

        // 每60秒自动刷新一次
        setInterval(loadPopularServers, 60000);

        // 添加滚动动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有特色卡片
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // 数字计数动画
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current) + (target >= 1000 ? '+' : target === 99 ? '%' : '+');
            }, 20);
        }

        // 当统计数据进入视口时开始动画
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counters = entry.target.querySelectorAll('.stats-counter');
                    counters.forEach(counter => {
                        const target = parseInt(counter.textContent);
                        animateCounter(counter, target);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        });

        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            statsObserver.observe(heroSection);
        }

        // 按钮悬停效果增强
        document.querySelectorAll('.btn-game').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });

    function loadPopularServers() {
        fetch('{% url "games:popular_servers" %}')
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('popular-servers');

            if (data.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center">
                            <div class="py-4">
                                <i class="bi bi-inbox display-4 text-muted mb-3"></i>
                                <p class="text-muted mb-3">目前没有足够的账号数据</p>
                                <a href="{% url 'services:sell_account' %}" class="btn btn-game">
                                    <i class="bi bi-plus-circle me-2"></i>出售您的账号
                                </a>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            data.forEach((server, index) => {
                const rankClass = index === 0 ? 'text-warning' : index === 1 ? 'text-secondary' : index === 2 ? 'text-warning' : '';
                const rankIcon = index < 3 ? '<i class="bi bi-trophy-fill me-1"></i>' : '';

                html += `
                    <tr class="table-row-hover">
                        <td class="${rankClass}">
                            ${rankIcon}${index + 1}
                        </td>
                        <td class="fw-semibold">${server.server}</td>
                        <td>
                            <span class="badge bg-gradient bg-primary rounded-pill px-3">${server.account_count}</span>
                        </td>
                        <td>
                            <a href="{% url 'services:account_list' %}?server=${encodeURIComponent(server.server)}"
                               class="btn btn-sm btn-outline-primary rounded-pill">
                                <i class="bi bi-eye me-1"></i>查看账号
                            </a>
                        </td>
                    </tr>
                `;
            });

            tableBody.innerHTML = html;

            // 更新最后刷新时间
            const now = new Date();
            document.getElementById('last-updated').textContent =
                `最后更新: ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
        })
        .catch(error => {
            console.error('获取热门区服数据失败:', error);
            document.getElementById('popular-servers').innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-danger py-4">
                        <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
                        <p class="mb-0">获取数据失败，请稍后再试</p>
                    </td>
                </tr>
            `;
        });
    }
</script>

<style>
    .table-row-hover:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
        transition: all 0.2s ease;
    }

    .bg-gradient {
        background: linear-gradient(45deg, #667eea, #764ba2) !important;
    }

    /* 添加一些额外的动画效果 */
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
        100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
    }

    .btn-game:hover {
        animation: pulse 1.5s infinite;
    }

    .feature-icon:hover {
        transform: rotate(360deg);
        transition: transform 0.6s ease;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .hero-section {
            background-attachment: scroll;
            min-height: 60vh;
        }

        .hero-content {
            padding: 1.5rem;
            margin: 1rem;
        }

        .floating-icon {
            font-size: 2rem;
            opacity: 0.1;
        }

        .stats-counter {
            font-size: 2rem;
        }
    }

    @media (max-width: 576px) {
        .hero-section {
            min-height: 50vh;
        }

        .hero-content {
            padding: 1rem;
            margin: 0.5rem;
        }

        .floating-icon {
            display: none;
        }
    }
</style>
{% endblock %}