{% extends 'base.html' %}
{% load static %}

{% block title %}{{ account.title }} - 账号详情{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ account.title }}</li>
        </ol>
    </nav>
    
    <!-- 账号标题和基本信息 -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="card-title h3 mb-0">{{ account.title }}</h1>
                <span class="badge bg-warning text-dark">明日之后</span>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- 账号图片 -->
                <div class="col-md-6">
                    {% if images %}
                    <div id="accountImagesCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            {% for image in images %}
                            <button type="button" data-bs-target="#accountImagesCarousel" data-bs-slide-to="{{ forloop.counter0 }}" {% if forloop.first %}class="active"{% endif %} aria-label="Slide {{ forloop.counter }}"></button>
                            {% endfor %}
                        </div>
                        <div class="carousel-inner">
                            {% for image in images %}
                            <div class="carousel-item {% if forloop.first %}active{% endif %}">
                                <img src="{{ image.image.url }}" class="d-block w-100" alt="{{ image.caption }}" style="max-height: 400px; object-fit: contain;">
                                {% if image.caption %}
                                <div class="carousel-caption d-none d-md-block bg-dark bg-opacity-50 rounded">
                                    <p class="mb-0">{{ image.caption }}</p>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#accountImagesCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">上一张</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#accountImagesCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">下一张</span>
                        </button>
                    </div>
                    
                    <!-- 缩略图 -->
                    {% if images.count > 1 %}
                    <div class="mt-2">
                        <div class="row g-1">
                            {% for image in images %}
                            <div class="col-3">
                                <img src="{{ image.image.url }}" class="img-thumbnail" alt="{{ image.caption }}" style="cursor: pointer; height: 80px; object-fit: cover;" data-index="{{ forloop.counter0 }}">
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                        <span class="text-muted">无图片</span>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 账号信息 -->
                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="text-danger fw-bold fs-2">¥ {{ account.price }}</h5>
                        <div>
                            <span class="badge bg-info">{{ account.get_account_level_display }}</span>
                            {% if account.profession %}
                            <span class="badge bg-success">{{ account.get_profession_display }}</span>
                            {% endif %}
                            {% if account.server %}
                            <span class="badge bg-warning text-dark">{{ account.server }}</span>
                            {% endif %}
                            {% if account.device_type %}
                            <span class="badge bg-dark">{{ account.device_type }}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">发布时间: {{ account.created_at|date:"Y-m-d H:i" }}</small>
                        <small class="text-muted ms-3">浏览量: {{ account.view_count }}</small>
                    </div>
                    
                    <!-- 标签 -->
                    {% if account.tags.all %}
                    <div class="mb-3">
                        {% for tag in account.tags.all %}
                        <span class="badge bg-light text-dark border me-1">{{ tag.name }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <hr>
                    
                    <!-- 申请购买按钮 -->
                    <div class="mb-3">
                        {% if user.is_authenticated %}
                            {% if user == account.seller %}
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>您是此账号的卖家，无法购买自己的账号
                            </div>
                            {% elif has_applied %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle-fill me-2"></i>您已经提交过购买申请，请前往<a href="{% url 'services:my_account_orders' %}" class="alert-link">我的购买申请</a>查看
                            </div>
                            {% else %}
                            <a href="{% url 'services:apply_for_account' account_id=account.id %}" class="btn btn-primary btn-lg w-100">
                                <i class="bi bi-bag-check me-2"></i>申请购买
                            </a>
                            {% endif %}
                        {% else %}
                        <a href="{% url 'login' %}?next={{ request.path }}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-box-arrow-in-right me-2"></i>登录后申请购买
                        </a>
                        {% endif %}
                    </div>
                    
                    <!-- 交易须知 -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 h6">交易须知</h5>
                        </div>
                        <div class="card-body">
                            <ul class="small mb-0">
                                <li>本平台为第三方中介，保障交易安全</li>
                                <li>提交申请后，平台会联系您确认交易细节</li>
                                <li>交易过程中遇到问题，请联系平台客服</li>
                                <li>禁止线下交易，平台不对线下交易负责</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 账号详情信息 -->
    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="accountDetailTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab" aria-controls="description" aria-selected="true">账号描述</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="game-roles-tab" data-bs-toggle="tab" data-bs-target="#game-roles" type="button" role="tab" aria-controls="game-roles" aria-selected="false">游戏角色</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="game-items-tab" data-bs-toggle="tab" data-bs-target="#game-items" type="button" role="tab" aria-controls="game-items" aria-selected="false">游戏物品</button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="accountDetailTabsContent">
                <!-- 账号描述 -->
                <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                    <div class="account-description">
                        {{ account.description|linebreaks }}
                    </div>
                </div>
                
                <!-- 游戏角色 -->
                <div class="tab-pane fade" id="game-roles" role="tabpanel" aria-labelledby="game-roles-tab">
                    {% if account.game_roles and account.game_roles.roles %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>角色名称</th>
                                    <th>角色等级</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for role in account.game_roles.roles %}
                                <tr>
                                    <td>{{ role.name }}</td>
                                    <td>{{ role.level }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-light">
                        <i class="bi bi-info-circle me-2"></i>卖家未提供角色信息
                    </div>
                    {% endif %}
                </div>
                
                <!-- 游戏物品 -->
                <div class="tab-pane fade" id="game-items" role="tabpanel" aria-labelledby="game-items-tab">
                    {% if account.game_items and account.game_items.items %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>物品名称</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in account.game_items.items %}
                                <tr>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.quantity }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-light">
                        <i class="bi bi-info-circle me-2"></i>卖家未提供物品信息
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 相关账号推荐 -->
    {% if related_accounts %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">相关账号推荐</h5>
        </div>
        <div class="card-body">
            <div class="row row-cols-1 row-cols-md-4 g-4">
                {% for related in related_accounts %}
                <div class="col">
                    <div class="card h-100">
                        <div class="position-relative">
                            {% if related.main_image %}
                            <img src="{{ related.main_image.image.url }}" class="card-img-top" alt="{{ related.title }}" style="height: 150px; object-fit: cover;">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                                <span class="text-muted">无图片</span>
                            </div>
                            {% endif %}
                            <span class="position-absolute top-0 end-0 badge bg-danger m-2">¥ {{ related.price }}</span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title fs-6">{{ related.title }}</h5>
                            <div class="mb-2">
                                <span class="badge bg-secondary">{{ related.get_game_type_display }}</span>
                                <span class="badge bg-info">{{ related.get_account_level_display }}</span>
                            </div>
                            <a href="{% url 'services:account_detail' account_id=related.id %}" class="stretched-link"></a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% if images.count > 1 %}
{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 缩略图点击功能
    document.querySelectorAll('.img-thumbnail').forEach(function(thumb) {
        thumb.addEventListener('click', function() {
            var index = this.getAttribute('data-index');
            var carousel = new bootstrap.Carousel(document.getElementById('accountImagesCarousel'));
            carousel.to(parseInt(index));
        });
    });
});
</script>
{% endblock %}
{% endif %}
{% endblock %} 