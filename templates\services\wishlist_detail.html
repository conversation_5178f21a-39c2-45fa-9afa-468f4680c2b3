{% extends 'base.html' %}

{% block title %}{{ service.title }} - 心愿工坊{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header {% if service.is_offering %}bg-success{% else %}bg-danger{% endif %} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            {% if service.is_offering %}
                            <i class="bi bi-briefcase"></i> 提供服务
                            {% else %}
                            <i class="bi bi-search"></i> 需求服务
                            {% endif %}
                        </h4>
                        <span class="badge bg-light text-dark">{{ service.get_service_type_display }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <h3 class="card-title mb-4">{{ service.title }}</h3>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="bi bi-person text-muted"></i> 发布人：{{ service.user.username }}
                            </p>
                            <p class="mb-2">
                                <i class="bi bi-clock text-muted"></i> 发布时间：{{ service.created_at|date:"Y-m-d H:i" }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="bi bi-tag text-muted"></i> 服务类型：{{ service.get_service_type_display }}
                            </p>
                            <p class="mb-2">
                                <i class="bi bi-currency-yen text-muted"></i> 价格：
                                {% if service.price %}
                                <span class="text-danger fw-bold">¥{{ service.price }}</span>
                                {% if service.price_negotiable %}<small class="text-muted">（可议价）</small>{% endif %}
                                {% else %}
                                <span class="text-muted">价格面议</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">详细描述</h5>
                        </div>
                        <div class="card-body">
                            {{ service.description|linebreaks }}
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">交易方式</h5>
                        </div>
                        <div class="card-body">
                            {% if user.is_authenticated %}
                                {% if user.is_staff %}
                                <div class="alert alert-info mb-3">
                                    <strong>管理员可见信息：</strong>
                                    <p>联系方式: {{ service.contact_info }}</p>
                                </div>
                                {% endif %}
                                
                                <div class="alert alert-primary" role="alert">
                                    <i class="bi bi-info-circle"></i> 为保障交易安全，联系方式仅对平台管理员可见。
                                </div>
                                
                                <div class="alert alert-success mb-0" role="alert">
                                    <h5 class="alert-heading"><i class="bi bi-shield-check"></i> 安全交易流程</h5>
                                    <p>点击下方"申请交易"按钮，我们的客服将在1-24小时内联系您，并创建交易群聊。</p>
                                    <hr>
                                    <p class="mb-0">平台将收取一定比例的服务费，但可为您提供：</p>
                                    <ul class="mb-0">
                                        <li>交易担保</li>
                                        <li>纠纷处理</li>
                                        <li>专业客服</li>
                                    </ul>
                                </div>
                            {% else %}
                                <div class="alert alert-warning mb-0" role="alert">
                                    <i class="bi bi-shield-exclamation"></i> 请<a href="{% url 'accounts:login' %}?next={{ request.path }}" class="alert-link">登录</a>后申请交易。
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if user.is_authenticated and service.user == user %}
                    <div class="alert alert-primary" role="alert">
                        <i class="bi bi-info-circle"></i> 这是您发布的服务。
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'services:wishlist_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                        
                        {% if user.is_authenticated and service.user != user %}
                        <button class="btn {% if service.is_offering %}btn-success{% else %}btn-danger{% endif %}"
                               id="applyTradeBtn">
                            {% if service.is_offering %}
                            <i class="bi bi-cart"></i> 申请购买
                            {% else %}
                            <i class="bi bi-hand-thumbs-up"></i> 申请提供
                            {% endif %}
                        </button>
                        {% endif %}
                        
                        {% if user.is_authenticated and service.user == user %}
                        <div>
                            <button class="btn btn-warning me-2">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <button class="btn btn-danger">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 申请交易模态框 -->
<div class="modal fade" id="applyTradeModal" tabindex="-1" aria-labelledby="applyTradeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="applyTradeModalLabel">申请交易</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="tradeApplicationForm" action="{% url 'services:apply_trade' service_id=service.id %}" method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="contactPreference" class="form-label">联系方式偏好</label>
                        <select class="form-select" id="contactPreference" name="contact_preference" required>
                            <option value="wechat">微信</option>
                            <option value="qq">QQ</option>
                            <option value="phone">电话</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">留言信息</label>
                        <textarea class="form-control" id="message" name="message" rows="3" placeholder="请简要描述您的需求或提供更多细节..."></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="agreeTerms" name="agree_terms" required>
                        <label class="form-check-label" for="agreeTerms">
                            我同意平台<a href="#" target="_blank">交易规则</a>和<a href="#" target="_blank">服务条款</a>
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitApplicationBtn">提交申请</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 申请交易按钮点击事件
        const applyTradeBtn = document.getElementById('applyTradeBtn');
        const submitApplicationBtn = document.getElementById('submitApplicationBtn');
        const tradeApplicationForm = document.getElementById('tradeApplicationForm');
        
        if (applyTradeBtn) {
            applyTradeBtn.addEventListener('click', function() {
                var modal = new bootstrap.Modal(document.getElementById('applyTradeModal'));
                modal.show();
            });
        }
        
        if (submitApplicationBtn) {
            submitApplicationBtn.addEventListener('click', function() {
                if (tradeApplicationForm.checkValidity()) {
                    // 显示加载状态
                    this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...';
                    this.disabled = true;
                    
                    // 提交表单
                    tradeApplicationForm.submit();
                } else {
                    tradeApplicationForm.reportValidity();
                }
            });
        }
    });
</script>
{% endblock %} 