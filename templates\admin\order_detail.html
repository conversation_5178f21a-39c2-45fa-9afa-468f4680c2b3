{% extends "admin/base_site.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
<style>
    .order-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    .status-badge {
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        display: inline-block;
    }
    .status-pending {
        background-color: #ffc107;
        color: #212529;
    }
    .status-processing {
        background-color: #17a2b8;
        color: #fff;
    }
    .status-completed {
        background-color: #28a745;
        color: #fff;
    }
    .status-rejected {
        background-color: #dc3545;
        color: #fff;
    }
    .status-cancelled {
        background-color: #6c757d;
        color: #fff;
    }
</style>
{% endblock %}

{% block content %}
<div class="order-container">
    <div id="content-main">
        <h1>{{ title }}</h1>
        
        <div class="module">
            <h2>订单基本信息</h2>
            <div class="form-row">
                <div>
                    <table>
                        <tr>
                            <th>订单ID:</th>
                            <td>{{ order.id }}</td>
                        </tr>
                        <tr>
                            <th>账号标题:</th>
                            <td>{{ order.account.title }}</td>
                        </tr>
                        <tr>
                            <th>账号价格:</th>
                            <td>¥{{ order.account.price }}</td>
                        </tr>
                        <tr>
                            <th>卖家:</th>
                            <td>{{ order.account.seller.get_display_name }} ({{ order.account.seller.email }})</td>
                        </tr>
                        <tr>
                            <th>买家:</th>
                            <td>{{ order.buyer.get_display_name }} ({{ order.buyer.email }})</td>
                        </tr>
                        <tr>
                            <th>状态:</th>
                            <td>
                                <span class="status-badge status-{{ order.status }}">
                                    {% if order.status == 'pending' %}待处理
                                    {% elif order.status == 'processing' %}处理中
                                    {% elif order.status == 'completed' %}已完成
                                    {% elif order.status == 'rejected' %}已拒绝
                                    {% elif order.status == 'cancelled' %}已取消
                                    {% endif %}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>创建时间:</th>
                            <td>{{ order.created_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                        <tr>
                            <th>更新时间:</th>
                            <td>{{ order.updated_at|date:"Y-m-d H:i:s" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="module">
            <h2>买家联系方式 (仅管理员可见)</h2>
            <div class="form-row">
                <div>
                    <table>
                        <tr>
                            <th>联系方式类型:</th>
                            <td>{{ order.get_contact_method_display }}</td>
                        </tr>
                        <tr>
                            <th>联系方式:</th>
                            <td>{{ order.contact_info }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        {% if order.message %}
        <div class="module">
            <h2>买家留言</h2>
            <div class="form-row">
                <div style="padding: 15px; background: #f9f9f9; border-radius: 4px;">
                    {{ order.message|linebreaks }}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if order.status == 'rejected' and order.reject_reason %}
        <div class="module">
            <h2>拒绝原因</h2>
            <div class="form-row">
                <div style="padding: 15px; background: #f9f9f9; border-radius: 4px;">
                    {{ order.reject_reason|linebreaks }}
                </div>
            </div>
        </div>
        {% endif %}
        
        <div style="margin-top: 20px;">
            <div class="submit-row">
                <a href="{{ request.META.HTTP_REFERER|default:'/' }}" class="button">
                    返回
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 