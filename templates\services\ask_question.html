{% extends 'base.html' %}
{% load static %}

{% block title %}提交问题 - 游戏答疑{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:game_qa' %}">游戏答疑</a></li>
            <li class="breadcrumb-item active" aria-current="page">提交问题</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title h4 mb-0">提交您的问题</h2>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">问题标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required maxlength="100" placeholder="简洁描述您的问题">
                            <div class="form-text">请用简洁的标题描述您的问题（5-100字）</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="form-label">问题分类 <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">请选择问题分类</option>
                                {% for cat_code, cat_name in categories %}
                                <option value="{{ cat_code }}">{{ cat_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">问题详情 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="content" name="content" rows="10" required placeholder="请详细描述您遇到的问题，包括重现步骤、相关环境信息等"></textarea>
                            <div class="form-text">详细的描述有助于我们更准确地回答您的问题</div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                                <label class="form-check-label" for="is_public">
                                    公开此问题（其他用户可以看到此问题及回答）
                                </label>
                                <div class="form-text">公开的问题可以帮助其他遇到类似问题的玩家</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'services:game_qa' %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">提交问题</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    提问小贴士
                </div>
                <div class="card-body">
                    <h5>如何提出一个好问题？</h5>
                    <ul>
                        <li>使用具体、清晰的标题</li>
                        <li>详细描述问题的背景和环境</li>
                        <li>说明您已经尝试过的解决方法</li>
                        <li>提供相关的截图或游戏信息（如角色等级、区服等）</li>
                        <li>使用清晰的语言，避免使用过多的简写或游戏术语</li>
                    </ul>
                    <div class="alert alert-info mb-0">
                        <i class="bi bi-info-circle"></i> 提交后，客服团队会在24小时内回复您的问题。您可以在"我的问题"页面查看回复状态。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 