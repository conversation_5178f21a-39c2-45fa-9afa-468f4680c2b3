# Generated by Django 4.2.20 on 2025-03-25 08:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('games', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='gameaccount',
            name='has_special_items',
            field=models.BooleanField(default=False, verbose_name='拥有稀有物品'),
        ),
        migrations.AddField(
            model_name='gameaccount',
            name='manor_level',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='庄园等级'),
        ),
        migrations.AddField(
            model_name='gameaccount',
            name='server_region',
            field=models.CharField(choices=[('雪国', '雪国'), ('沙城', '沙城'), ('希望谷', '希望谷'), ('新世界', '新世界'), ('圣托帕', '圣托帕'), ('其他', '其他')], default='其他', max_length=50, verbose_name='区服地区'),
        ),
    ]
