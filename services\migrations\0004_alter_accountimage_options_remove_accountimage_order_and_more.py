# Generated by Django 4.2 on 2025-03-26 08:55

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0003_alter_servicegameaccount_account_level'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='accountimage',
            options={},
        ),
        migrations.RemoveField(
            model_name='accountimage',
            name='order',
        ),
        migrations.RemoveField(
            model_name='accountimage',
            name='uploaded_at',
        ),
        migrations.AddField(
            model_name='accountimage',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='accountimage',
            name='section',
            field=models.CharField(blank=True, help_text='图片分类，如人物主页、属性面板等', max_length=30),
        ),
        migrations.AlterField(
            model_name='accountimage',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.servicegameaccount'),
        ),
        migrations.AlterField(
            model_name='accountimage',
            name='caption',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='accountimage',
            name='image',
            field=models.ImageField(upload_to='account_images/%Y/%m/'),
        ),
        migrations.AlterField(
            model_name='accountimage',
            name='is_main',
            field=models.BooleanField(default=False),
        ),
    ]
