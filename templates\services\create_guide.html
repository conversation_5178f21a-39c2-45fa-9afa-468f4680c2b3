{% extends 'base.html' %}
{% load static %}

{% block title %}发布攻略 - 攻略专区{% endblock %}

{% block extra_css %}
<style>
.image-preview {
    max-width: 100%;
    max-height: 200px;
    margin-top: 10px;
}
.image-preview-container {
    display: none;
    margin-bottom: 15px;
}
.image-select-button {
    margin-bottom: 15px;
}
.uploaded-images {
    margin-top: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f9fa;
}
.image-item {
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: white;
}
.insert-image-btn {
    margin-top: 5px;
}
.hidden-file-input {
    display: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:guide_list' %}">攻略专区</a></li>
            <li class="breadcrumb-item active" aria-current="page">发布攻略</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title h4 mb-0">发布新攻略</h2>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="guideForm">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">攻略标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required maxlength="200" placeholder="输入攻略标题">
                            <div class="form-text">清晰简洁的标题能够吸引更多读者关注</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="form-label">攻略分类 <span class="text-danger">*</span></label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">请选择攻略分类</option>
                                {% for cat_code, cat_name in categories %}
                                <option value="{{ cat_code }}">{{ cat_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3 mt-3">
                            <label for="content" class="form-label">攻略内容 <span class="text-danger">*</span></label>
                            <div class="btn-toolbar mb-2" role="toolbar">
                                <div class="btn-group me-2" role="group">
                                    <button type="button" class="btn btn-outline-secondary" id="direct-insert-image-btn">
                                        <i class="bi bi-image"></i> 插入图片
                                    </button>
                                </div>
                            </div>
                            <input type="file" id="direct-image-upload" class="hidden-file-input" accept="image/*">
                            <textarea class="form-control" id="content" name="content" rows="15" required placeholder="详细描述您的攻略内容..."></textarea>
                            <div class="form-text">
                                点击"插入图片"按钮可以直接选择图片并插入到光标位置
                            </div>
                        </div>
                        
                        <!-- 已上传的图片列表 -->
                        <div class="uploaded-images" id="uploaded-images" style="display: none;">
                            <h5>已插入的图片</h5>
                            <div class="uploaded-images-list" id="uploaded-images-list">
                                <!-- 这里会动态添加已上传的图片 -->
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured">
                                <label class="form-check-label" for="is_featured">
                                    设为精选攻略
                                </label>
                                <div class="form-text">精选攻略将在攻略专区首页轮播展示</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'services:guide_list' %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">发布攻略</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">攻略发布小贴士</div>
                <div class="card-body">
                    <h5>编写高质量攻略的建议</h5>
                    <ul>
                        <li>内容应该清晰明确，使用标题分段</li>
                        <li>添加适当的图片说明步骤和要点</li>
                        <li>点击"插入图片"按钮可以直接选择并插入图片</li>
                        <li>视频链接格式：直接粘贴视频网站分享链接即可</li>
                        <li>保持内容客观准确，如有特殊操作请详细说明</li>
                        <li>更新版本：如果是针对游戏特定版本的攻略，请注明版本号</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
// 确保脚本仅执行一次
(function() {
    // 检查是否已经初始化过
    if (window.guideEditorInitialized) {
        return;
    }
    window.guideEditorInitialized = true;
    
    let imageCount = 0;
    const maxImages = 20;
    const uploadedImages = [];
    
    document.addEventListener('DOMContentLoaded', function() {
        const directImageBtn = document.getElementById('direct-insert-image-btn');
        const directImageUpload = document.getElementById('direct-image-upload');
        const contentTextarea = document.getElementById('content');
        const guideForm = document.getElementById('guideForm');
        const uploadedImagesContainer = document.getElementById('uploaded-images');
        const uploadedImagesList = document.getElementById('uploaded-images-list');
        
        if (!directImageBtn || !directImageUpload) {
            console.error('Image upload buttons not found');
            return;
        }
        
        // 清除可能存在的事件
        const newDirectImageBtn = directImageBtn.cloneNode(true);
        directImageBtn.parentNode.replaceChild(newDirectImageBtn, directImageBtn);
        
        const newDirectImageUpload = directImageUpload.cloneNode(true);
        directImageUpload.parentNode.replaceChild(newDirectImageUpload, directImageUpload);
        
        // 点击插入图片按钮时触发文件选择
        newDirectImageBtn.addEventListener('click', function(e) {
            e.preventDefault(); // 阻止默认行为
            console.log('Image button clicked');
            newDirectImageUpload.click();
        }, { once: false });
        
        // 选择文件后直接处理
        newDirectImageUpload.addEventListener('change', function() {
            console.log('File selected');
            if (this.files && this.files[0]) {
                if (imageCount >= maxImages) {
                    alert('最多只能上传' + maxImages + '张图片');
                    return;
                }
                
                // 准备图片数据
                imageCount++;
                
                // 创建新图片信息
                const newImage = {
                    id: imageCount,
                    file: this.files[0],
                    caption: '',
                };
                
                // 添加到已上传图片列表
                uploadedImages.push(newImage);
                
                // 显示已上传图片
                uploadedImagesContainer.style.display = 'block';
                
                // 添加图片到列表
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                
                // 创建图片预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 添加图片预览到列表
                    imageItem.innerHTML = `
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <img src="${e.target.result}" class="img-thumbnail" style="max-height: 80px;">
                            </div>
                            <div class="col-md-8">
                                <div><strong>图片 ${newImage.id}</strong></div>
                                <div>
                                    <input type="text" class="form-control form-control-sm caption-input" 
                                        placeholder="输入图片说明（可选）" data-image-id="${newImage.id}">
                                </div>
                            </div>
                        </div>
                    `;
                    uploadedImagesList.appendChild(imageItem);
                    
                    // 添加实际的文件上传输入
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.name = 'images';
                    fileInput.style.display = 'none';
                    fileInput.dataset.imageId = newImage.id;
                    
                    // 创建一个新的DataTransfer对象并分配文件
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(newImage.file);
                    fileInput.files = dataTransfer.files;
                    
                    // 添加caption输入
                    const captionInput = document.createElement('input');
                    captionInput.type = 'hidden';
                    captionInput.name = 'captions';
                    captionInput.value = '';
                    captionInput.id = 'caption-' + newImage.id;
                    
                    // 添加顺序输入
                    const orderInput = document.createElement('input');
                    orderInput.type = 'hidden';
                    orderInput.name = 'orders';
                    orderInput.value = newImage.id;
                    
                    guideForm.appendChild(fileInput);
                    guideForm.appendChild(captionInput);
                    guideForm.appendChild(orderInput);
                    
                    // 监听说明文字变化
                    const captionField = imageItem.querySelector('.caption-input');
                    captionField.addEventListener('input', function() {
                        document.getElementById('caption-' + this.dataset.imageId).value = this.value;
                    });
                    
                    // 自动在光标位置插入图片标记
                    insertImageTag(newImage.id);
                };
                
                reader.readAsDataURL(this.files[0]);
                
                // 重置文件输入，以便可以选择相同的文件
                this.value = '';
            }
        });
        
        // 在光标位置插入图片标记
        function insertImageTag(imageId) {
            const textarea = contentTextarea;
            const startPos = textarea.selectionStart;
            const endPos = textarea.selectionEnd;
            const text = textarea.value;
            const imageTag = `[IMG:${imageId}]`;
            
            // 插入图片标记
            textarea.value = text.substring(0, startPos) + imageTag + text.substring(endPos);
            
            // 重新设置光标位置
            textarea.focus();
            textarea.selectionStart = startPos + imageTag.length;
            textarea.selectionEnd = startPos + imageTag.length;
        }
    });
})();
</script>
{% endblock extra_js %}
{% endblock content %} 