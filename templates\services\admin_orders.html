{% extends 'base.html' %}
{% load static %}

{% block title %}账号交易订单管理{% endblock %}

{% block extra_css %}
<style>
    .status-card {
        border-radius: 10px;
        margin-bottom: 20px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .status-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }
    
    .stats-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 30px;
    }
    
    .stat-col {
        flex: 1;
        padding: 0 10px;
        min-width: 200px;
    }
    
    .pending-card {
        background: linear-gradient(135deg, #fff8e1, #ffe082);
        border-left: 5px solid #ffc107;
    }
    
    .processing-card {
        background: linear-gradient(135deg, #e1f5fe, #81d4fa);
        border-left: 5px solid #17a2b8;
    }
    
    .completed-card {
        background: linear-gradient(135deg, #e8f5e9, #a5d6a7);
        border-left: 5px solid #28a745;
    }
    
    .status-card .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    
    .status-card .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .status-card .stat-title {
        font-size: 1.2rem;
        margin-bottom: 15px;
        font-weight: 500;
    }
    
    .status-card .btn {
        border-radius: 30px;
        padding: 8px 20px;
        font-weight: 500;
        width: 80%;
        transition: all 0.2s;
    }
    
    .status-card .btn:hover {
        transform: scale(1.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">账号交易订单管理</h1>
    
    <!-- 状态统计卡片 -->
    <div class="stats-row">
        <div class="stat-col">
            <div class="status-card pending-card text-center">
                <div class="stat-icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
                <div class="stat-number">{{ pending_count }}</div>
                <div class="stat-title">待处理订单</div>
                <a href="?status=pending" class="btn btn-warning">查看待处理</a>
            </div>
        </div>
        <div class="stat-col">
            <div class="status-card processing-card text-center">
                <div class="stat-icon">
                    <i class="bi bi-arrow-repeat"></i>
                </div>
                <div class="stat-number">{{ processing_count }}</div>
                <div class="stat-title">处理中订单</div>
                <a href="?status=processing" class="btn btn-info text-white">查看处理中</a>
            </div>
        </div>
        <div class="stat-col">
            <div class="status-card completed-card text-center">
                <div class="stat-icon">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stat-number">{{ completed_count }}</div>
                <div class="stat-title">已完成订单</div>
                <a href="?status=completed" class="btn btn-success">查看已完成</a>
            </div>
        </div>
    </div>
    
    <!-- 订单筛选 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">订单筛选</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">状态筛选:</label>
                    <select name="status" id="status" class="form-select">
                        <option value="all" {% if not status_filter %}selected{% endif %}>所有状态</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>待处理</option>
                        <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>处理中</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>已完成</option>
                        <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>已拒绝</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>已取消</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="search" class="form-label">搜索:</label>
                    <input type="text" name="search" id="search" value="{{ search_query }}" placeholder="搜索买家/卖家/账号..." class="form-control">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">应用筛选</button>
                    <a href="{% url 'services:admin_account_orders' %}" class="btn btn-outline-secondary">重置</a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 订单列表 -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">订单列表</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>订单ID</th>
                            <th>账号信息</th>
                            <th>价格</th>
                            <th>买家信息</th>
                            <th>卖家信息</th>
                            <th>联系方式</th>
                            <th>状态</th>
                            <th>申请时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in page_obj %}
                        <tr>
                            <td>#{{ order.id }}</td>
                            <td>
                                <a href="{% url 'services:account_detail' order.account.id %}" target="_blank">{{ order.account.title }}</a>
                            </td>
                            <td class="text-danger">¥{{ order.price }}</td>
                            <td>
                                {{ order.buyer.get_display_name }}<br>
                                <small class="text-muted">{{ order.buyer.email }}</small>
                            </td>
                            <td>
                                {{ order.account.seller.get_display_name }}<br>
                                <small class="text-muted">{{ order.account.seller.email }}</small>
                            </td>
                            <td>
                                {{ order.get_contact_type_display }}: {{ order.contact_value }}
                            </td>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning text-dark">待处理</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">处理中</span>
                                {% elif order.status == 'completed' %}
                                <span class="badge bg-success">已完成</span>
                                {% elif order.status == 'rejected' %}
                                <span class="badge bg-danger">已拒绝</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-secondary">已取消</span>
                                {% endif %}
                            </td>
                            <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'services:admin_view_order' order.id %}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                    {% if order.status == 'pending' %}
                                    <a href="{% url 'services:admin_process_order' order.id %}" class="btn btn-sm btn-outline-success">处理申请</a>
                                    {% elif order.status == 'processing' %}
                                    <a href="{% url 'services:admin_process_order' order.id %}" class="btn btn-sm btn-outline-success">完成交易</a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
            <nav class="mt-4" aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">首页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">上一页</a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item disabled">
                        <span class="page-link">第 {{ page_obj.number }} 页 / 共 {{ page_obj.paginator.num_pages }} 页</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">下一页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">末页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>暂无订单数据
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 