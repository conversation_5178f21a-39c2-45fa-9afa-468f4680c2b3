{% extends 'base.html' %}
{% load static %}
{% load json_encode from json_tags %}

{% block title %}审核账号 - {{ account.title }}{% endblock %}

{% block extra_css %}
<style>
    .image-container {
        position: relative;
        margin-bottom: 15px;
    }
    .account-image {
        max-width: 100%;
        border-radius: 5px;
    }
    .caption {
        margin-top: 5px;
        font-size: 0.9rem;
        color: #6c757d;
    }
    .tag {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
    .role-card, .item-card {
        border: 1px solid rgba(0,0,0,.125);
        border-radius: 0.25rem;
        padding: 10px;
        margin-bottom: 10px;
    }
    .img-thumbnail-container {
        width: 100px;
        height: 100px;
        overflow: hidden;
        position: relative;
        cursor: pointer;
        border: 2px solid transparent;
    }
    .img-thumbnail-container.active {
        border-color: #0d6efd;
    }
    .img-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:admin_account_review' %}">账号审核管理</a></li>
            <li class="breadcrumb-item active" aria-current="page">审核账号</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">管理员功能</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="{% url 'services:admin_account_review' %}" class="list-group-item list-group-item-action active">
                            <i class="bi bi-shield-check me-2"></i>账号审核管理
                        </a>
                        <a href="{% url 'admin:services_servicegameaccount_changelist' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-list-ul me-2"></i>所有账号管理
                        </a>
                        <a href="{% url 'admin:services_accountorder_changelist' %}" class="list-group-item list-group-item-action">
                            <i class="bi bi-cart-check me-2"></i>订单管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- 卖家信息 -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">卖家信息</h5>
                </div>
                <div class="card-body">
                    <p><strong>用户名：</strong>{{ account.seller.username }}</p>
                    <p><strong>昵称：</strong>{{ account.seller.get_display_name }}</p>
                    <p><strong>注册时间：</strong>{{ account.seller.date_joined|date:"Y-m-d" }}</p>
                    <p><strong>出售账号数：</strong>{{ seller_account_count }}</p>
                    <p><strong>联系方式：</strong>{{ account.get_contact_type_display }}: {{ account.contact_value }}</p>
                    <a href="{% url 'admin:accounts_user_change' account.seller.id %}" class="btn btn-outline-primary btn-sm w-100" target="_blank">
                        <i class="bi bi-person-circle me-1"></i>查看用户信息
                    </a>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-lg-9">
            <!-- 账号基本信息 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">账号信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4>{{ account.title }}</h4>
                            <div class="mb-3">
                                <span class="badge bg-info me-2">{{ account.get_game_type_display }}</span>
                                <span class="badge bg-secondary me-2">{{ account.get_account_level_display }}</span>
                                {% if account.device_type %}
                                <span class="badge bg-dark">{{ account.device_type }}</span>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <h5 class="text-danger">¥{{ account.price }}</h5>
                            </div>
                            <div class="mb-3">
                                <p><strong>提交时间：</strong> {{ account.created_at|date:"Y-m-d H:i" }}</p>
                                {% if account.tags.exists %}
                                <p><strong>标签：</strong></p>
                                <div class="mb-2">
                                    {% for tag in account.tags.all %}
                                    <span class="tag">{{ tag.name }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            {% if account.main_image %}
                            <img src="{{ account.main_image.url }}" alt="{{ account.title }}" class="img-fluid rounded">
                            {% else %}
                            <div class="bg-light text-center d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr>

                    <h5>账号描述</h5>
                    <div class="mb-4">
                        <p>{{ account.description|linebreaks }}</p>
                    </div>

                    {% if account.roles.exists %}
                    <h5>角色信息</h5>
                    <div class="row mb-4">
                        {% for role in account.roles.all %}
                        <div class="col-md-4 mb-2">
                            <div class="role-card">
                                <div><strong>{{ role.name }}</strong></div>
                                {% if role.level %}
                                <div class="small text-muted">等级: {{ role.level }}</div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    {% if account.items.exists %}
                    <h5>物品信息</h5>
                    <div class="row mb-4">
                        {% for item in account.items.all %}
                        <div class="col-md-4 mb-2">
                            <div class="item-card">
                                <div><strong>{{ item.name }}</strong></div>
                                {% if item.quantity %}
                                <div class="small text-muted">数量: {{ item.quantity }}</div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <h5>账号截图</h5>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex overflow-auto py-2" id="thumbnails-container">
                                {% for image in account.images.all %}
                                <div class="img-thumbnail-container mx-1 {% if forloop.first %}active{% endif %}" data-index="{{ forloop.counter0 }}">
                                    <img src="{{ image.image.url }}" class="img-thumbnail" alt="缩略图 {{ forloop.counter }}">
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div id="main-image-container" class="text-center">
                                {% if account.images.exists %}
                                {% with first_image=account.images.first %}
                                <div class="image-container" id="image-0">
                                    <img src="{{ first_image.image.url }}" class="account-image img-fluid" alt="{{ first_image.caption }}">
                                    {% if first_image.caption %}
                                    <div class="caption">{{ first_image.caption }}</div>
                                    {% endif %}
                                </div>
                                {% endwith %}
                                {% else %}
                                <div class="bg-light text-center d-flex align-items-center justify-content-center rounded" style="height: 300px;">
                                    <div class="text-muted">
                                        <i class="bi bi-image" style="font-size: 3rem;"></i>
                                        <p class="mt-2">无图片</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审核操作 -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">审核操作</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="" id="reviewForm">
                        {% csrf_token %}
                        <input type="hidden" name="action" id="actionField" value="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <button type="button" id="approveButton" class="btn btn-success w-100">
                                    <i class="bi bi-check-circle me-1"></i>通过审核
                                </button>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="button" class="btn btn-danger w-100" data-bs-toggle="collapse" data-bs-target="#rejectForm">
                                    <i class="bi bi-x-circle me-1"></i>拒绝审核
                                </button>
                            </div>
                        </div>

                        <div class="collapse mt-3" id="rejectForm">
                            <div class="card card-body bg-light">
                                <h6 class="card-title">拒绝原因</h6>
                                <div class="mb-3">
                                    <textarea name="reason" class="form-control" rows="3" placeholder="请输入拒绝原因，以便卖家修改后重新提交" required></textarea>
                                </div>
                                <div class="mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="reason_type" id="reason1" value="信息不完整">
                                        <label class="form-check-label" for="reason1">信息不完整</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="reason_type" id="reason2" value="图片不清晰或不足">
                                        <label class="form-check-label" for="reason2">图片不清晰或不足</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="reason_type" id="reason3" value="价格过高">
                                        <label class="form-check-label" for="reason3">价格过高</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="reason_type" id="reason4" value="含有违规内容">
                                        <label class="form-check-label" for="reason4">含有违规内容</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="reason_type" id="reason5" value="other">
                                        <label class="form-check-label" for="reason5">其他原因</label>
                                    </div>
                                </div>
                                <button type="button" id="rejectButton" class="btn btn-danger">
                                    确认拒绝
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 处理审核表单提交
    const reviewForm = document.getElementById('reviewForm');
    const actionField = document.getElementById('actionField');
    const approveButton = document.getElementById('approveButton');
    const rejectButton = document.getElementById('rejectButton');
    
    // 通过审核
    approveButton.addEventListener('click', function() {
        actionField.value = 'approve';
        console.log('提交表单: 通过审核');
        reviewForm.submit();
    });
    
    // 拒绝审核
    rejectButton.addEventListener('click', function() {
        const reasonValue = document.querySelector('textarea[name="reason"]').value.trim();
        if(!reasonValue) {
            alert('请输入拒绝原因');
            return;
        }
        actionField.value = 'reject';
        console.log('提交表单: 拒绝审核');
        reviewForm.submit();
    });

    // 处理图片缩略图点击
    const thumbnailContainers = document.querySelectorAll('.img-thumbnail-container');
    const mainImageContainer = document.getElementById('main-image-container');
    
    // 图片数据从服务器端直接生成JSON
    const imagesJson = '{{ account_images_json|escapejs }}';
    const images = imagesJson ? JSON.parse(imagesJson) : [];

    thumbnailContainers.forEach(container => {
        container.addEventListener('click', function() {
            const index = this.getAttribute('data-index');
            
            // 更新缩略图选中状态
            thumbnailContainers.forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            
            // 更新主图
            if (images[index]) {
                const image = images[index];
                let html = `
                    <div class="image-container" id="image-${index}">
                        <img src="${image.url}" class="account-image img-fluid" alt="${image.caption || ''}">
                        ${image.caption ? `<div class="caption">${image.caption}</div>` : ''}
                    </div>
                `;
                mainImageContainer.innerHTML = html;
            }
        });
    });

    // 处理拒绝原因单选框
    const reasonRadios = document.querySelectorAll('input[name="reason_type"]');
    const reasonTextarea = document.querySelector('textarea[name="reason"]');

    reasonRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const value = this.value;
            if (value !== 'other') {
                reasonTextarea.value = value;
            } else {
                reasonTextarea.value = '';
                reasonTextarea.focus();
            }
        });
    });
});
</script>
{% endblock %}
{% endblock %} 