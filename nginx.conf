# Nginx配置文件 - 放置在 /etc/nginx/sites-available/game_trade

server {
    listen 80;
    server_name your-domain.com your-server-ip;  # 替换为你的域名或服务器IP
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 客户端最大上传大小
    client_max_body_size 100M;

    # 静态文件
    location /static/ {
        alias /root/web/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 媒体文件
    location /media/ {
        alias /root/web/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # 主应用
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日志
    access_log /var/log/nginx/game_trade_access.log;
    error_log /var/log/nginx/game_trade_error.log;
}

# HTTPS配置（可选，需要SSL证书）
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # 其他配置与HTTP相同...
# }
