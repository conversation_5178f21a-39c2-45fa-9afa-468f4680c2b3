from django.contrib import admin
from .models import Transaction, Payment, Review, Dispute

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction_id', 'buyer', 'seller', 'game_account', 'amount', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('transaction_id', 'buyer__username', 'seller__username', 'game_account__title')
    readonly_fields = ('transaction_id', 'created_at', 'updated_at')
    
    fieldsets = (
        (None, {'fields': ('transaction_id', 'buyer', 'seller', 'game_account')}),
        ('财务信息', {'fields': ('amount', 'platform_fee')}),
        ('状态', {'fields': ('status', 'buyer_message')}),
        ('时间戳', {'fields': ('created_at', 'updated_at', 'completed_at')}),
    )

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('payment_id', 'transaction', 'amount', 'payment_method', 'payment_status', 'payment_time')
    list_filter = ('payment_status', 'payment_method')
    search_fields = ('payment_id', 'transaction__transaction_id', 'external_reference')
    readonly_fields = ('payment_id', 'created_at')

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('transaction', 'reviewer', 'reviewee', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('reviewer__username', 'reviewee__username', 'comment')
    readonly_fields = ('created_at',)

@admin.register(Dispute)
class DisputeAdmin(admin.ModelAdmin):
    list_display = ('transaction', 'initiator', 'status', 'created_at', 'resolved_at')
    list_filter = ('status', 'created_at')
    search_fields = ('transaction__transaction_id', 'initiator__username', 'reason')
    readonly_fields = ('created_at',)
    
    fieldsets = (
        (None, {'fields': ('transaction', 'initiator', 'reason', 'evidence')}),
        ('解决方案', {'fields': ('status', 'admin_notes', 'resolution', 'resolved_at')}),
        ('时间戳', {'fields': ('created_at',)}),
    ) 