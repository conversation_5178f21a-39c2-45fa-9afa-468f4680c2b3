{% extends 'base.html' %}
{% load static %}

{% block title %}账号购买申请管理{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item active" aria-current="page">账号购买申请管理</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- 左侧侧边栏 -->
        <div class="col-lg-3 mb-4">
            <div class="card sticky-top" style="top: 20px; z-index: 1000;">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">账号交易</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'services:account_list' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-grid-3x3-gap me-2"></i>浏览账号
                    </a>
                    <a href="{% url 'services:sell_account' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-upload me-2"></i>出售账号
                    </a>
                    <a href="{% url 'services:my_selling_accounts' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-bag me-2"></i>我的出售
                    </a>
                    <a href="{% url 'services:my_account_orders' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-cart me-2"></i>我的购买
                    </a>
                    <a href="{% url 'services:account_orders_seller' %}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-clipboard-check me-2"></i>购买申请管理
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 右侧主内容 -->
        <div class="col-lg-9">
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-warning h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title text-warning">待处理申请</h5>
                                    <p class="card-text fs-1 fw-bold">{{ pending_count }}</p>
                                </div>
                                <i class="bi bi-hourglass-split text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <a href="?status=pending" class="stretched-link"></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-info h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title text-info">处理中申请</h5>
                                    <p class="card-text fs-1 fw-bold">{{ processing_count }}</p>
                                </div>
                                <i class="bi bi-arrow-clockwise text-info" style="font-size: 3rem;"></i>
                            </div>
                            <a href="?status=processing" class="stretched-link"></a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 筛选器 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">筛选选项</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">申请状态</label>
                            <select id="status" name="status" class="form-select">
                                <option value="all" {% if status_filter == 'all' or not status_filter %}selected{% endif %}>全部状态</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>待处理</option>
                                <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>处理中</option>
                                <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>已完成</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>已拒绝</option>
                                <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>已取消</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="account" class="form-label">按账号筛选</label>
                            <select id="account" name="account" class="form-select">
                                <option value="">全部账号</option>
                                {% for account in selling_accounts %}
                                <option value="{{ account.id }}" {% if account_filter == account.id|stringformat:"i" %}selected{% endif %}>
                                    {{ account.title }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="sort" class="form-label">排序方式</label>
                            <select id="sort" name="sort" class="form-select">
                                <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>最新申请</option>
                                <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>最早申请</option>
                                <option value="-updated_at" {% if sort_by == '-updated_at' %}selected{% endif %}>最近更新</option>
                                <option value="account__title" {% if sort_by == 'account__title' %}selected{% endif %}>账号名称</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">应用筛选</button>
                            <a href="{% url 'services:account_orders_seller' %}" class="btn btn-outline-secondary">重置</a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 申请列表 -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">购买申请列表</h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th scope="col">订单ID</th>
                                    <th scope="col">账号信息</th>
                                    <th scope="col">买家信息</th>
                                    <th scope="col">状态</th>
                                    <th scope="col">申请时间</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in page_obj %}
                                <tr>
                                    <td>#{{ order.id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if order.account.main_image %}
                                            <img src="{{ order.account.main_image.url }}" alt="{{ order.account.title }}" class="me-2 rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                            {% else %}
                                            <div class="me-2 bg-light text-center d-flex align-items-center justify-content-center rounded" style="width: 40px; height: 40px;">
                                                <i class="bi bi-image text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <div class="fw-bold text-truncate" style="max-width: 150px;">{{ order.account.title }}</div>
                                                <div class="small text-danger">¥{{ order.account.price }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ order.buyer.get_display_name }}</div>
                                        <div class="small text-muted">买家ID: {{ order.buyer.id }}</div>
                                    </td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                        <span class="badge bg-warning text-dark">待处理</span>
                                        {% elif order.status == 'processing' %}
                                        <span class="badge bg-info">处理中</span>
                                        {% elif order.status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif order.status == 'rejected' %}
                                        <span class="badge bg-danger">已拒绝</span>
                                        {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-secondary">已取消</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'services:process_order' order.id %}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                            {% if order.status == 'pending' %}
                                            <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#acceptModal{{ order.id }}">接受</button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ order.id }}">拒绝</button>
                                            {% elif order.status == 'processing' %}
                                            <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#completeModal{{ order.id }}">完成交易</button>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- 接受申请模态框 -->
                                        <div class="modal fade" id="acceptModal{{ order.id }}" tabindex="-1" aria-labelledby="acceptModalLabel{{ order.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="acceptModalLabel{{ order.id }}">确认接受申请</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>您确定要接受此购买申请吗？</p>
                                                        <p>接受后，账号状态将变为"已预订"，其他买家将无法再提交购买申请。</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <form method="post" action="{% url 'services:process_order' order.id %}">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="action" value="accept">
                                                            <button type="submit" class="btn btn-success">确认接受</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 拒绝申请模态框 -->
                                        <div class="modal fade" id="rejectModal{{ order.id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ order.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="rejectModalLabel{{ order.id }}">拒绝申请</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form method="post" action="{% url 'services:process_order' order.id %}">
                                                        {% csrf_token %}
                                                        <input type="hidden" name="action" value="reject">
                                                        <div class="modal-body">
                                                            <p>您确定要拒绝此购买申请吗？</p>
                                                            <div class="mb-3">
                                                                <label for="reason{{ order.id }}" class="form-label">拒绝原因（选填）</label>
                                                                <textarea class="form-control" id="reason{{ order.id }}" name="reason" rows="3" placeholder="请输入拒绝原因，以便买家了解情况"></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                            <button type="submit" class="btn btn-danger">确认拒绝</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 完成交易模态框 -->
                                        <div class="modal fade" id="completeModal{{ order.id }}" tabindex="-1" aria-labelledby="completeModalLabel{{ order.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="completeModalLabel{{ order.id }}">确认完成交易</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="alert alert-info">
                                                            <i class="bi bi-info-circle-fill me-2"></i>
                                                            在确认完成交易前，请确保以下事项已完成：
                                                            <ul class="mb-0 mt-2">
                                                                <li>买家已支付完成</li>
                                                                <li>账号信息已提供给买家</li>
                                                                <li>买家已确认可以正常使用账号</li>
                                                            </ul>
                                                        </div>
                                                        <p>完成交易后，账号将变为"已售出"状态，其他所有待处理的购买申请将被自动取消。</p>
                                                        <p class="text-danger fw-bold">此操作不可撤销。</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <form method="post" action="{% url 'services:process_order' order.id %}">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="action" value="complete">
                                                            <button type="submit" class="btn btn-success">确认完成</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if account_filter %}&account={{ account_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if account_filter %}&account={{ account_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in page_obj.paginator.page_range %}
                                {% if page_obj.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if account_filter %}&account={{ account_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if account_filter %}&account={{ account_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if account_filter %}&account={{ account_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        暂无购买申请。
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 交易流程说明 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">交易流程</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <div class="text-primary mb-2">
                                <i class="bi bi-1-circle" style="font-size: 2rem;"></i>
                            </div>
                            <h6>收到申请</h6>
                            <p class="small text-muted">买家提交购买申请，等待卖家处理</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="text-primary mb-2">
                                <i class="bi bi-2-circle" style="font-size: 2rem;"></i>
                            </div>
                            <h6>接受申请</h6>
                            <p class="small text-muted">卖家接受申请，并与买家联系</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="text-primary mb-2">
                                <i class="bi bi-3-circle" style="font-size: 2rem;"></i>
                            </div>
                            <h6>线下交易</h6>
                            <p class="small text-muted">买卖双方协商价格和交付方式</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="text-primary mb-2">
                                <i class="bi bi-4-circle" style="font-size: 2rem;"></i>
                            </div>
                            <h6>确认完成</h6>
                            <p class="small text-muted">交易成功后，卖家确认完成交易</p>
                        </div>
                    </div>
                    <div class="alert alert-warning mt-2">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>注意：</strong>本平台仅为买卖双方提供信息对接服务，不参与具体交易过程。请双方注意保护个人信息安全，谨防诈骗。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 