from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count, Avg, F
from .models import Game, GameServer, GameAccount, AccountScreenshot
from .forms import GameAccountForm, ScreenshotForm
from django.http import JsonResponse
from services.models import ServiceGameAccount
from trades.models import Transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.paginator import Paginator

def game_list(request):
    """游戏列表视图 - 只显示明日之后"""
    # 仅获取明日之后游戏
    game = Game.objects.filter(name="明日之后", is_active=True).first()
    if not game:
        # 如果游戏不存在，创建它
        game = Game.objects.create(
            name="明日之后",
            slug="lifeafter",
            description="《明日之后》是一款由网易开发的开放世界生存手游，背景设定在病毒爆发后的末世。玩家需要收集资源、建造庇护所、制作武器和装备，同时与其他玩家合作或对抗，在这个危险的世界中生存下去。",
            is_active=True
        )
    
    return render(request, 'games/game_list.html', {'games': [game]})

def game_detail(request, game_slug):
    """游戏详情视图"""
    # 强制只显示明日之后
    game = get_object_or_404(Game, slug=game_slug, name="明日之后", is_active=True)
    servers = game.servers.filter(is_active=True)
    featured_accounts = game.accounts.filter(status='selling', is_featured=True)[:5]
    
    context = {
        'game': game,
        'servers': servers,
        'featured_accounts': featured_accounts,
    }
    return render(request, 'games/game_detail.html', context)

def account_list(request):
    """账号列表视图"""
    accounts = GameAccount.objects.filter(status='available')
    
    # 获取筛选参数
    game = request.GET.get('game')
    server = request.GET.get('server')
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    search_query = request.GET.get('q')
    sort_by = request.GET.get('sort')
    recommended = request.GET.get('recommended')
    
    # 应用筛选条件
    if game:
        accounts = accounts.filter(game__slug=game)
    if server:
        accounts = accounts.filter(server=server)
    if min_price:
        accounts = accounts.filter(price__gte=min_price)
    if max_price:
        accounts = accounts.filter(price__lte=max_price)
    if search_query:
        accounts = accounts.filter(
            Q(title__icontains=search_query) | 
            Q(description__icontains=search_query) |
            Q(server__icontains=search_query)
        )
    if recommended:
        accounts = accounts.filter(is_featured=True)
    
    # 应用排序
    if sort_by == 'price_low':
        accounts = accounts.order_by('price')
    elif sort_by == 'price_high':
        accounts = accounts.order_by('-price')
    elif sort_by == 'level_high':
        accounts = accounts.order_by('-level')
    elif sort_by == 'newest':
        accounts = accounts.order_by('-created_at')
    else:
        # 默认排序
        accounts = accounts.order_by('-is_featured', '-created_at')
    
    context = {
        'accounts': accounts,
        'server': server,  # 将区服参数传递到模板
    }
    return render(request, 'games/account_list.html', context)

def account_detail(request, account_id):
    """游戏账号详情视图"""
    account = get_object_or_404(GameAccount, pk=account_id)
    screenshots = account.screenshots.all()
    
    similar_accounts = GameAccount.objects.filter(
        game=account.game,
        status='selling'
    ).exclude(pk=account.pk)[:3]
    
    context = {
        'account': account,
        'screenshots': screenshots,
        'similar_accounts': similar_accounts,
    }
    return render(request, 'games/account_detail.html', context)

@login_required
def create_account(request):
    """出售账号页面视图"""
    if request.method == 'POST':
        form = GameAccountForm(request.POST, request.FILES)
        if form.is_valid():
            account = form.save(commit=False)
            account.seller = request.user
            account.game = Game.objects.get(slug='lifeafter')  # 获取明日之后游戏
            account.status = 'pending'  # 设置为待审核状态
            account.save()
            
            # 保存账号截图
            if 'image' in request.FILES:
                account.image = request.FILES['image']
                account.save()
            
            # 添加消息提示
            messages.success(request, '您的账号已成功提交，等待管理员审核')
            
            # 重定向到提交成功页面
            return redirect('games:submit_success', account_id=account.id)
    else:
        form = GameAccountForm()
    
    return render(request, 'games/create_account.html', {'form': form})

def submit_success(request, account_id):
    """提交成功页面视图"""
    account = get_object_or_404(GameAccount, id=account_id, seller=request.user)
    return render(request, 'games/submit_success.html', {'account': account})

@login_required
def edit_account(request, account_id):
    """编辑游戏账号视图"""
    account = get_object_or_404(GameAccount, pk=account_id, seller=request.user)
    
    if account.status not in ['pending', 'selling']:
        messages.error(request, '只有待审核或出售中的账号才能编辑。')
        return redirect('games:account_detail', account_id=account.id)
    
    if request.method == 'POST':
        form = GameAccountForm(request.POST, instance=account)
        if form.is_valid():
            form.save()
            messages.success(request, '游戏账号信息已更新。')
            return redirect('games:account_detail', account_id=account.id)
    else:
        form = GameAccountForm(instance=account)
    
    return render(request, 'games/edit_account.html', {'form': form, 'account': account})

@login_required
def delete_account(request, account_id):
    """删除游戏账号视图"""
    account = get_object_or_404(GameAccount, pk=account_id, seller=request.user)
    
    if account.status not in ['pending', 'selling']:
        messages.error(request, '只有待审核或出售中的账号才能删除。')
        return redirect('games:account_detail', account_id=account.id)
    
    if request.method == 'POST':
        account.delete()
        messages.success(request, '游戏账号已删除。')
        return redirect('accounts:dashboard')
    
    return render(request, 'games/delete_account.html', {'account': account})

def search_accounts(request):
    """搜索游戏账号视图"""
    query = request.GET.get('q', '')
    
    if query:
        accounts = GameAccount.objects.filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(game__name__icontains=query) |
            Q(server__name__icontains=query),
            status='selling'
        )
    else:
        accounts = GameAccount.objects.none()
    
    context = {
        'accounts': accounts,
        'query': query,
    }
    return render(request, 'games/search_results.html', context)

def filter_accounts(request):
    """过滤游戏账号视图"""
    accounts = GameAccount.objects.filter(status='selling')
    
    # 获取过滤参数
    game_id = request.GET.get('game')
    server_id = request.GET.get('server')
    min_level = request.GET.get('min_level')
    max_level = request.GET.get('max_level')
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    character_class = request.GET.get('character_class')
    
    # 应用过滤器
    if game_id:
        accounts = accounts.filter(game_id=game_id)
    if server_id:
        accounts = accounts.filter(server_id=server_id)
    if min_level:
        accounts = accounts.filter(character_level__gte=min_level)
    if max_level:
        accounts = accounts.filter(character_level__lte=max_level)
    if min_price:
        accounts = accounts.filter(price__gte=min_price)
    if max_price:
        accounts = accounts.filter(price__lte=max_price)
    if character_class:
        accounts = accounts.filter(character_class=character_class)
    
    context = {
        'accounts': accounts,
        'filters': request.GET,
    }
    return render(request, 'games/filter_results.html', context)

@login_required
def add_screenshot(request, account_id):
    """添加账号截图视图"""
    account = get_object_or_404(GameAccount, pk=account_id, seller=request.user)
    
    if request.method == 'POST':
        form = ScreenshotForm(request.POST, request.FILES)
        if form.is_valid():
            screenshot = form.save(commit=False)
            screenshot.account = account
            screenshot.save()
            
            # 更新账号的has_screenshots标志
            if not account.has_screenshots:
                account.has_screenshots = True
                account.save()
                
            messages.success(request, '截图已添加。')
            return redirect('games:account_detail', account_id=account.id)
    else:
        form = ScreenshotForm()
    
    return render(request, 'games/add_screenshot.html', {'form': form, 'account': account})

@login_required
def delete_screenshot(request, account_id, screenshot_id):
    """删除账号截图视图"""
    account = get_object_or_404(GameAccount, pk=account_id, seller=request.user)
    screenshot = get_object_or_404(AccountScreenshot, pk=screenshot_id, account=account)
    
    if request.method == 'POST':
        screenshot.delete()
        
        # 检查是否还有其他截图
        if account.screenshots.count() == 0:
            account.has_screenshots = False
            account.save()
            
        messages.success(request, '截图已删除。')
        return redirect('games:account_detail', account_id=account.id)
    
    return render(request, 'games/delete_screenshot.html', {'screenshot': screenshot, 'account': account})

def get_popular_servers(request):
    """获取热门区服数据"""
    # 统计状态为active(已上架)的账号按区服分组并按数量排序
    server_stats = ServiceGameAccount.objects.filter(
        status='active'
    ).values('server').annotate(
        account_count=Count('id')
    ).order_by('-account_count')[:5]  # 获取前5个热门区服
    
    # 过滤掉空区服名
    server_stats = [stat for stat in server_stats if stat['server']]
    
    return JsonResponse(list(server_stats), safe=False)

@login_required
def test_transaction_page(request):
    """测试交易页面"""
    return render(request, 'trades/test_transaction.html')

@login_required
def create_test_transaction(request):
    """创建测试账号和交易，用于调试"""
    # 创建一个测试游戏账号
    seller = request.user
    account = GameAccount.objects.create(
        title='测试游戏账号',
        description='这是一个测试用的游戏账号',
        price=199.00,
        level=50,
        server='测试服务器',
        device='ios',
        seller=seller,
        status='selling'
    )
    
    # 如果当前用户不是管理员，则从现有用户中选择一个作为买家
    # 否则使用当前用户作为买家和卖家
    User = get_user_model()
    if request.user.is_staff and User.objects.count() > 1:
        buyers = User.objects.exclude(id=request.user.id).order_by('?')
        if buyers.exists():
            buyer = buyers.first()
        else:
            buyer = request.user
    else:
        buyer = request.user
    
    # 创建交易
    platform_fee = account.price * 0.05
    transaction = Transaction.objects.create(
        buyer=buyer,
        seller=seller,
        game_account=account,
        amount=account.price,
        platform_fee=platform_fee,
        status='pending',
        buyer_message='这是一个测试交易'
    )
    
    # 更新账号状态
    account.status = 'reserved'
    account.save()
    
    messages.success(request, f'测试交易已创建！交易ID: {transaction.transaction_id}')
    return redirect('trades:transaction_list') 