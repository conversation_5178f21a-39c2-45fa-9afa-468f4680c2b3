{% extends 'base.html' %}
{% load static %}

{% block title %}申请购买账号 - {{ account.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 导航栏 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_list' %}">账号交易</a></li>
            <li class="breadcrumb-item"><a href="{% url 'services:account_detail' account_id=account.id %}">{{ account.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">申请购买</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 账号信息摘要 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">账号信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            {% if account.main_image %}
                            <img src="{{ account.main_image.image.url }}" class="img-fluid rounded" alt="{{ account.title }}">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                                <span class="text-muted">无图片</span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-8">
                            <h5>{{ account.title }}</h5>
                            <p class="text-danger fw-bold fs-4">¥ {{ account.price }}</p>
                            <div class="mb-2">
                                <span class="badge bg-secondary">{{ account.get_game_type_display }}</span>
                                <span class="badge bg-info">{{ account.get_account_level_display }}</span>
                                {% if account.device_type %}
                                <span class="badge bg-dark">{{ account.device_type }}</span>
                                {% endif %}
                            </div>
                            <small class="text-muted">发布时间: {{ account.created_at|date:"Y-m-d H:i" }}</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 申请表单 -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">申请购买</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="contact_type" class="form-label">联系方式 <span class="text-danger">*</span></label>
                            <select class="form-select" id="contact_type" name="contact_type" required>
                                <option value="">选择联系方式</option>
                                <option value="phone">手机号码</option>
                                <option value="wechat">微信</option>
                                <option value="qq">QQ</option>
                            </select>
                            <div class="form-text">您的联系方式仅用于平台管理员与您联系确认交易细节，不会透露给卖家</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="contact_value" class="form-label">联系方式信息 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="contact_value" name="contact_value" required>
                            <div class="form-text">请输入您的联系方式，此信息仅管理员可见，绝不会向卖家或其他用户透露</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">给卖家的留言</label>
                            <textarea class="form-control" id="message" name="message" rows="3" placeholder="有什么想告诉卖家的？"></textarea>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    我已阅读并同意<a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">《账号交易条款》</a>
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'services:account_detail' account_id=account.id %}" class="btn btn-outline-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">提交申请</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易条款模态框 -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">账号交易条款</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5>1. 交易流程</h5>
                <p>提交申请后，平台将审核您的购买申请并联系您确认交易细节。交易完成后，账号将会转移到您的名下。</p>
                
                <h5>2. 交易安全</h5>
                <p>禁止线下私自交易，平台不对线下交易所产生的问题负责。所有交易必须通过平台进行，以确保双方的权益。</p>
                
                <h5>3. 账号状态</h5>
                <p>平台会尽力核实账号信息的真实性，但不对账号的实际状态作担保。如发现账号信息与实际不符，可联系平台客服退款处理。</p>
                
                <h5>4. 责任限制</h5>
                <p>平台仅作为交易双方的中介，不对卖家提供的账号后续使用中可能产生的问题负责。</p>
                
                <h5>5. 个人信息</h5>
                <p>您提供的联系方式仅用于此次交易，平台不会将您的个人信息用于其他用途或向第三方透露。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我已阅读并同意</button>
            </div>
        </div>
    </div>
</div>
{% endblock %} 