from django.db import migrations

def add_lifeafter_game(apps, schema_editor):
    Game = apps.get_model('games', 'Game')
    if not Game.objects.filter(slug='lifeafter').exists():
        Game.objects.create(
            name='明日之后',
            slug='lifeafter',
            description='《明日之后》是网易开发的开放世界生存手游，背景设定在病毒爆发后的末世。玩家需要收集资源、建造庇护所、制作武器和装备，同时与其他玩家合作或对抗，在这个危险的世界中生存下去。',
            is_active=True
        )

def remove_lifeafter_game(apps, schema_editor):
    Game = apps.get_model('games', 'Game')
    Game.objects.filter(slug='lifeafter').delete()

class Migration(migrations.Migration):

    dependencies = [
        ('games', '0002_gameaccount_has_special_items_and_more'),
    ]

    operations = [
        migrations.RunPython(add_lifeafter_game, remove_lifeafter_game),
    ] 