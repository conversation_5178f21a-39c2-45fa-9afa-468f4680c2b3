{% extends 'base.html' %}
{% load static %}

{% block title %}梦羽明日之后 - 安全可靠的账号交易服务{% endblock %}

{% block extra_css %}
<style>
    /* 整体页面渐变背景 */
    body {
        background: linear-gradient(180deg,
            #667eea 0%,     /* 顶部紫蓝色 */
            #764ba2 25%,    /* 紫色 */
            #ff6b6b 50%,    /* 中间红色 */
            #4ecdc4 75%,    /* 青色 */
            #a8edea 100%    /* 底部浅青色 */
        );
        background-attachment: fixed;
        min-height: 100vh;
    }

    /* 确保内容区域透明，显示背景渐变 */
    .main-content {
        background: transparent;
    }
    .hero-section {
        background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('{% static "img/hero-bg.jpg" %}');
        background-size: cover;
        background-position: center;
        min-height: 80vh;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
    }

    .floating-icon {
        position: absolute;
        color: rgba(255,255,255,0.1);
        font-size: 2rem;
        animation: float 6s ease-in-out infinite;
        z-index: 1;
    }

    .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
    .floating-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
    .floating-icon:nth-child(3) { top: 60%; left: 5%; animation-delay: 2s; }
    .floating-icon:nth-child(4) { bottom: 20%; right: 10%; animation-delay: 3s; }
    .floating-icon:nth-child(5) { top: 40%; left: 80%; animation-delay: 4s; }
    .floating-icon:nth-child(6) { bottom: 40%; left: 20%; animation-delay: 5s; }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(10deg); }
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .btn-game {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-game:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .stats-counter {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }

    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1.5rem;
    }

    .service-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: none;
        height: 100%;
    }

    .service-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    }

    .service-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .section-title {
        position: relative;
        margin-bottom: 3rem;
        font-weight: 700;
        color: #2d3748;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 2px;
    }

    /* 平台特色卡片样式 */
    .platform-features .feature-card {
        transition: all 0.3s ease;
    }

    .platform-features .feature-card:hover {
        transform: translateY(-5px);
    }

    .platform-features .feature-card:hover .card {
        background: rgba(0, 0, 0, 0.3) !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 0 !important;
    }

    .platform-features .card {
        background: transparent !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        transition: all 0.3s ease;
    }

    /* 只针对英雄区域的平台特色卡片移除背景 */
    .hero-section .platform-features .card,
    .hero-section .platform-features .card-body,
    .hero-section .platform-features .feature-card {
        background: transparent !important;
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
    }

    /* 移除英雄区域平台特色卡片的Bootstrap样式 */
    .hero-section .platform-features .shadow,
    .hero-section .platform-features .shadow-sm,
    .hero-section .platform-features .shadow-lg {
        border-radius: 0 !important;
        background: transparent !important;
        background-color: transparent !important;
        box-shadow: none !important;
    }

    /* 确保所有图标正常显示 */
    i,
    .bi,
    [class*="bi-"] {
        background: none !important;
        color: inherit !important;
    }

    /* 游戏介绍部分样式 */
    .game-intro-section .feature-item:hover {
        transform: translateY(-5px);
        background: rgba(255,255,255,0.15) !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .game-intro-section .showcase-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .game-intro-section .showcase-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.3) !important;
    }

    .game-intro-section .feature-icon-modern {
        transition: all 0.3s ease;
    }

    .game-intro-section .feature-item:hover .feature-icon-modern {
        transform: scale(1.1) rotate(5deg);
    }

    /* 平台特色部分样式 */
    .platform-features-section .modern-feature-card:hover {
        transform: translateY(-10px);
        background: rgba(255,255,255,0.2) !important;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    }

    .platform-features-section .modern-feature-card:hover .feature-icon-modern {
        transform: scale(1.15) rotate(-5deg);
    }

    /* 心愿工坊部分样式 */
    .wishlist-section .wishlist-service-card:hover {
        transform: translateY(-8px) scale(1.02);
        background: rgba(255,255,255,0.25) !important;
        box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    }

    .wishlist-section .wishlist-service-card:hover .service-icon {
        transform: scale(1.2) rotate(10deg);
    }

    /* 按钮悬停效果 */
    .btn-outline-light:hover {
        background: rgba(255,255,255,0.2) !important;
        border-color: rgba(255,255,255,0.8) !important;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .btn-light:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.3) !important;
    }

    /* 交易流程部分样式 */
    .trading-process-section .process-step-card:hover {
        transform: translateY(-8px) scale(1.02);
        background: rgba(255,255,255,0.25) !important;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }

    .trading-process-section .process-step-card:hover .step-number {
        transform: scale(1.15) rotate(-5deg);
    }

    /* 用户评价部分样式 */
    .user-reviews-section .review-card:hover {
        transform: translateY(-5px);
        background: rgba(255,255,255,0.95) !important;
        box-shadow: 0 15px 30px rgba(0,0,0,0.15) !important;
    }

    .user-reviews-section .review-card:hover .user-avatar {
        transform: scale(1.1);
    }

    /* 评分星星动画 */
    .rating i {
        transition: all 0.2s ease;
    }

    .review-card:hover .rating i {
        transform: scale(1.2);
    }

    /* 热门区服部分样式 */
    .popular-servers-section .modern-stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.3) !important;
    }

    /* 强制覆盖表格背景 */
    .modern-table {
        background: transparent !important;
        color: #ffffff !important;
    }

    .modern-table thead tr {
        background: rgba(0,0,0,0.3) !important;
    }

    .modern-table thead th {
        background: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }

    .modern-table tbody tr {
        background: rgba(0,0,0,0.4) !important;
        border: none !important;
        transition: all 0.3s ease;
    }

    .modern-table tbody tr:hover {
        background: rgba(0,0,0,0.6) !important;
        transform: translateX(5px);
    }

    .modern-table tbody td {
        border: none !important;
        color: #ffffff !important;
        padding: 1rem 0.75rem;
        vertical-align: middle;
        font-weight: 500;
        background: transparent !important;
    }

    .modern-table tbody tr:not(:last-child) {
        border-bottom: 1px solid rgba(255,255,255,0.2) !important;
    }

    /* 表格中的按钮样式 */
    .modern-table .btn {
        color: #ffffff !important;
        border-color: rgba(255,255,255,0.5) !important;
        background: rgba(255,255,255,0.1) !important;
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .modern-table .btn:hover {
        background: rgba(255,255,255,0.2) !important;
        border-color: rgba(255,255,255,0.8) !important;
        color: #ffffff !important;
    }

    /* 强制覆盖Bootstrap表格样式 */
    .table > :not(caption) > * > * {
        background-color: transparent !important;
    }

    .platform-features .card-body {
        padding: 1.5rem 1rem;
    }

    .platform-features .card-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .platform-features .card-text {
        opacity: 0.9;
        font-size: 0.85rem;
    }

    /* 全宽布局样式 */
    .home-fullwidth-container {
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        width: 100vw;
        background: transparent;
    }
</style>
{% endblock %}

{% block content %}
<!-- 全宽布局容器 -->
<div class="home-fullwidth-container">

<!-- 英雄区域 - 全屏宽度 -->
<section class="hero-section">
    <div class="floating-icon"><i class="bi bi-controller"></i></div>
    <div class="floating-icon"><i class="bi bi-trophy"></i></div>
    <div class="floating-icon"><i class="bi bi-gem"></i></div>
    <div class="floating-icon"><i class="bi bi-shield-check"></i></div>
    <div class="floating-icon"><i class="bi bi-star"></i></div>
    <div class="floating-icon"><i class="bi bi-heart"></i></div>
    
    <div class="container-fluid px-4">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-5 col-lg-6 hero-content">
                <h1 class="display-3 text-white fw-bold mb-4">
                    <span class="d-block">梦羽明日之后</span>
                    <small class="text-white-50 fs-4">专业游戏账号交易平台</small>
                </h1>
                <p class="lead text-white mb-4">
                    找《明日之后》游戏服务，上梦羽明日之后<br>
                    无论您是想回游、退坑，还是寻求代肝、出/收金或其它明日服务，我们满足您的一切需求
                </p>
                <div class="d-flex flex-wrap gap-3 mb-4">
                    <a class="btn btn-game btn-lg" href="{% url 'services:account_list' %}">
                        <i class="bi bi-search me-2"></i>浏览账号
                    </a>
                    {% if user.is_authenticated %}
                    <a class="btn btn-outline-light btn-lg" href="{% url 'services:sell_account' %}">
                        <i class="bi bi-plus-circle me-2"></i>出售账号
                    </a>
                    {% else %}
                    <a class="btn btn-outline-light btn-lg" href="{% url 'accounts:register' %}">
                        <i class="bi bi-person-plus me-2"></i>立即注册
                    </a>
                    {% endif %}
                </div>
                
                <!-- 统计数据 -->
                <div class="row text-white mt-5">
                    <div class="col-4 text-center">
                        <div class="stats-counter text-white">1000+</div>
                        <small class="text-white-50">账号交易</small>
                    </div>
                    <div class="col-4 text-center">
                        <div class="stats-counter text-white">500+</div>
                        <small class="text-white-50">活跃用户</small>
                    </div>
                    <div class="col-4 text-center">
                        <div class="stats-counter text-white">99%</div>
                        <small class="text-white-50">好评率</small>
                    </div>
                </div>
            </div>
            <div class="col-xl-5 col-lg-6">
                <div class="platform-features">
                    <div class="row g-4">
                        <div class="col-6">
                            <a href="/services/accounts/" class="feature-card text-decoration-none">
                                <div class="card h-100 border-0 shadow-sm bg-transparent text-white text-center p-4">
                                    <div class="card-body">
                                        <i class="bi bi-shield-check display-4 mb-3 text-success"></i>
                                        <h5 class="card-title">安全交易</h5>
                                        <p class="card-text small">平台担保，资金安全</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="/services/community/" class="feature-card text-decoration-none">
                                <div class="card h-100 border-0 shadow-sm bg-transparent text-white text-center p-4">
                                    <div class="card-body">
                                        <i class="bi bi-people display-4 mb-3 text-info"></i>
                                        <h5 class="card-title">社区交流</h5>
                                        <p class="card-text small">玩家互动，经验分享</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="/services/wishlist/" class="feature-card text-decoration-none">
                                <div class="card h-100 border-0 shadow-sm bg-transparent text-white text-center p-4">
                                    <div class="card-body">
                                        <i class="bi bi-heart display-4 mb-3 text-danger"></i>
                                        <h5 class="card-title">心愿工坊</h5>
                                        <p class="card-text small">定制服务，满足需求</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="/services/guides/" class="feature-card text-decoration-none">
                                <div class="card h-100 border-0 shadow-sm bg-transparent text-white text-center p-4">
                                    <div class="card-body">
                                        <i class="bi bi-book display-4 mb-3 text-warning"></i>
                                        <h5 class="card-title">攻略专区</h5>
                                        <p class="card-text small">游戏攻略，新手指南</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 游戏介绍 -->
<section class="py-5 game-intro-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,0.05) 10px, rgba(255,255,255,0.05) 20px); opacity: 0.3;"></div>

    <div class="container-fluid px-4 position-relative">
        <div class="row align-items-center justify-content-center">
            <!-- 左侧内容 -->
            <div class="col-xl-5 col-lg-6 mb-5 mb-lg-0">
                <div class="text-white">
                    <!-- 标题区域 -->
                    <div class="mb-5 text-center text-lg-start">
                        <h2 class="display-5 fw-bold mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                            关于《明日之后》
                        </h2>
                        <div class="d-inline-block bg-white" style="height: 3px; width: 60px; border-radius: 2px;"></div>
                    </div>

                    <!-- 特色功能卡片 -->
                    <div class="game-features">
                        <div class="feature-item mb-4 p-4 rounded-3" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s ease;">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon-modern me-4" style="width: 60px; height: 60px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(255,107,107,0.3);">
                                    <i class="bi bi-globe2 fs-4 text-white"></i>
                                </div>
                                <div>
                                    <h5 class="mb-2 fw-bold">末日生存世界</h5>
                                    <p class="mb-0 opacity-75">病毒席卷全球，在废土世界中求生</p>
                                    <small class="text-warning">🔥 沉浸式末日体验</small>
                                </div>
                            </div>
                        </div>

                        <div class="feature-item mb-4 p-4 rounded-3" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s ease;">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon-modern me-4" style="width: 60px; height: 60px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(78,205,196,0.3);">
                                    <i class="bi bi-house-heart fs-4 text-white"></i>
                                </div>
                                <div>
                                    <h5 class="mb-2 fw-bold">庄园建造</h5>
                                    <p class="mb-0 opacity-75">建造专属庄园，打造理想家园</p>
                                    <small class="text-info">🏠 自由建造系统</small>
                                </div>
                            </div>
                        </div>

                        <div class="feature-item mb-4 p-4 rounded-3" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s ease;">
                            <div class="d-flex align-items-center">
                                <div class="feature-icon-modern me-4" style="width: 60px; height: 60px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(168,237,234,0.3);">
                                    <i class="bi bi-people-fill fs-4 text-dark"></i>
                                </div>
                                <div>
                                    <h5 class="mb-2 fw-bold">多人协作</h5>
                                    <p class="mb-0 opacity-75">与好友组队探索，共同面对挑战</p>
                                    <small class="text-success">👥 团队合作模式</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部描述 -->
                    <div class="mt-5 p-4 rounded-3" style="background: rgba(0,0,0,0.2); border-left: 4px solid #ffd700;">
                        <p class="lead mb-0 fw-medium">
                            <i class="bi bi-star-fill text-warning me-2"></i>
                            无论您是生存达人还是收藏爱好者，这里都能找到适合您的游戏账号。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 右侧图片展示 -->
            <div class="col-xl-5 col-lg-6">
                <div class="position-relative">
                    <!-- 主要展示区域 -->
                    <div class="game-showcase">
                        <div class="row g-4">
                            <!-- 顶部两个小图 -->
                            <div class="col-6">
                                <div class="showcase-card position-relative overflow-hidden rounded-4 shadow-lg" style="height: 200px; background: linear-gradient(45deg, #667eea, #764ba2);">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white">
                                            <i class="bi bi-house-door display-4 mb-2"></i>
                                            <h6 class="fw-bold">庄园建造</h6>
                                            <small class="opacity-75">自由建造</small>
                                        </div>
                                    </div>
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50" style="background: linear-gradient(transparent, rgba(0,0,0,0.3));"></div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="showcase-card position-relative overflow-hidden rounded-4 shadow-lg" style="height: 200px; background: linear-gradient(45deg, #ff6b6b, #ee5a24);">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white">
                                            <i class="bi bi-shield-shaded display-4 mb-2"></i>
                                            <h6 class="fw-bold">武器装备</h6>
                                            <small class="opacity-75">战斗系统</small>
                                        </div>
                                    </div>
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50" style="background: linear-gradient(transparent, rgba(0,0,0,0.3));"></div>
                                </div>
                            </div>
                            <!-- 底部大图 -->
                            <div class="col-12">
                                <div class="showcase-card position-relative overflow-hidden rounded-4 shadow-lg" style="height: 250px; background: linear-gradient(45deg, #4ecdc4, #44a08d);">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white">
                                            <i class="bi bi-globe-americas display-3 mb-3"></i>
                                            <h5 class="fw-bold mb-2">末日世界探索</h5>
                                            <p class="mb-0 opacity-75">广阔的开放世界等你探索</p>
                                        </div>
                                    </div>
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50" style="background: linear-gradient(transparent, rgba(0,0,0,0.3));"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 装饰元素 -->
                    <div class="position-absolute top-0 end-0 translate-middle-y" style="width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); border-radius: 50%; z-index: -1;"></div>
                    <div class="position-absolute bottom-0 start-0 translate-middle-x" style="width: 80px; height: 80px; background: radial-gradient(circle, rgba(255,215,0,0.2) 0%, transparent 70%); border-radius: 50%; z-index: -1;"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 平台特色 -->
<section class="py-5 platform-features-section" style="position: relative; overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: repeating-linear-gradient(-45deg, transparent, transparent 15px, rgba(255,255,255,0.03) 15px, rgba(255,255,255,0.03) 30px); opacity: 0.5;"></div>

    <div class="container-fluid px-4 position-relative">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-white mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                平台特色
            </h2>
            <div class="d-inline-block bg-white mb-4" style="height: 3px; width: 80px; border-radius: 2px;"></div>
            <p class="lead text-white opacity-75 mb-0">专业的游戏交易平台，为您提供全方位服务</p>
        </div>

        <div class="row g-4 justify-content-center">
            <!-- 渠道服账号 -->
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="modern-feature-card h-100 p-4 rounded-4 text-white position-relative overflow-hidden" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.4s ease;">
                    <!-- 卡片装饰 -->
                    <div class="position-absolute top-0 end-0 w-50 h-50" style="background: radial-gradient(circle, rgba(255,107,107,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <div class="text-center">
                        <!-- 图标 -->
                        <div class="feature-icon-modern mx-auto mb-4" style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(255,107,107,0.3); transition: all 0.3s ease;">
                            <i class="bi bi-shield-check fs-2 text-white"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="h4 mb-3 fw-bold">渠道服账号</h3>
                        <p class="mb-4 opacity-75">提供iOS、安卓等多渠道服务器账号，满足不同玩家需求</p>

                        <!-- 特色列表 -->
                        <div class="feature-list mb-4">
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-white" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">iOS渠道服</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-white" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">安卓渠道服</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-white" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">跨服账号</span>
                            </div>
                        </div>

                        <!-- 按钮 -->
                        <a href="{% url 'services:account_list' %}" class="btn btn-outline-light btn-sm px-4 py-2 rounded-pill fw-medium" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                            浏览账号 <i class="bi bi-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 大神攻略 -->
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="modern-feature-card h-100 p-4 rounded-4 text-white position-relative overflow-hidden" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.4s ease;">
                    <!-- 卡片装饰 -->
                    <div class="position-absolute top-0 end-0 w-50 h-50" style="background: radial-gradient(circle, rgba(78,205,196,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <div class="text-center">
                        <!-- 图标 -->
                        <div class="feature-icon-modern mx-auto mb-4" style="width: 80px; height: 80px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(78,205,196,0.3); transition: all 0.3s ease;">
                            <i class="bi bi-journal-text fs-2 text-white"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="h4 mb-3 fw-bold">大神攻略</h3>
                        <p class="mb-4 opacity-75">七年老牌攻略博主提供的高质量游戏攻略和技巧分享</p>

                        <!-- 特色列表 -->
                        <div class="feature-list mb-4">
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-white" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">新手指南</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-white" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">进阶技巧</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-white" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">职业攻略</span>
                            </div>
                        </div>

                        <!-- 按钮 -->
                        <a href="{% url 'services:guide_list' %}" class="btn btn-outline-light btn-sm px-4 py-2 rounded-pill fw-medium" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                            查看攻略 <i class="bi bi-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 特色服务 -->
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="modern-feature-card h-100 p-4 rounded-4 text-white position-relative overflow-hidden" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.4s ease;">
                    <!-- 卡片装饰 -->
                    <div class="position-absolute top-0 end-0 w-50 h-50" style="background: radial-gradient(circle, rgba(255,215,0,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <div class="text-center">
                        <!-- 图标 -->
                        <div class="feature-icon-modern mx-auto mb-4" style="width: 80px; height: 80px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(255,215,0,0.3); transition: all 0.3s ease;">
                            <i class="bi bi-star-fill fs-2 text-dark"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="h4 mb-3 fw-bold">特色服务</h3>
                        <p class="mb-4 opacity-75">7年资深博主提供的专业游戏服务</p>

                        <!-- 特色列表 -->
                        <div class="feature-list mb-4">
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-dark" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">账号调整</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-dark" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">游戏答疑</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-dark" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">在线直播</span>
                            </div>
                        </div>

                        <!-- 下拉菜单按钮 -->
                        <div class="dropdown">
                            <button class="btn btn-outline-light btn-sm px-4 py-2 rounded-pill fw-medium dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                                选择服务
                            </button>
                            <ul class="dropdown-menu border-0 shadow-lg" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
                                <li><a class="dropdown-item py-2" href="{% url 'services:account_adjustment' %}" style="transition: all 0.2s ease;">
                                    <i class="bi bi-gear-fill me-2 text-primary"></i> 账号调整
                                </a></li>
                                <li><a class="dropdown-item py-2" href="{% url 'services:game_qa' %}" style="transition: all 0.2s ease;">
                                    <i class="bi bi-question-circle-fill me-2 text-success"></i> 游戏答疑
                                </a></li>
                                <li><a class="dropdown-item py-2" href="{% url 'services:live_stream' %}" style="transition: all 0.2s ease;">
                                    <i class="bi bi-broadcast me-2 text-danger"></i> 在线直播
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 玩家社区 -->
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="modern-feature-card h-100 p-4 rounded-4 text-white position-relative overflow-hidden" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.4s ease;">
                    <!-- 卡片装饰 -->
                    <div class="position-absolute top-0 end-0 w-50 h-50" style="background: radial-gradient(circle, rgba(168,237,234,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <div class="text-center">
                        <!-- 图标 -->
                        <div class="feature-icon-modern mx-auto mb-4" style="width: 80px; height: 80px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(168,237,234,0.3); transition: all 0.3s ease;">
                            <i class="bi bi-people-fill fs-2 text-dark"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="h4 mb-3 fw-bold">玩家社区</h3>
                        <p class="mb-4 opacity-75">参与游戏讨论，分享游戏经验，与其他玩家交流互动</p>

                        <!-- 特色列表 -->
                        <div class="feature-list mb-4">
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-dark" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">游戏攻略</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-dark" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">问题解答</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-start mb-2">
                                <div class="me-3" style="width: 20px; height: 20px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-check2 text-dark" style="font-size: 0.8rem;"></i>
                                </div>
                                <span class="small">经验分享</span>
                            </div>
                        </div>

                        <!-- 按钮 -->
                        <a href="{% url 'services:community' %}" class="btn btn-outline-light btn-sm px-4 py-2 rounded-pill fw-medium" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                            进入社区 <i class="bi bi-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 心愿工坊 -->
<section class="py-5 wishlist-section" style="position: relative; overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: repeating-linear-gradient(45deg, transparent, transparent 20px, rgba(255,255,255,0.05) 20px, rgba(255,255,255,0.05) 40px); opacity: 0.4;"></div>

    <div class="container-fluid px-4 position-relative">
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-11">
                <div class="position-relative">
                    <!-- 标题区域 -->
                    <div class="text-center mb-5">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-4" style="width: 100px; height: 100px; background: rgba(255,255,255,0.2); backdrop-filter: blur(10px); border: 2px solid rgba(255,255,255,0.3);">
                            <i class="bi bi-heart-fill fs-1 text-white"></i>
                        </div>
                        <h2 class="display-4 fw-bold text-white mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">心愿工坊</h2>
                        <div class="d-inline-block bg-white mb-4" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                        <p class="lead text-white opacity-75 mb-5">满足您的各类游戏需求，寻找您想要的服务或提供您的专业技能</p>
                    </div>

                    <!-- 服务卡片网格 -->
                    <div class="row g-4 mb-5 justify-content-center">
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <div class="wishlist-service-card p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                                <!-- 装饰元素 -->
                                <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                                <div class="service-icon mx-auto mb-3" style="width: 70px; height: 70px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(78,205,196,0.4); transition: all 0.3s ease;">
                                    <i class="bi bi-controller fs-3 text-white"></i>
                                </div>
                                <h5 class="mb-2 fw-bold">代肝服务</h5>
                                <p class="small opacity-75 mb-0">专业代练，快速升级</p>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <div class="wishlist-service-card p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                                <!-- 装饰元素 -->
                                <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                                <div class="service-icon mx-auto mb-3" style="width: 70px; height: 70px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(102,126,234,0.4); transition: all 0.3s ease;">
                                    <i class="bi bi-map fs-3 text-white"></i>
                                </div>
                                <h5 class="mb-2 fw-bold">跑图服务</h5>
                                <p class="small opacity-75 mb-0">地图探索，资源收集</p>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <div class="wishlist-service-card p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                                <!-- 装饰元素 -->
                                <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                                <div class="service-icon mx-auto mb-3" style="width: 70px; height: 70px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(255,215,0,0.4); transition: all 0.3s ease;">
                                    <i class="bi bi-trophy fs-3 text-dark"></i>
                                </div>
                                <h5 class="mb-2 fw-bold">PVP代打</h5>
                                <p class="small opacity-75 mb-0">竞技场代打，提升排名</p>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <div class="wishlist-service-card p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                                <!-- 装饰元素 -->
                                <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                                <div class="service-icon mx-auto mb-3" style="width: 70px; height: 70px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 25px rgba(168,237,234,0.4); transition: all 0.3s ease;">
                                    <i class="bi bi-coin fs-3 text-dark"></i>
                                </div>
                                <h5 class="mb-2 fw-bold">金条交易</h5>
                                <p class="small opacity-75 mb-0">安全金条买卖</p>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="text-center">
                        <div class="d-flex flex-wrap justify-content-center gap-4">
                            <a href="{% url 'services:wishlist_service' %}" class="btn btn-light btn-lg px-5 py-3 rounded-pill fw-bold shadow-lg" style="transition: all 0.3s ease; border: none;">
                                <i class="bi bi-plus-circle me-2 text-danger"></i>发布需求
                            </a>
                            <a href="{% url 'services:wishlist_provide' %}" class="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-bold" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.5);">
                                <i class="bi bi-briefcase me-2"></i>提供服务
                            </a>
                            <a href="{% url 'services:wishlist_list' %}" class="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-bold" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.5);">
                                <i class="bi bi-list me-2"></i>浏览全部
                            </a>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 热门区服 -->
<section class="py-5 popular-servers-section" style="position: relative; overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: repeating-linear-gradient(90deg, transparent, transparent 40px, rgba(255,255,255,0.02) 40px, rgba(255,255,255,0.02) 80px); opacity: 0.3;"></div>

    <div class="container-fluid px-4 position-relative">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-white mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                热门区服
            </h2>
            <div class="d-inline-block bg-white mb-4" style="height: 3px; width: 80px; border-radius: 2px;"></div>
            <p class="lead text-white opacity-75 mb-0">实时区服账号数量统计</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-xl-8 col-lg-10">
                <div class="modern-stats-card rounded-4 overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); box-shadow: 0 15px 35px rgba(0,0,0,0.2);">
                    <div class="card-header p-4" style="background: rgba(255,255,255,0.1); border-bottom: 1px solid rgba(255,255,255,0.2);">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 text-white fw-bold">
                                <i class="bi bi-bar-chart-fill me-2" style="color: #ffd700;"></i>
                                区服账号数量统计
                            </h5>
                            <span id="last-updated" class="small text-white opacity-75"></span>
                        </div>
                    </div>
                    <div class="card-body p-4" style="background: transparent;">
                        <div class="table-responsive">
                            <table class="table table-hover modern-table" style="background: transparent !important;">
                                <thead>
                                    <tr style="background: rgba(0,0,0,0.3) !important; border: none;">
                                        <th class="text-white fw-bold border-0 py-3" style="background: transparent !important;">排名</th>
                                        <th class="text-white fw-bold border-0 py-3" style="background: transparent !important;">区服名称</th>
                                        <th class="text-white fw-bold border-0 py-3" style="background: transparent !important;">账号数量</th>
                                        <th class="text-white fw-bold border-0 py-3" style="background: transparent !important;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="popular-servers">
                                    <tr style="background: rgba(0,0,0,0.4) !important;">
                                        <td colspan="4" class="text-center border-0 py-4" style="background: transparent !important; color: #ffffff !important;">
                                            <div class="spinner-border text-light" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <p class="text-white mt-2 mb-0">正在加载数据...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer p-4" style="background: rgba(255,255,255,0.1); border-top: 1px solid rgba(255,255,255,0.2);">
                        <div class="text-end">
                            <a href="{% url 'services:account_list' %}" class="btn btn-outline-light btn-sm px-4 py-2 rounded-pill fw-medium" style="transition: all 0.3s ease; border: 2px solid rgba(255,255,255,0.3);">
                                浏览全部账号 <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 交易流程 -->
<section class="py-5 trading-process-section" style="position: relative; overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: repeating-linear-gradient(135deg, transparent, transparent 25px, rgba(255,255,255,0.04) 25px, rgba(255,255,255,0.04) 50px); opacity: 0.6;"></div>

    <div class="container-fluid px-4 position-relative">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-white mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                安全交易流程
            </h2>
            <div class="d-inline-block bg-white mb-4" style="height: 3px; width: 100px; border-radius: 2px;"></div>
            <p class="lead text-white opacity-75 mb-0">7步安全交易，保障买卖双方权益</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-11">
                <!-- 流程步骤 -->
                <div class="row g-4 mb-5">
                    <!-- 步骤1 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 50%; box-shadow: 0 8px 25px rgba(255,107,107,0.4); transition: all 0.3s ease;">
                                1
                            </div>

                            <h5 class="mb-3 fw-bold">提交订单</h5>
                            <p class="small opacity-75 mb-3">选择心仪账号后提交订单，确认交易意向</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-cart-plus fs-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; box-shadow: 0 8px 25px rgba(102,126,234,0.4); transition: all 0.3s ease;">
                                2
                            </div>

                            <h5 class="mb-3 fw-bold">客服拉群</h5>
                            <p class="small opacity-75 mb-3">专业客服建立交易群聊，确保沟通顺畅</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-chat-dots fs-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤3 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; box-shadow: 0 8px 25px rgba(255,215,0,0.4); transition: all 0.3s ease;">
                                3
                            </div>

                            <h5 class="mb-3 fw-bold">买家验号</h5>
                            <p class="small opacity-75 mb-3">验证账号信息、角色属性、物品真实性</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-search fs-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤4 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #a8edea, #fed6e3); border-radius: 50%; box-shadow: 0 8px 25px rgba(168,237,234,0.4); transition: all 0.3s ease;">
                                <span class="text-dark">4</span>
                            </div>

                            <h5 class="mb-3 fw-bold">合同签署</h5>
                            <p class="small opacity-75 mb-3">电子合同确保交易双方权益，明确交易细则</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-file-earmark-text fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行流程 -->
                <div class="row g-4 justify-content-center mb-5">
                    <!-- 步骤5 -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff9a9e, #fecfef); border-radius: 50%; box-shadow: 0 8px 25px rgba(255,154,158,0.4); transition: all 0.3s ease;">
                                <span class="text-dark">5</span>
                            </div>

                            <h5 class="mb-3 fw-bold">买家付款</h5>
                            <p class="small opacity-75 mb-3">通过平台安全支付系统完成交易付款</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-credit-card fs-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤6 -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; box-shadow: 0 8px 25px rgba(102,126,234,0.4); transition: all 0.3s ease;">
                                6
                            </div>

                            <h5 class="mb-3 fw-bold">账号换绑</h5>
                            <p class="small opacity-75 mb-3">安全完成账号所有权转移，确认登录信息</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-arrow-left-right fs-2"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤7 -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="process-step-card h-100 p-4 rounded-4 text-white text-center position-relative overflow-hidden" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3); transition: all 0.4s ease;">
                            <!-- 装饰元素 -->
                            <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                            <!-- 步骤编号 -->
                            <div class="step-number mx-auto mb-3 d-flex align-items-center justify-content-center fw-bold fs-3" style="width: 80px; height: 80px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; box-shadow: 0 8px 25px rgba(78,205,196,0.4); transition: all 0.3s ease;">
                                7
                            </div>

                            <h5 class="mb-3 fw-bold">卖家收款</h5>
                            <p class="small opacity-75 mb-3">交易完成后卖家安全收款，平台结算佣金</p>

                            <!-- 图标 -->
                            <div class="process-icon" style="color: rgba(255,255,255,0.8);">
                                <i class="bi bi-check-circle fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="text-center">
                    <a href="{% url 'services:account_list' %}" class="btn btn-light btn-lg px-5 py-3 rounded-pill fw-bold shadow-lg" style="transition: all 0.3s ease; border: none;">
                        <i class="bi bi-rocket me-2 text-success"></i>立即开始交易
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 用户评价 -->
<section class="py-5 user-reviews-section" style="position: relative; overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="background: repeating-linear-gradient(60deg, transparent, transparent 30px, rgba(255,255,255,0.1) 30px, rgba(255,255,255,0.1) 60px); opacity: 0.3;"></div>

    <div class="container position-relative">
        <!-- 标题区域 -->
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-dark mb-3" style="text-shadow: 2px 2px 4px rgba(255,255,255,0.5);">
                用户评价
            </h2>
            <div class="d-inline-block bg-dark mb-4" style="height: 3px; width: 80px; border-radius: 2px;"></div>
            <p class="lead text-dark opacity-75 mb-0">听听用户的真实声音</p>
        </div>

        <div class="row g-4">
            <!-- 用户评价1 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="review-card h-100 p-4 rounded-4 position-relative overflow-hidden" style="background: rgba(255,255,255,0.8); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.5); transition: all 0.4s ease; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                    <!-- 装饰元素 -->
                    <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(102,126,234,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <!-- 用户信息 -->
                    <div class="d-flex align-items-center mb-4">
                        <div class="user-avatar me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; box-shadow: 0 4px 15px rgba(102,126,234,0.3);">
                            <i class="bi bi-person-fill fs-4 text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold text-dark">李先生</h5>
                            <div class="rating mb-1">
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                            </div>
                            <small class="text-muted">满级庄园玩家</small>
                        </div>
                    </div>

                    <!-- 评价内容 -->
                    <blockquote class="blockquote mb-0">
                        <p class="text-dark mb-0 fst-italic">"在这里买到了一个满级庄园账号，还带有很多限定家具，非常满意！客服服务也很专业。"</p>
                    </blockquote>

                    <!-- 引用图标 -->
                    <div class="position-absolute top-0 end-0 p-3">
                        <i class="bi bi-quote text-primary opacity-25" style="font-size: 2.5rem;"></i>
                    </div>
                </div>
            </div>

            <!-- 用户评价2 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="review-card h-100 p-4 rounded-4 position-relative overflow-hidden" style="background: rgba(255,255,255,0.8); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.5); transition: all 0.4s ease; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                    <!-- 装饰元素 -->
                    <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(78,205,196,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <!-- 用户信息 -->
                    <div class="d-flex align-items-center mb-4">
                        <div class="user-avatar me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; box-shadow: 0 4px 15px rgba(78,205,196,0.3);">
                            <i class="bi bi-person-fill fs-4 text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold text-dark">王女士</h5>
                            <div class="rating mb-1">
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-half text-warning"></i>
                            </div>
                            <small class="text-muted">账号出售者</small>
                        </div>
                    </div>

                    <!-- 评价内容 -->
                    <blockquote class="blockquote mb-0">
                        <p class="text-dark mb-0 fst-italic">"出售了我的明日之后账号，价格合理，交易过程中平台提供了很好的安全保障，推荐！"</p>
                    </blockquote>

                    <!-- 引用图标 -->
                    <div class="position-absolute top-0 end-0 p-3">
                        <i class="bi bi-quote text-success opacity-25" style="font-size: 2.5rem;"></i>
                    </div>
                </div>
            </div>

            <!-- 用户评价3 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="review-card h-100 p-4 rounded-4 position-relative overflow-hidden" style="background: rgba(255,255,255,0.8); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.5); transition: all 0.4s ease; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                    <!-- 装饰元素 -->
                    <div class="position-absolute top-0 end-0 w-25 h-25" style="background: radial-gradient(circle, rgba(255,215,0,0.2) 0%, transparent 70%); border-radius: 50%;"></div>

                    <!-- 用户信息 -->
                    <div class="d-flex align-items-center mb-4">
                        <div class="user-avatar me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; background: linear-gradient(45deg, #ffd700, #ffb347); border-radius: 50%; box-shadow: 0 4px 15px rgba(255,215,0,0.3);">
                            <i class="bi bi-person-fill fs-4 text-dark"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold text-dark">张先生</h5>
                            <div class="rating mb-1">
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star text-warning"></i>
                            </div>
                            <small class="text-muted">新手玩家</small>
                        </div>
                    </div>

                    <!-- 评价内容 -->
                    <blockquote class="blockquote mb-0">
                        <p class="text-dark mb-0 fst-italic">"第一次在网上买游戏账号，有点担心，但是这个平台的交易流程很规范，让人放心。"</p>
                    </blockquote>

                    <!-- 引用图标 -->
                    <div class="position-absolute top-0 end-0 p-3">
                        <i class="bi bi-quote text-info opacity-25" style="font-size: 2.5rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 恢复正常容器布局 -->
<main class="container py-4">
    <!-- 这里可以放置需要正常容器宽度的内容 -->

</div>
<!-- 结束全宽布局容器 -->

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载热门区服数据
        loadPopularServers();

        // 每60秒自动刷新一次
        setInterval(loadPopularServers, 60000);
    });

    function loadPopularServers() {
        fetch('/services/api/popular-servers/')
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('popular-servers');
                const lastUpdated = document.getElementById('last-updated');

                if (data.servers && data.servers.length > 0) {
                    tbody.innerHTML = '';
                    data.servers.forEach((server, index) => {
                        const row = document.createElement('tr');
                        row.style.background = 'rgba(0,0,0,0.4) !important';
                        row.innerHTML = `
                            <td style="background: transparent !important; color: #ffffff !important;">
                                <span class="badge ${index < 3 ? 'bg-warning' : 'bg-light text-dark'} rounded-pill">
                                    ${index + 1}
                                </span>
                            </td>
                            <td style="background: transparent !important; color: #ffffff !important;">
                                <strong class="text-white">${server.name}</strong>
                                ${server.is_hot ? '<span class="badge bg-danger ms-2">热门</span>' : ''}
                            </td>
                            <td style="background: transparent !important; color: #ffffff !important;">
                                <span class="text-warning fw-bold">${server.account_count}</span> <span class="text-white-50">个</span>
                            </td>
                            <td style="background: transparent !important; color: #ffffff !important;">
                                <a href="/accounts/?server=${encodeURIComponent(server.name)}"
                                   class="btn btn-outline-light btn-sm">
                                    查看账号
                                </a>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    // 更新时间
                    if (data.last_updated) {
                        lastUpdated.textContent = `更新时间: ${new Date(data.last_updated).toLocaleString()}`;
                    }
                } else {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center text-white-50">
                                <i class="bi bi-info-circle me-2"></i>暂无数据
                            </td>
                        </tr>
                    `;
                }
            })
            .catch(error => {
                console.error('加载热门区服数据失败:', error);
                const tbody = document.getElementById('popular-servers');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>加载失败，请稍后重试
                        </td>
                    </tr>
                `;
            });
    }

    // 统计数字动画效果
    function animateCounter(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 20);
    }

    // 当统计数字进入视口时开始动画
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.textContent.replace(/\D/g, ''));
                animateCounter(counter, target);
                observer.unobserve(counter);
            }
        });
    });

    // 观察所有统计数字
    document.querySelectorAll('.stats-counter').forEach(counter => {
        observer.observe(counter);
    });
</script>
{% endblock %}
